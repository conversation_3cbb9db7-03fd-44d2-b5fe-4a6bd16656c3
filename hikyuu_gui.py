#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu 量化交易策略开发图形界面
一个现代化的、用户友好的Hikyuu图形界面，专注于策略开发、回测和分析
"""

import sys
import os
import json
import traceback
from pathlib import Path
from datetime import datetime, timedelta

# 添加hikyuu路径
hikyuu_path = Path(__file__).parent / "hikyuu"
if hikyuu_path.exists():
    sys.path.insert(0, str(hikyuu_path.parent))

try:
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog, scrolledtext
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    from matplotlib.figure import Figure
    import pandas as pd
    import numpy as np
except ImportError as e:
    print(f"缺少必要的依赖包: {e}")
    print("请安装: pip install matplotlib pandas numpy")
    sys.exit(1)

try:
    from hikyuu.interactive import *
    print("✅ Hikyuu 加载成功")
except Exception as e:
    print(f"❌ Hikyuu 加载失败: {e}")
    print("请确保 hikyuu 已正确安装: pip install hikyuu")


class HikyuuGUI:
    """Hikyuu 图形界面主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Hikyuu 量化交易策略开发平台")
        self.root.geometry("1400x900")
        
        # 设置图标
        try:
            icon_path = hikyuu_path / "gui" / "hikyuu.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass
        
        # 初始化变量
        self.current_stock = None
        self.current_strategy = None
        self.backtest_result = None
        
        # 创建界面
        self.create_widgets()
        self.create_menu()
        
        # 加载配置
        self.load_config()
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建策略", command=self.new_strategy)
        file_menu.add_command(label="打开策略", command=self.open_strategy)
        file_menu.add_command(label="保存策略", command=self.save_strategy)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 数据菜单
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="数据", menu=data_menu)
        data_menu.add_command(label="数据管理", command=self.open_data_manager)
        data_menu.add_command(label="更新数据", command=self.update_data)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="策略向导", command=self.strategy_wizard)
        tools_menu.add_command(label="指标计算器", command=self.indicator_calculator)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建左侧面板（股票选择和策略编辑）
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        
        # 创建右侧面板（图表和结果）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 左侧面板内容
        self.create_left_panel(left_frame)
        
        # 右侧面板内容
        self.create_right_panel(right_frame)
        
    def create_left_panel(self, parent):
        """创建左侧面板"""
        # 股票选择区域
        stock_frame = ttk.LabelFrame(parent, text="股票选择", padding=10)
        stock_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(stock_frame, text="股票代码:").pack(anchor=tk.W)
        self.stock_var = tk.StringVar(value="sz000001")
        stock_entry = ttk.Entry(stock_frame, textvariable=self.stock_var, width=20)
        stock_entry.pack(fill=tk.X, pady=(5, 10))
        
        ttk.Button(stock_frame, text="加载股票", command=self.load_stock).pack(fill=tk.X)
        
        # 策略编辑区域
        strategy_frame = ttk.LabelFrame(parent, text="策略编辑", padding=10)
        strategy_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 策略模板选择
        ttk.Label(strategy_frame, text="策略模板:").pack(anchor=tk.W)
        self.template_var = tk.StringVar()
        template_combo = ttk.Combobox(strategy_frame, textvariable=self.template_var, 
                                    values=["双均线策略", "MACD策略", "RSI策略", "自定义策略"])
        template_combo.pack(fill=tk.X, pady=(5, 10))
        template_combo.bind("<<ComboboxSelected>>", self.load_template)
        
        # 策略代码编辑器
        ttk.Label(strategy_frame, text="策略代码:").pack(anchor=tk.W)
        self.code_text = scrolledtext.ScrolledText(strategy_frame, height=15, width=40)
        self.code_text.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        # 按钮区域
        button_frame = ttk.Frame(strategy_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="运行回测", command=self.run_backtest).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_code).pack(side=tk.LEFT)
        
    def create_right_panel(self, parent):
        """创建右侧面板"""
        # 创建选项卡
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # K线图选项卡
        chart_frame = ttk.Frame(notebook)
        notebook.add(chart_frame, text="K线图表")
        self.create_chart_panel(chart_frame)
        
        # 回测结果选项卡
        result_frame = ttk.Frame(notebook)
        notebook.add(result_frame, text="回测结果")
        self.create_result_panel(result_frame)
        
        # 日志选项卡
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="运行日志")
        self.create_log_panel(log_frame)
        
    def create_chart_panel(self, parent):
        """创建图表面板"""
        # 创建matplotlib图表
        self.fig = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        toolbar = NavigationToolbar2Tk(self.canvas, parent)
        toolbar.update()
        
    def create_result_panel(self, parent):
        """创建结果面板"""
        # 结果显示区域
        result_text = scrolledtext.ScrolledText(parent, height=20)
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.result_text = result_text
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        # 日志显示区域
        log_text = scrolledtext.ScrolledText(parent, height=20)
        log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.log_text = log_text
        
        # 重定向日志输出
        self.log("🚀 Hikyuu 量化交易平台启动成功")
        self.log("📖 请选择股票代码并编写策略进行回测")
        
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def load_stock(self):
        """加载股票数据"""
        try:
            stock_code = self.stock_var.get().strip()
            if not stock_code:
                messagebox.showwarning("警告", "请输入股票代码")
                return
                
            self.log(f"📈 正在加载股票: {stock_code}")
            
            # 获取股票对象
            self.current_stock = sm[stock_code]
            if not self.current_stock.valid:
                messagebox.showerror("错误", f"无效的股票代码: {stock_code}")
                return

            # 获取K线数据
            query = Query(-250)  # 最近250个交易日
            kdata = self.current_stock.get_kdata(query)
            
            if len(kdata) == 0:
                messagebox.showwarning("警告", f"股票 {stock_code} 没有数据")
                return
                
            # 绘制K线图
            self.plot_kline(kdata)
            
            self.log(f"✅ 股票 {stock_code} 加载成功，共 {len(kdata)} 条数据")
            
        except Exception as e:
            error_msg = f"加载股票失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
    def plot_kline(self, kdata):
        """绘制K线图"""
        try:
            self.fig.clear()
            
            # 创建子图
            ax1 = self.fig.add_subplot(211)
            ax2 = self.fig.add_subplot(212)
            
            # 准备数据
            dates = [k.datetime.date() for k in kdata]
            opens = [k.open for k in kdata]
            highs = [k.high for k in kdata]
            lows = [k.low for k in kdata]
            closes = [k.close for k in kdata]
            volumes = [k.volume for k in kdata]
            
            # 绘制K线图（简化版）
            for i in range(len(dates)):
                color = 'red' if closes[i] >= opens[i] else 'green'
                ax1.plot([i, i], [lows[i], highs[i]], color=color, linewidth=1)
                ax1.plot([i, i], [opens[i], closes[i]], color=color, linewidth=3)
            
            ax1.set_title(f"{self.current_stock.name} ({self.current_stock.market_code}) K线图")
            ax1.set_ylabel("价格")
            ax1.grid(True, alpha=0.3)
            
            # 绘制成交量
            ax2.bar(range(len(volumes)), volumes, alpha=0.7)
            ax2.set_title("成交量")
            ax2.set_ylabel("成交量")
            ax2.set_xlabel("交易日")
            ax2.grid(True, alpha=0.3)
            
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            self.log(f"❌ 绘制K线图失败: {str(e)}")
            
    def load_template(self, event=None):
        """加载策略模板"""
        template = self.template_var.get()

        templates = {
            "双均线策略": '''# 双均线策略示例
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 获取K线数据并创建均线指标
kdata = stock.get_kdata(Query(-250))
close_data = CLOSE(kdata)
ma5 = MA(close_data, n=5)   # 5日均线
ma20 = MA(close_data, n=20) # 20日均线

# 创建信号指示器（5日线上穿20日线买入，下穿卖出）
my_sg = SG_Cross(ma5, ma20)

# 资金管理（固定每次买入1000股）
my_mm = MM_FixedCount(1000)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
''',
            "MACD策略": '''# MACD策略示例
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 获取K线数据并创建MACD指标
kdata = stock.get_kdata(Query(-250))
close_data = CLOSE(kdata)
macd = MACD(close_data)

# 获取MACD的DIFF和DEA线
diff = macd.get_result(0)  # DIFF线
dea = macd.get_result(1)   # DEA线

# 创建信号指示器（DIFF上穿DEA买入，下穿卖出）
my_sg = SG_Cross(diff, dea)

# 资金管理
my_mm = MM_FixedCount(1000)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
''',
            "RSI策略": '''# RSI策略示例
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 获取K线数据并创建RSI指标
kdata = stock.get_kdata(Query(-250))
close_data = CLOSE(kdata)
rsi = RSI(close_data, n=14)

# 创建信号指示器（RSI<30买入，RSI>70卖出）
my_sg = SG_Band(rsi, 30, 70)

# 资金管理
my_mm = MM_FixedCount(1000)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
''',
            "自定义策略": '''# 自定义策略模板
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 在这里编写你的策略逻辑
# 例如：创建自定义信号指示器
# my_sg = ...

# 资金管理策略
my_mm = MM_FixedCount(1000)

# 创建交易系统
# sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
# sys.run(stock, Query(-250))
'''
        }

        if template in templates:
            self.code_text.delete(1.0, tk.END)
            self.code_text.insert(1.0, templates[template])
            self.log(f"📝 已加载 {template} 模板")

    def clear_code(self):
        """清空代码编辑器"""
        self.code_text.delete(1.0, tk.END)
        self.log("🗑️ 已清空代码编辑器")

    def run_backtest(self):
        """运行回测"""
        try:
            if not self.current_stock:
                messagebox.showwarning("警告", "请先加载股票数据")
                return

            code = self.code_text.get(1.0, tk.END).strip()
            if not code:
                messagebox.showwarning("警告", "请输入策略代码")
                return

            self.log("🚀 开始运行回测...")

            # 准备执行环境
            exec_globals = {
                'stock': self.current_stock,
                'sm': sm,
                'Query': Query,
                'crtTM': crtTM,
                'SG_Cross': SG_Cross,
                'SG_Band': SG_Band,
                'SYS_Simple': SYS_Simple,
                'MM_FixedCount': MM_FixedCount,
                'MA': MA,
                'EMA': EMA,
                'MACD': MACD,
                'RSI': RSI,
                'CLOSE': CLOSE,
                'OPEN': OPEN,
                'HIGH': HIGH,
                'LOW': LOW,
                'VOL': VOL,
                'get_kdata': get_kdata,
            }

            # 执行策略代码
            exec(code, exec_globals)

            # 获取系统对象
            if 'sys' in exec_globals:
                sys_obj = exec_globals['sys']
                self.backtest_result = sys_obj

                # 显示回测结果
                self.show_backtest_result(sys_obj)

                # 绘制资金曲线
                self.plot_performance(sys_obj)

                self.log("✅ 回测完成")
            else:
                self.log("⚠️ 未找到交易系统对象 'sys'")

        except Exception as e:
            error_msg = f"回测执行失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            traceback.print_exc()

    def show_backtest_result(self, sys_obj):
        """显示回测结果"""
        try:
            # 清空结果显示区域
            self.result_text.delete(1.0, tk.END)

            # 获取交易管理器
            tm = sys_obj.tm

            # 计算基本统计信息
            trade_list = tm.get_trade_list()
            position_list = tm.get_position_list()

            result_text = f"""
=== Hikyuu 回测结果报告 ===

📊 基本信息:
股票代码: {self.current_stock.market_code}
股票名称: {self.current_stock.name}
回测期间: {trade_list[0].datetime if len(trade_list) > 0 else 'N/A'} - {trade_list[-1].datetime if len(trade_list) > 0 else 'N/A'}

💰 资金情况:
初始资金: {tm.init_cash:.2f}
最终资金: {tm.current_cash:.2f}
总收益: {tm.current_cash - tm.init_cash:.2f}
收益率: {((tm.current_cash - tm.init_cash) / tm.init_cash * 100):.2f}%

📈 交易统计:
总交易次数: {len(trade_list)}
盈利交易: {len([t for t in trade_list if t.real_price * t.number > 0])}
亏损交易: {len([t for t in trade_list if t.real_price * t.number < 0])}

📋 持仓记录:
持仓次数: {len(position_list)}
"""

            # 显示最近的交易记录
            if len(trade_list) > 0:
                result_text += "\n🔄 最近交易记录:\n"
                for i, trade in enumerate(trade_list[-10:]):  # 显示最近10笔交易
                    action = "买入" if trade.business == 1 else "卖出"
                    result_text += f"{i+1}. {trade.datetime} {action} {trade.number}股 @{trade.real_price:.2f}\n"

            self.result_text.insert(1.0, result_text)

        except Exception as e:
            self.log(f"❌ 显示回测结果失败: {str(e)}")

    def plot_performance(self, sys_obj):
        """绘制策略表现图"""
        try:
            # 获取资金曲线
            tm = sys_obj.tm

            # 获取交易日期列表
            trade_list = tm.get_trade_list()
            if len(trade_list) == 0:
                self.log("⚠️ 没有交易记录，无法绘制资金曲线")
                return

            # 创建日期列表
            dates = DatetimeList()
            for trade in trade_list:
                dates.append(trade.datetime)

            # 获取资金曲线
            funds_curve = tm.get_funds_curve(dates)

            if len(funds_curve) == 0:
                self.log("⚠️ 没有资金曲线数据")
                return

            # 清空图表
            self.fig.clear()

            # 创建子图
            ax1 = self.fig.add_subplot(211)
            ax2 = self.fig.add_subplot(212)

            # 绘制资金曲线
            dates = [f.datetime.date() for f in funds_curve]
            values = [f.total for f in funds_curve]

            ax1.plot(range(len(dates)), values, 'b-', linewidth=2, label='资金曲线')
            ax1.set_title(f"{self.current_stock.name} 策略表现")
            ax1.set_ylabel("资金")
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 绘制收益率曲线
            if len(values) > 1:
                returns = [(values[i] - values[0]) / values[0] * 100 for i in range(len(values))]
                ax2.plot(range(len(dates)), returns, 'g-', linewidth=2, label='收益率%')
                ax2.set_title("收益率曲线")
                ax2.set_ylabel("收益率 (%)")
                ax2.set_xlabel("交易日")
                ax2.grid(True, alpha=0.3)
                ax2.legend()

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            self.log(f"❌ 绘制策略表现图失败: {str(e)}")

    def new_strategy(self):
        """新建策略"""
        self.code_text.delete(1.0, tk.END)
        self.template_var.set("")
        self.log("📄 新建策略")

    def open_strategy(self):
        """打开策略文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="打开策略文件",
                filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
            )
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.code_text.delete(1.0, tk.END)
                self.code_text.insert(1.0, content)
                self.log(f"📂 已打开策略文件: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"打开文件失败: {str(e)}")

    def save_strategy(self):
        """保存策略文件"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存策略文件",
                defaultextension=".py",
                filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
            )
            if file_path:
                content = self.code_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log(f"💾 已保存策略文件: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {str(e)}")

    def open_data_manager(self):
        """打开数据管理器"""
        try:
            # 启动Hikyuu的数据管理工具
            import subprocess
            subprocess.Popen([sys.executable, "-m", "hikyuu.gui.HikyuuTDX"])
            self.log("🔧 已启动数据管理工具")
        except Exception as e:
            messagebox.showerror("错误", f"启动数据管理工具失败: {str(e)}")

    def update_data(self):
        """更新数据"""
        self.log("📡 数据更新功能开发中...")
        messagebox.showinfo("提示", "数据更新功能开发中，请使用数据管理工具")

    def strategy_wizard(self):
        """策略向导"""
        wizard_window = tk.Toplevel(self.root)
        wizard_window.title("策略向导")
        wizard_window.geometry("600x400")
        wizard_window.transient(self.root)
        wizard_window.grab_set()

        # 向导内容
        ttk.Label(wizard_window, text="策略向导", font=("Arial", 16, "bold")).pack(pady=20)

        # 策略类型选择
        ttk.Label(wizard_window, text="选择策略类型:").pack(anchor=tk.W, padx=20)
        strategy_type = tk.StringVar(value="趋势跟踪")
        types = ["趋势跟踪", "均值回归", "动量策略", "套利策略"]
        for t in types:
            ttk.Radiobutton(wizard_window, text=t, variable=strategy_type, value=t).pack(anchor=tk.W, padx=40)

        # 参数设置
        params_frame = ttk.LabelFrame(wizard_window, text="参数设置", padding=10)
        params_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Label(params_frame, text="初始资金:").grid(row=0, column=0, sticky=tk.W)
        init_cash = tk.StringVar(value="100000")
        ttk.Entry(params_frame, textvariable=init_cash).grid(row=0, column=1, padx=10)

        ttk.Label(params_frame, text="交易数量:").grid(row=1, column=0, sticky=tk.W)
        trade_count = tk.StringVar(value="1000")
        ttk.Entry(params_frame, textvariable=trade_count).grid(row=1, column=1, padx=10)

        def generate_strategy():
            # 生成策略代码
            strategy_code = f'''# {strategy_type.get()}策略
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash={init_cash.get()})

# 创建信号指示器
my_sg = SG_Cross(MA(n=5), MA(n=20))  # 示例：双均线交叉

# 资金管理
my_mm = MM_FixedCount({trade_count.get()})

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
'''
            self.code_text.delete(1.0, tk.END)
            self.code_text.insert(1.0, strategy_code)
            wizard_window.destroy()
            self.log(f"🧙‍♂️ 已生成 {strategy_type.get()} 策略")

        ttk.Button(wizard_window, text="生成策略", command=generate_strategy).pack(pady=20)

    def indicator_calculator(self):
        """指标计算器"""
        calc_window = tk.Toplevel(self.root)
        calc_window.title("指标计算器")
        calc_window.geometry("500x300")
        calc_window.transient(self.root)
        calc_window.grab_set()

        ttk.Label(calc_window, text="指标计算器", font=("Arial", 16, "bold")).pack(pady=20)
        ttk.Label(calc_window, text="功能开发中...").pack(pady=50)

    def show_help(self):
        """显示帮助"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        help_window.grab_set()

        help_text = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = """
Hikyuu 量化交易策略开发平台 - 使用说明

1. 股票选择
   - 在左侧"股票选择"区域输入股票代码（如：sz000001）
   - 点击"加载股票"按钮加载股票数据和K线图

2. 策略编辑
   - 选择策略模板或编写自定义策略
   - 在代码编辑器中编写Python策略代码
   - 使用Hikyuu的API进行策略开发

3. 回测运行
   - 点击"运行回测"按钮执行策略
   - 在"回测结果"选项卡查看详细结果
   - 在"K线图表"选项卡查看策略表现图

4. 文件操作
   - 文件菜单：新建、打开、保存策略文件
   - 数据菜单：管理和更新股票数据
   - 工具菜单：策略向导、指标计算器

5. 常用快捷键
   - Ctrl+N: 新建策略
   - Ctrl+O: 打开策略
   - Ctrl+S: 保存策略

6. 注意事项
   - 确保已安装Hikyuu: pip install hikyuu
   - 策略代码中必须包含名为'sys'的交易系统对象
   - 建议先使用策略模板，再进行自定义修改

更多信息请访问：https://hikyuu.org/
"""
        help_text.insert(1.0, help_content)
        help_text.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """
Hikyuu 量化交易策略开发平台

版本: 1.0.0
基于: Hikyuu 量化交易框架
开发: 便携化图形界面

特性:
• 可视化策略开发
• 实时回测分析
• 多种策略模板
• 图表展示功能
• 便携化设计

官网: https://hikyuu.org/
"""
        messagebox.showinfo("关于", about_text)

    def load_config(self):
        """加载配置"""
        # 设置matplotlib中文字体
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
        except:
            pass

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 创建并运行GUI
        app = HikyuuGUI()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
