#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu GUI 功能测试脚本
测试所有策略模板和核心功能
"""

import sys
from pathlib import Path

# 添加hikyuu路径
hikyuu_path = Path(__file__).parent / "hikyuu"
if hikyuu_path.exists():
    sys.path.insert(0, str(hikyuu_path))

try:
    from hikyuu.interactive import *
    print("✅ Hikyuu 导入成功")
except ImportError as e:
    print(f"❌ Hikyuu 导入失败: {e}")
    sys.exit(1)

def test_basic_api():
    """测试基本API功能"""
    print("\n🔍 测试基本API...")
    
    # 测试股票获取
    try:
        stock = sm['sz000001']
        print(f"✅ 股票获取成功: {stock.name} ({stock.market_code}{stock.code})")
    except Exception as e:
        print(f"❌ 股票获取失败: {e}")
        return False
    
    # 测试K线数据
    try:
        kdata = stock.get_kdata(Query(-10))
        print(f"✅ K线数据获取成功: {len(kdata)} 条记录")
        if len(kdata) > 0:
            k = kdata[0]
            print(f"   示例数据: {k.datetime} 开:{k.open} 高:{k.high} 低:{k.low} 收:{k.close}")
    except Exception as e:
        print(f"❌ K线数据获取失败: {e}")
        return False
    
    return True

def test_double_ma_strategy():
    """测试双均线策略"""
    print("\n📈 测试双均线策略...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建均线指标
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        ma5 = MA(close_data, n=5)   # 5日均线
        ma20 = MA(close_data, n=20) # 20日均线
        
        # 创建信号指示器
        my_sg = SG_Cross(ma5, ma20)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        
        # 获取结果
        initial_cash = my_tm.init_cash
        final_cash = my_tm.current_cash
        profit = final_cash - initial_cash
        
        print(f"✅ 双均线策略测试成功")
        print(f"   初始资金: {initial_cash}")
        print(f"   最终资金: {final_cash}")
        print(f"   收益: {profit:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 双均线策略测试失败: {e}")
        return False

def test_macd_strategy():
    """测试MACD策略"""
    print("\n📊 测试MACD策略...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建MACD指标
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        macd = MACD(close_data)
        
        # 获取MACD的DIFF和DEA线
        diff = macd.get_result(0)  # DIFF线
        dea = macd.get_result(1)   # DEA线
        
        # 创建信号指示器
        my_sg = SG_Cross(diff, dea)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        
        # 获取结果
        initial_cash = my_tm.init_cash
        final_cash = my_tm.current_cash
        profit = final_cash - initial_cash
        
        print(f"✅ MACD策略测试成功")
        print(f"   初始资金: {initial_cash}")
        print(f"   最终资金: {final_cash}")
        print(f"   收益: {profit:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ MACD策略测试失败: {e}")
        return False

def test_rsi_strategy():
    """测试RSI策略"""
    print("\n📉 测试RSI策略...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建RSI指标
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        rsi = RSI(close_data, n=14)
        
        # 创建信号指示器
        my_sg = SG_Band(rsi, 30, 70)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        
        # 获取结果
        initial_cash = my_tm.init_cash
        final_cash = my_tm.current_cash
        profit = final_cash - initial_cash
        
        print(f"✅ RSI策略测试成功")
        print(f"   初始资金: {initial_cash}")
        print(f"   最终资金: {final_cash}")
        print(f"   收益: {profit:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ RSI策略测试失败: {e}")
        return False

def test_indicators():
    """测试技术指标"""
    print("\n🔧 测试技术指标...")
    
    try:
        stock = sm['sz000001']
        kdata = stock.get_kdata(Query(-100))
        close_data = CLOSE(kdata)
        
        # 测试各种指标
        indicators = {
            "MA(5)": MA(close_data, n=5),
            "MA(20)": MA(close_data, n=20),
            "MACD": MACD(close_data),
            "RSI(14)": RSI(close_data, n=14),
        }
        
        for name, indicator in indicators.items():
            if len(indicator) > 0:
                print(f"✅ {name}: {len(indicator)} 个数据点")
            else:
                print(f"❌ {name}: 无数据")
                
        return True
        
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("  🧪 Hikyuu GUI 功能全面测试")
    print("=" * 60)
    
    tests = [
        ("基本API", test_basic_api),
        ("双均线策略", test_double_ma_strategy),
        ("MACD策略", test_macd_strategy),
        ("RSI策略", test_rsi_strategy),
        ("技术指标", test_indicators),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI功能完全正常！")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，需要检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
