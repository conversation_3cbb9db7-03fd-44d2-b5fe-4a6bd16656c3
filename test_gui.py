#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu GUI 测试脚本
用于测试图形界面的基本功能
"""

import sys
import os
from pathlib import Path

def test_dependencies():
    """测试依赖包"""
    print("🔍 测试依赖包...")
    
    try:
        import tkinter
        print("✅ tkinter - OK")
    except ImportError:
        print("❌ tkinter - 缺失")
        return False
    
    try:
        import matplotlib
        print("✅ matplotlib - OK")
    except ImportError:
        print("❌ matplotlib - 缺失")
        return False
    
    try:
        import pandas
        print("✅ pandas - OK")
    except ImportError:
        print("❌ pandas - 缺失")
        return False
    
    try:
        import numpy
        print("✅ numpy - OK")
    except ImportError:
        print("❌ numpy - 缺失")
        return False
    
    return True

def test_hikyuu():
    """测试Hikyuu框架"""
    print("\n🔍 测试Hikyuu框架...")

    try:
        import hikyuu
        print("✅ hikyuu - OK")

        # 测试基本功能
        try:
            import hikyuu.interactive
            print("✅ hikyuu.interactive - OK")

            # 测试股票管理器
            try:
                from hikyuu.interactive import sm
                if len(sm) > 0:
                    print(f"✅ 股票数据 - OK (共{len(sm)}只股票)")
                else:
                    print("⚠️ 股票数据 - 空 (可能需要下载数据)")
            except Exception as e:
                print(f"⚠️ 股票管理器 - 警告: {e}")

        except ImportError as e:
            print(f"❌ hikyuu.interactive - 缺失: {e}")
            return False

        return True

    except ImportError as e:
        print(f"❌ hikyuu - 缺失: {e}")
        return False
    except Exception as e:
        print(f"⚠️ hikyuu - 警告: {e}")
        return True

def test_gui_files():
    """测试GUI文件"""
    print("\n🔍 测试GUI文件...")
    
    files_to_check = [
        "hikyuu_gui.py",
        "start_hikyuu_gui.bat",
        "start_hikyuu_gui.sh",
        "README_GUI.md"
    ]
    
    all_exist = True
    for file in files_to_check:
        if Path(file).exists():
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            all_exist = False
    
    return all_exist

def test_example_strategies():
    """测试示例策略"""
    print("\n🔍 测试示例策略...")
    
    strategy_dir = Path("example_strategies")
    if not strategy_dir.exists():
        print("❌ example_strategies 目录不存在")
        return False
    
    strategies = list(strategy_dir.glob("*.py"))
    if strategies:
        print(f"✅ 找到 {len(strategies)} 个示例策略:")
        for strategy in strategies:
            print(f"   - {strategy.name}")
        return True
    else:
        print("⚠️ 没有找到示例策略文件")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("  Hikyuu GUI 测试脚本")
    print("=" * 50)
    
    # 测试依赖包
    deps_ok = test_dependencies()
    
    # 测试Hikyuu
    hikyuu_ok = test_hikyuu()
    
    # 测试GUI文件
    files_ok = test_gui_files()
    
    # 测试示例策略
    examples_ok = test_example_strategies()
    
    # 总结
    print("\n" + "=" * 50)
    print("  测试结果总结")
    print("=" * 50)
    
    if deps_ok and hikyuu_ok and files_ok:
        print("🎉 所有测试通过！可以启动GUI")
        print("\n启动方式:")
        print("Windows: start_hikyuu_gui.bat")
        print("Linux/macOS: ./start_hikyuu_gui.sh")
        print("手动启动: python hikyuu_gui.py")
    else:
        print("❌ 部分测试失败，请检查以上错误")
        
        if not deps_ok:
            print("\n安装依赖包:")
            print("pip install matplotlib pandas numpy")
        
        if not hikyuu_ok:
            print("\n安装Hikyuu:")
            print("pip install hikyuu")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
