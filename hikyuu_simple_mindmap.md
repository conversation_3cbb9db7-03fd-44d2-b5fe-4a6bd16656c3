# 🚀 Hikyuu 2.6.5 量化交易框架思维导图

## 📊 框架总览

```
                    Hikyuu 2.6.5 量化交易框架
                            │
        ┌───────────────────┼───────────────────┐
        │                   │                   │
    数据管理层          技术指标层         交易系统核心
        │                   │                   │
        │                   │                   │
    风险控制层          高级策略层         工具扩展层
```

---

## 🗂️ 详细结构

### 1️⃣ 数据管理层
```
数据管理层
├── StockManager (股票管理器)
│   ├── sm['sz000001'] - 获取股票
│   ├── get_stock_list() - 股票列表
│   └── __len__() - 股票总数
│
├── Stock (股票对象)
│   ├── 属性: name, code, market, type
│   ├── get_kdata(Query) - 获取K线数据
│   └── get_finance_data() - 获取财务数据
│
└── KData (K线数据)
    ├── Query(-100) - 最近100天
    ├── KType.DAY/WEEK/MIN5 - K线类型
    └── CLOSE/HIGH/LOW/VOL() - 数据提取
```

### 2️⃣ 技术指标层
```
技术指标层
├── 趋势指标
│   ├── MA(data, n) - 移动平均
│   ├── EMA(data, n) - 指数平均
│   ├── MACD(data) - MACD指标
│   └── ADX(kdata, n) - 趋向指数
│
├── 震荡指标
│   ├── RSI(data, n) - 相对强弱
│   ├── KDJ(kdata) - 随机指标
│   ├── CCI(kdata, n) - 顺势指标
│   └── WR(kdata, n) - 威廉指标
│
├── 成交量指标
│   ├── OBV(kdata) - 能量潮
│   └── AD(kdata) - 聚散指标
│
└── 价格指标
    ├── BOLL(data, n, p) - 布林带
    ├── SAR(kdata) - 抛物线
    └── ATR(kdata, n) - 真实波幅
```

### 3️⃣ 交易系统核心
```
交易系统核心
├── 信号指示器 (SG)
│   ├── SG_Cross(fast, slow) - 双线交叉
│   ├── SG_Band(ind, low, high) - 区间突破
│   ├── SG_Bool(condition) - 布尔条件
│   └── 信号运算: & (AND), | (OR), + (加), - (减)
│
├── 交易管理器 (TM)
│   ├── crtTM(init_cash) - 创建账户
│   ├── buy(datetime, stock, price, number) - 买入
│   ├── sell(datetime, stock, price, number) - 卖出
│   ├── get_trade_list() - 交易记录
│   └── get_position_list() - 持仓记录
│
├── 资金管理器 (MM)
│   ├── MM_FixedCount(count) - 固定股数
│   ├── MM_FixedPercent(percent) - 固定比例
│   └── MM_FixedCash(cash) - 固定金额
│
└── 交易系统 (SYS)
    ├── SYS_Simple(tm, sg, mm) - 简单系统
    └── run(stock, query) - 运行回测
```

### 4️⃣ 风险控制层
```
风险控制层
├── 止损策略 (ST)
│   └── ST_FixedPercent(percent) - 固定百分比止损
│
├── 止盈策略 (TP)
│   └── TP_FixedPercent(percent) - 固定百分比止盈
│
├── 市场环境 (EV)
│   └── EV_Bool(condition) - 环境判断
│
├── 系统条件 (CN)
│   └── CN_Bool(condition) - 条件判断
│
└── 滑点算法 (SP)
    └── SP_FixedPercent(percent) - 滑点控制
```

### 5️⃣ 高级策略层
```
高级策略层
├── 多因子模型 (MF)
│   ├── MF_MultiFactor() - 多因子模型
│   └── add_factor(name, factor) - 添加因子
│
├── 选股器 (SE)
│   ├── SE_Fixed(stocks, sys) - 固定选股
│   └── SE_MultiFactor(mf, topn) - 多因子选股
│
├── 投资组合 (PF)
│   └── PF_Simple(tm, af, se) - 简单组合
│
└── 资金分配器 (AF)
    └── AF_EqualWeight() - 等权重分配
```

### 6️⃣ 工具扩展层
```
工具扩展层
├── 日期时间
│   ├── Datetime(year, month, day) - 日期对象
│   └── DatetimeList() - 日期列表
│
├── 数据转换
│   ├── hku_save(obj, file) - 保存对象
│   └── hku_load(file) - 加载对象
│
├── 系统配置
│   ├── load_hikyuu() - 初始化
│   └── get_global_context() - 全局上下文
│
└── 常量枚举
    ├── BUSINESS.BUY=1, SELL=2 - 业务类型
    └── SystemPart.SG, MM, ST - 系统部件
```

---

## 🎯 核心使用流程

```
1. 数据获取
   stock = sm['sz000001']
   kdata = stock.get_kdata(Query(-100))

2. 指标计算
   close = CLOSE(kdata)
   ma5 = MA(close, 5)
   ma20 = MA(close, 20)

3. 信号生成
   sg = SG_Cross(ma5, ma20)

4. 系统构建
   tm = crtTM(init_cash=100000)
   mm = MM_FixedCount(1000)
   sys = SYS_Simple(tm=tm, sg=sg, mm=mm)

5. 回测运行
   sys.run(stock, Query(-250))

6. 结果分析
   trades = tm.get_trade_list()
   收益率 = (tm.current_cash - tm.init_cash) / tm.init_cash * 100
```

---

## 💡 快速API参考

| 功能 | API | 说明 |
|------|-----|------|
| 获取股票 | `sm['sz000001']` | 通过代码获取股票对象 |
| K线数据 | `stock.get_kdata(Query(-100))` | 获取最近100天K线 |
| 价格序列 | `CLOSE(kdata)` | 提取收盘价序列 |
| 移动平均 | `MA(data, n=5)` | 计算n日移动平均 |
| 双线交叉 | `SG_Cross(fast, slow)` | 快慢线交叉信号 |
| 创建账户 | `crtTM(init_cash=100000)` | 创建交易管理器 |
| 固定股数 | `MM_FixedCount(1000)` | 每次买入1000股 |
| 简单系统 | `SYS_Simple(tm, sg, mm)` | 组合交易系统 |
| 运行回测 | `sys.run(stock, query)` | 执行策略回测 |

---

## 🔥 重点提示

- **核心组件**: TM(交易管理) + SG(信号) + MM(资金管理) = 完整策略
- **数据流向**: 股票数据 → 技术指标 → 交易信号 → 系统执行 → 结果分析
- **扩展能力**: 支持多因子、选股、组合等高级功能
- **风险控制**: 内置止损止盈、环境判断等风控机制
