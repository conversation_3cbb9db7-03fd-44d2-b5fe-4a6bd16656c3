#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu实用学习：专注于可用功能的实战教程
"""

from hikyuu.interactive import *

def lesson_basic_strategy_analysis():
    """基础策略深度分析"""
    print("=" * 60)
    print("  📊 基础策略深度分析")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 200
    
    print(f"分析股票: {stock.name}")
    print(f"分析周期: {days} 天")
    
    # 获取数据
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    
    # 策略1: 短期双均线
    tm1 = crtTM(init_cash=init_cash)
    sg1 = SG_Cross(MA(close_data, 5), MA(close_data, 10))
    mm1 = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg1, mm=mm1)
    sys1.run(stock, Query(-days))
    
    # 策略2: 中期双均线
    tm2 = crtTM(init_cash=init_cash)
    sg2 = SG_Cross(MA(close_data, 10), MA(close_data, 30))
    mm2 = MM_FixedCount(1000)
    sys2 = SYS_Simple(tm=tm2, sg=sg2, mm=mm2)
    sys2.run(stock, Query(-days))
    
    # 策略3: 长期双均线
    tm3 = crtTM(init_cash=init_cash)
    sg3 = SG_Cross(MA(close_data, 20), MA(close_data, 60))
    mm3 = MM_FixedCount(1000)
    sys3 = SYS_Simple(tm=tm3, sg=sg3, mm=mm3)
    sys3.run(stock, Query(-days))
    
    # 分析结果
    strategies = [
        ("短期(5,10)", tm1, len(tm1.get_trade_list())),
        ("中期(10,30)", tm2, len(tm2.get_trade_list())),
        ("长期(20,60)", tm3, len(tm3.get_trade_list()))
    ]
    
    print(f"\n策略对比分析:")
    print(f"{'策略':<12} {'收益率':<8} {'交易次数':<8} {'胜率估算'}")
    print("-" * 40)
    
    for name, tm, trade_count in strategies:
        ret = ((tm.current_cash - init_cash) / init_cash) * 100
        win_rate = "N/A" if trade_count <= 2 else f"{min(70, max(30, 50 + ret/2)):.0f}%"
        print(f"{name:<12} {ret:>6.2f}% {trade_count:>6d}次 {win_rate:>8}")
    
    # 找出最佳策略
    best_tm = max([tm1, tm2, tm3], key=lambda x: x.current_cash)
    best_ret = ((best_tm.current_cash - init_cash) / init_cash) * 100
    
    print(f"\n🏆 最佳策略收益率: {best_ret:.2f}%")
    
    return strategies

def lesson_rsi_strategy_deep():
    """RSI策略深度应用"""
    print("\n" + "=" * 60)
    print("  📈 RSI策略深度应用")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 150
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    
    # 不同RSI参数测试
    rsi_params = [
        (14, 30, 70, "标准RSI"),
        (14, 20, 80, "严格RSI"),
        (21, 30, 70, "长周期RSI"),
        (7, 30, 70, "短周期RSI")
    ]
    
    results = []
    
    print(f"RSI策略参数测试:")
    print(f"{'策略':<15} {'收益率':<8} {'交易次数'}")
    print("-" * 35)
    
    for period, low, high, name in rsi_params:
        tm = crtTM(init_cash=init_cash)
        rsi = RSI(close_data, period)
        sg = SG_Band(rsi, low, high)
        mm = MM_FixedCount(1000)
        sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
        sys.run(stock, Query(-days))
        
        ret = ((tm.current_cash - init_cash) / init_cash) * 100
        trades = len(tm.get_trade_list())
        results.append((name, ret, trades))
        
        print(f"{name:<15} {ret:>6.2f}% {trades:>6d}次")
    
    best = max(results, key=lambda x: x[1])
    print(f"\n🏆 最佳RSI策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return results

def lesson_money_management_deep():
    """资金管理深度研究"""
    print("\n" + "=" * 60)
    print("  💰 资金管理深度研究")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 120
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    
    # 不同资金管理策略
    mm_strategies = [
        (MM_FixedCount(500), "保守(500股)"),
        (MM_FixedCount(1000), "标准(1000股)"),
        (MM_FixedCount(1500), "积极(1500股)"),
        (MM_FixedCount(2000), "激进(2000股)")
    ]
    
    results = []
    
    print(f"资金管理策略测试:")
    print(f"{'策略':<15} {'收益率':<8} {'最大持仓'}")
    print("-" * 35)
    
    for mm, name in mm_strategies:
        tm = crtTM(init_cash=init_cash)
        sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
        sys.run(stock, Query(-days))
        
        ret = ((tm.current_cash - init_cash) / init_cash) * 100
        positions = tm.get_position_list()
        max_position = len(positions)
        
        results.append((name, ret, max_position))
        print(f"{name:<15} {ret:>6.2f}% {max_position:>6d}")
    
    best = max(results, key=lambda x: x[1])
    print(f"\n🏆 最佳资金管理: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return results

def lesson_multi_indicator_combination():
    """多指标组合策略"""
    print("\n" + "=" * 60)
    print("  🔧 多指标组合策略")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 180
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    
    # 计算各种指标
    ma5 = MA(close_data, 5)
    ma20 = MA(close_data, 20)
    rsi = RSI(close_data, 14)
    
    strategies = []
    
    # 1. 单一MA策略
    tm1 = crtTM(init_cash=init_cash)
    sg1 = SG_Cross(ma5, ma20)
    mm = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg1, mm=mm)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("MA策略", ret1))
    
    # 2. 单一RSI策略
    tm2 = crtTM(init_cash=init_cash)
    sg2 = SG_Band(rsi, 30, 70)
    sys2 = SYS_Simple(tm=tm2, sg=sg2, mm=mm)
    sys2.run(stock, Query(-days))
    ret2 = ((tm2.current_cash - init_cash) / init_cash) * 100
    strategies.append(("RSI策略", ret2))
    
    # 3. MA + RSI 组合策略 (AND)
    tm3 = crtTM(init_cash=init_cash)
    sg3 = SG_Cross(ma5, ma20) & SG_Band(rsi, 30, 70)
    sys3 = SYS_Simple(tm=tm3, sg=sg3, mm=mm)
    sys3.run(stock, Query(-days))
    ret3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    strategies.append(("MA+RSI(AND)", ret3))
    
    # 4. MA + RSI 组合策略 (OR)
    tm4 = crtTM(init_cash=init_cash)
    sg4 = SG_Cross(ma5, ma20) | SG_Band(rsi, 30, 70)
    sys4 = SYS_Simple(tm=tm4, sg=sg4, mm=mm)
    sys4.run(stock, Query(-days))
    ret4 = ((tm4.current_cash - init_cash) / init_cash) * 100
    strategies.append(("MA+RSI(OR)", ret4))
    
    print(f"多指标组合策略测试:")
    print(f"{'策略':<15} {'收益率'}")
    print("-" * 25)
    
    for name, ret in strategies:
        print(f"{name:<15} {ret:>6.2f}%")
    
    best = max(strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳组合策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return strategies

def lesson_parameter_optimization():
    """参数优化实战"""
    print("\n" + "=" * 60)
    print("  ⚡ 参数优化实战")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 150
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    
    print("正在测试不同参数组合...")
    
    best_return = float('-inf')
    best_params = None
    test_count = 0
    
    # 优化MA参数
    for fast in [3, 5, 8, 10]:
        for slow in [15, 20, 25, 30]:
            if fast >= slow:
                continue
            
            try:
                tm = crtTM(init_cash=init_cash)
                sg = SG_Cross(MA(close_data, fast), MA(close_data, slow))
                mm = MM_FixedCount(1000)
                sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
                sys.run(stock, Query(-days))
                
                ret = ((tm.current_cash - init_cash) / init_cash) * 100
                test_count += 1
                
                if ret > best_return:
                    best_return = ret
                    best_params = (fast, slow)
                    
            except:
                continue
    
    print(f"测试了 {test_count} 种参数组合")
    
    if best_params:
        print(f"🏆 最优参数: MA({best_params[0]}, {best_params[1]})")
        print(f"🏆 最优收益率: {best_return:.2f}%")
        
        # 用最优参数重新测试
        tm_best = crtTM(init_cash=init_cash)
        sg_best = SG_Cross(MA(close_data, best_params[0]), MA(close_data, best_params[1]))
        mm_best = MM_FixedCount(1000)
        sys_best = SYS_Simple(tm=tm_best, sg=sg_best, mm=mm_best)
        sys_best.run(stock, Query(-days))
        
        trades = tm_best.get_trade_list()
        print(f"🏆 最优策略交易次数: {len(trades)}")
        
        return best_params, best_return
    else:
        print("❌ 未找到有效参数")
        return None, 0

def lesson_practical_tips():
    """实用技巧总结"""
    print("\n" + "=" * 60)
    print("  💡 实用技巧总结")
    print("=" * 60)
    
    print("📚 Hikyuu核心概念回顾:")
    print("  • TM (TradeManager): 管理资金和交易记录")
    print("  • SG (Signal): 产生买卖信号")
    print("  • MM (MoneyManager): 控制每次交易的资金量")
    print("  • SYS (System): 组合所有组件的交易系统")
    
    print("\n🎯 策略开发流程:")
    print("  1. 数据获取: stock.get_kdata(Query(-days))")
    print("  2. 指标计算: MA, RSI, MACD等")
    print("  3. 信号生成: SG_Cross, SG_Band等")
    print("  4. 系统构建: SYS_Simple组合所有组件")
    print("  5. 回测运行: sys.run(stock, query)")
    print("  6. 结果分析: 收益率、交易次数等")
    
    print("\n⚠️ 常见陷阱:")
    print("  • 过度拟合: 参数过度优化导致实盘失效")
    print("  • 未来函数: 使用了未来数据")
    print("  • 数据偏差: 只在特定时期有效")
    print("  • 交易成本: 忽略手续费和滑点")
    
    print("\n✅ 最佳实践:")
    print("  • 样本外验证: 用不同时期数据验证")
    print("  • 参数稳健性: 测试参数变化的影响")
    print("  • 风险控制: 设置止损和仓位管理")
    print("  • 多样化: 不要依赖单一策略")
    
    print("\n🔧 调试技巧:")
    print("  • 打印中间结果: print(指标值)")
    print("  • 检查数据质量: len(kdata), 数据范围")
    print("  • 逐步构建: 先简单后复杂")
    print("  • 对比基准: 与买入持有策略比较")

def main():
    """实用学习主流程"""
    print("🚀 Hikyuu实用学习教程")
    print("专注于可用功能的深度实战")
    print("=" * 60)
    
    # 基础策略分析
    lesson_basic_strategy_analysis()
    
    # RSI策略深度应用
    lesson_rsi_strategy_deep()
    
    # 资金管理深度研究
    lesson_money_management_deep()
    
    # 多指标组合策略
    lesson_multi_indicator_combination()
    
    # 参数优化实战
    best_params, best_return = lesson_parameter_optimization()
    
    # 实用技巧总结
    lesson_practical_tips()
    
    # 最终总结
    print("\n" + "=" * 60)
    print("  🎓 实用学习完成")
    print("=" * 60)
    print("✅ 你已经掌握了:")
    print("  • Hikyuu的核心组件使用")
    print("  • 多种技术指标的应用")
    print("  • 策略组合和优化方法")
    print("  • 实战中的注意事项")
    
    if best_params:
        print(f"\n🏆 今日最佳发现:")
        print(f"  最优MA参数: ({best_params[0]}, {best_params[1]})")
        print(f"  最优收益率: {best_return:.2f}%")
    
    print("\n🎯 下一步建议:")
    print("  1. 在不同股票上测试策略")
    print("  2. 尝试更长的回测周期")
    print("  3. 研究基本面结合技术面")
    print("  4. 学习风险管理技术")
    print("  5. 考虑实盘模拟交易")
    
    print("\n💪 继续学习资源:")
    print("  • Hikyuu官方文档")
    print("  • 量化交易社区")
    print("  • 金融数据分析书籍")
    print("  • 实盘交易经验分享")
    
    print("=" * 60)
    print("🎉 恭喜完成Hikyuu实用学习！")
    print("现在你可以开始构建自己的量化交易策略了！")

if __name__ == "__main__":
    main()
