/*
 * build_in.h
 *
 *  Created on: 2013-4-19
 *      Author: fasiondog
 */

#pragma once
#ifndef SIGNAL_BUILD_IN_H_
#define SIGNAL_BUILD_IN_H_

#include "crt/SG_AllwaysBuy.h"
#include "crt/SG_Cross.h"
#include "crt/SG_CrossGold.h"
#include "crt/SG_Cycle.h"
#include "crt/SG_Flex.h"
#include "crt/SG_Single.h"
#include "crt/SG_Bool.h"
#include "crt/SG_Band.h"
#include "crt/SG_Logic.h"
#include "crt/SG_Manual.h"
#include "crt/SG_OneSide.h"

#endif /* SIGNAL_BUILD_IN_H_ */
