# RSI超买超卖策略示例
# 基于RSI指标的均值回归策略

from hikyuu.interactive import *

# 策略参数
INIT_CASH = 100000      # 初始资金
RSI_PERIOD = 14         # RSI周期
OVERSOLD = 30           # 超卖线
OVERBOUGHT = 70         # 超买线
TRADE_COUNT = 1000      # 每次交易股数

# 创建交易账户
my_tm = crtTM(init_cash=INIT_CASH)

# 创建RSI指标
rsi = RSI(n=RSI_PERIOD)

# 创建信号指示器
# 当RSI < 30时买入（超卖），RSI > 70时卖出（超买）
my_sg = SG_Band(rsi, OVERSOLD, OVERBOUGHT)

# 资金管理策略：固定股数
my_mm = MM_FixedCount(TRADE_COUNT)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测（最近250个交易日）
sys.run(stock, Query(-250))

# 打印基本统计信息
print(f"初始资金: {my_tm.initCash}")
print(f"最终资金: {my_tm.currentCash}")
print(f"总收益: {my_tm.currentCash - my_tm.initCash:.2f}")
print(f"收益率: {((my_tm.currentCash - my_tm.initCash) / my_tm.initCash * 100):.2f}%")
