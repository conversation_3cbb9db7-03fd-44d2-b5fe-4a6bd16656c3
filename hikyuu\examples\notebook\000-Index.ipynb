{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<img src=\"http://hikyuu.readthedocs.io/zh_CN/latest/_images/00000-title.png\" align=\"left\">"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Hikyuu Quant Framework是一款基于C++/Python的开源量化交易研究框架，用于策略分析及回测。其核心思想基于当前成熟的系统化交易方法，将整个系统化交易策略抽象为由市场环境判断策略、系统有效条件、信号指示器、止损/止盈策略、资金管理策略、盈利目标策略、移滑价差算法七大组件，你可以分别构建这些组件的策略资产库，在实际研究中对它们自由组合来观察系统的有效性、稳定性以及单一种类策略的效果。在系统策略之上，对交易对象选择、系统策略选择、资产组合资金分配进行了进一步封装，能够灵活支持更高层级的策略组合。\n", "\n", "更多信息，请参见：<https://hikyuu.org>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 入门篇\n", "\n", "* [001 交互式工具示例](001-overview.ipynb?flush_cache=True)\n", "* [002 获取股票对象](002-HowToGetStock.ipynb?flush_cache=True)\n", "* [003 获取并绘制K线数据](003-HowToGetKDataAndDraw.ipynb?flush_cache=True)\n", "* [004 计算并绘制技术指标](004-IndicatorOverview.ipynb?flush_cache=True)\n", "* [005 绘制组合图形](005-Drawplot.ipynb?flush_cache=True)\n", "* [006 TradeManager应用](006-TradeManager.ipynb?flush_cache=True)\n", "* [007 系统策略演示](007-SystemDetails.ipynb?flush_cache=True)\n", "* [008 序列化说明](008-Pickle.ipynb?flush_cache=True)\n", "* [009_获取实时日线数据](009-RealData.ipynb?flush_cache=True)\n", "* [010_资产组合](010-Portfolio.ipynb?flush_cache=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 示例\n", "\n", "* [Demo1](Demo/Demo1.ipynb?flush_cache=True)\n", "* [Demo2](Demo/Demo2.ipynb?flush_cache=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}