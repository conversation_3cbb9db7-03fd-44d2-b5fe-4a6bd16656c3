# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'MainWindow.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1178, 746)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.centralwidget)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.tabWidget = QtWidgets.QTabWidget(self.centralwidget)
        self.tabWidget.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        self.tabWidget.setObjectName("tabWidget")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.tab_4)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.groupBox_2 = QtWidgets.QGroupBox(self.tab_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_2.sizePolicy().hasHeightForWidth())
        self.groupBox_2.setSizePolicy(sizePolicy)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(10, 10, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pytdx_radioButton = QtWidgets.QRadioButton(self.groupBox_2)
        self.pytdx_radioButton.setEnabled(True)
        self.pytdx_radioButton.setObjectName("pytdx_radioButton")
        self.horizontalLayout.addWidget(self.pytdx_radioButton)
        self.label_16 = QtWidgets.QLabel(self.groupBox_2)
        self.label_16.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label_16.setObjectName("label_16")
        self.horizontalLayout.addWidget(self.label_16)
        self.use_tdx_number_spinBox = QtWidgets.QSpinBox(self.groupBox_2)
        self.use_tdx_number_spinBox.setMinimum(1)
        self.use_tdx_number_spinBox.setMaximum(20)
        self.use_tdx_number_spinBox.setProperty("value", 10)
        self.use_tdx_number_spinBox.setObjectName("use_tdx_number_spinBox")
        self.horizontalLayout.addWidget(self.use_tdx_number_spinBox)
        self.label_17 = QtWidgets.QLabel(self.groupBox_2)
        self.label_17.setObjectName("label_17")
        self.horizontalLayout.addWidget(self.label_17)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.verticalLayout_12.addLayout(self.horizontalLayout)
        spacerItem1 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_12.addItem(spacerItem1)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setContentsMargins(10, 0, -1, -1)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.tdx_radioButton = QtWidgets.QRadioButton(self.groupBox_2)
        self.tdx_radioButton.setObjectName("tdx_radioButton")
        self.horizontalLayout_5.addWidget(self.tdx_radioButton)
        self.verticalLayout_12.addLayout(self.horizontalLayout_5)
        self.gridLayout_2 = QtWidgets.QGridLayout()
        self.gridLayout_2.setContentsMargins(10, -1, -1, 10)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.select_tdx_dir_pushButton = QtWidgets.QPushButton(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.select_tdx_dir_pushButton.sizePolicy().hasHeightForWidth())
        self.select_tdx_dir_pushButton.setSizePolicy(sizePolicy)
        self.select_tdx_dir_pushButton.setObjectName("select_tdx_dir_pushButton")
        self.gridLayout_2.addWidget(self.select_tdx_dir_pushButton, 0, 3, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        self.label_2.setObjectName("label_2")
        self.gridLayout_2.addWidget(self.label_2, 0, 0, 1, 1)
        self.tdx_dir_lineEdit = QtWidgets.QLineEdit(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(1)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tdx_dir_lineEdit.sizePolicy().hasHeightForWidth())
        self.tdx_dir_lineEdit.setSizePolicy(sizePolicy)
        self.tdx_dir_lineEdit.setObjectName("tdx_dir_lineEdit")
        self.gridLayout_2.addWidget(self.tdx_dir_lineEdit, 0, 1, 1, 2)
        self.verticalLayout_12.addLayout(self.gridLayout_2)
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_24.setContentsMargins(10, 0, -1, -1)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.qmt_radioButton = QtWidgets.QRadioButton(self.groupBox_2)
        self.qmt_radioButton.setObjectName("qmt_radioButton")
        self.horizontalLayout_24.addWidget(self.qmt_radioButton)
        self.verticalLayout_12.addLayout(self.horizontalLayout_24)
        self.verticalLayout_13.addWidget(self.groupBox_2)
        spacerItem2 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_13.addItem(spacerItem2)
        self.groupBox_7 = QtWidgets.QGroupBox(self.tab_4)
        self.groupBox_7.setObjectName("groupBox_7")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.groupBox_7)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setContentsMargins(10, 10, -1, -1)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.import_weight_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_weight_checkBox.setObjectName("import_weight_checkBox")
        self.horizontalLayout_4.addWidget(self.import_weight_checkBox)
        self.import_history_finance_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_history_finance_checkBox.setObjectName("import_history_finance_checkBox")
        self.horizontalLayout_4.addWidget(self.import_history_finance_checkBox)
        self.import_block_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_block_checkBox.setObjectName("import_block_checkBox")
        self.horizontalLayout_4.addWidget(self.import_block_checkBox)
        self.verticalLayout_11.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setContentsMargins(10, -1, -1, -1)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.import_stock_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_stock_checkBox.setObjectName("import_stock_checkBox")
        self.horizontalLayout_3.addWidget(self.import_stock_checkBox)
        self.import_fund_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_fund_checkBox.setObjectName("import_fund_checkBox")
        self.horizontalLayout_3.addWidget(self.import_fund_checkBox)
        self.import_future_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_future_checkBox.setEnabled(False)
        self.import_future_checkBox.setObjectName("import_future_checkBox")
        self.horizontalLayout_3.addWidget(self.import_future_checkBox)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem3)
        self.verticalLayout_11.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(10, -1, -1, -1)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.import_day_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_day_checkBox.setObjectName("import_day_checkBox")
        self.horizontalLayout_2.addWidget(self.import_day_checkBox)
        self.import_min5_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_min5_checkBox.setObjectName("import_min5_checkBox")
        self.horizontalLayout_2.addWidget(self.import_min5_checkBox)
        self.import_min_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_min_checkBox.setObjectName("import_min_checkBox")
        self.horizontalLayout_2.addWidget(self.import_min_checkBox)
        self.import_trans_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_trans_checkBox.setEnabled(True)
        self.import_trans_checkBox.setObjectName("import_trans_checkBox")
        self.horizontalLayout_2.addWidget(self.import_trans_checkBox)
        self.import_time_checkBox = QtWidgets.QCheckBox(self.groupBox_7)
        self.import_time_checkBox.setEnabled(True)
        self.import_time_checkBox.setObjectName("import_time_checkBox")
        self.horizontalLayout_2.addWidget(self.import_time_checkBox)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.verticalLayout_11.addLayout(self.horizontalLayout_2)
        spacerItem5 = QtWidgets.QSpacerItem(20, 15, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_11.addItem(spacerItem5)
        self.line = QtWidgets.QFrame(self.groupBox_7)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout_11.addWidget(self.line)
        self.gridLayout_4 = QtWidgets.QGridLayout()
        self.gridLayout_4.setContentsMargins(10, 10, -1, -1)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_7 = QtWidgets.QLabel(self.groupBox_7)
        self.label_7.setObjectName("label_7")
        self.gridLayout_4.addWidget(self.label_7, 4, 0, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBox_7)
        self.label_6.setObjectName("label_6")
        self.gridLayout_4.addWidget(self.label_6, 3, 0, 1, 1)
        self.min5_start_dateEdit = QtWidgets.QDateEdit(self.groupBox_7)
        self.min5_start_dateEdit.setMinimumDateTime(QtCore.QDateTime(QtCore.QDate(1989, 12, 24), QtCore.QTime(16, 0, 0)))
        self.min5_start_dateEdit.setCalendarPopup(True)
        self.min5_start_dateEdit.setObjectName("min5_start_dateEdit")
        self.gridLayout_4.addWidget(self.min5_start_dateEdit, 1, 1, 1, 1)
        self.min_start_dateEdit = QtWidgets.QDateEdit(self.groupBox_7)
        self.min_start_dateEdit.setCalendarPopup(True)
        self.min_start_dateEdit.setObjectName("min_start_dateEdit")
        self.gridLayout_4.addWidget(self.min_start_dateEdit, 2, 1, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBox_7)
        self.label_3.setObjectName("label_3")
        self.gridLayout_4.addWidget(self.label_3, 0, 0, 1, 1)
        self.label_10 = QtWidgets.QLabel(self.groupBox_7)
        self.label_10.setObjectName("label_10")
        self.gridLayout_4.addWidget(self.label_10, 1, 0, 1, 1)
        self.label_15 = QtWidgets.QLabel(self.groupBox_7)
        self.label_15.setObjectName("label_15")
        self.gridLayout_4.addWidget(self.label_15, 2, 0, 1, 1)
        self.trans_start_dateEdit = QtWidgets.QDateEdit(self.groupBox_7)
        self.trans_start_dateEdit.setCalendarPopup(True)
        self.trans_start_dateEdit.setObjectName("trans_start_dateEdit")
        self.gridLayout_4.addWidget(self.trans_start_dateEdit, 3, 1, 1, 1)
        self.day_start_dateEdit = QtWidgets.QDateEdit(self.groupBox_7)
        self.day_start_dateEdit.setMinimumDateTime(QtCore.QDateTime(QtCore.QDate(1989, 12, 24), QtCore.QTime(16, 0, 0)))
        self.day_start_dateEdit.setCalendarPopup(True)
        self.day_start_dateEdit.setObjectName("day_start_dateEdit")
        self.gridLayout_4.addWidget(self.day_start_dateEdit, 0, 1, 1, 1)
        self.time_start_dateEdit = QtWidgets.QDateEdit(self.groupBox_7)
        self.time_start_dateEdit.setCalendarPopup(True)
        self.time_start_dateEdit.setObjectName("time_start_dateEdit")
        self.gridLayout_4.addWidget(self.time_start_dateEdit, 4, 1, 1, 1)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_4.addItem(spacerItem6, 1, 2, 1, 1)
        self.verticalLayout_11.addLayout(self.gridLayout_4)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_11.addItem(spacerItem7)
        self.verticalLayout_13.addWidget(self.groupBox_7)
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.tab_3)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.groupBox_4 = QtWidgets.QGroupBox(self.tab_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy)
        self.groupBox_4.setMinimumSize(QtCore.QSize(0, 80))
        self.groupBox_4.setObjectName("groupBox_4")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.groupBox_4)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_19.setContentsMargins(10, -1, -1, -1)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.enable_hdf55_radioButton = QtWidgets.QRadioButton(self.groupBox_4)
        self.enable_hdf55_radioButton.setObjectName("enable_hdf55_radioButton")
        self.horizontalLayout_19.addWidget(self.enable_hdf55_radioButton)
        self.enable_mysql_radioButton = QtWidgets.QRadioButton(self.groupBox_4)
        self.enable_mysql_radioButton.setObjectName("enable_mysql_radioButton")
        self.horizontalLayout_19.addWidget(self.enable_mysql_radioButton)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_19.addItem(spacerItem8)
        self.verticalLayout_14.addLayout(self.horizontalLayout_19)
        self.verticalLayout_9.addWidget(self.groupBox_4)
        spacerItem9 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_9.addItem(spacerItem9)
        self.groupBox_3 = QtWidgets.QGroupBox(self.tab_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_3.sizePolicy().hasHeightForWidth())
        self.groupBox_3.setSizePolicy(sizePolicy)
        self.groupBox_3.setMinimumSize(QtCore.QSize(0, 80))
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.gridLayout_3 = QtWidgets.QGridLayout()
        self.gridLayout_3.setContentsMargins(10, -1, -1, -1)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.hdf5_dir_lineEdit = QtWidgets.QLineEdit(self.groupBox_3)
        self.hdf5_dir_lineEdit.setObjectName("hdf5_dir_lineEdit")
        self.gridLayout_3.addWidget(self.hdf5_dir_lineEdit, 0, 1, 1, 1)
        self.hdf5_dir_pushButton = QtWidgets.QPushButton(self.groupBox_3)
        self.hdf5_dir_pushButton.setObjectName("hdf5_dir_pushButton")
        self.gridLayout_3.addWidget(self.hdf5_dir_pushButton, 0, 2, 1, 1)
        self.label = QtWidgets.QLabel(self.groupBox_3)
        self.label.setObjectName("label")
        self.gridLayout_3.addWidget(self.label, 0, 0, 1, 1)
        self.verticalLayout_10.addLayout(self.gridLayout_3)
        self.verticalLayout_9.addWidget(self.groupBox_3)
        spacerItem10 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_9.addItem(spacerItem10)
        self.groupBox = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox.setFlat(False)
        self.groupBox.setCheckable(False)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.gridLayout_5 = QtWidgets.QGridLayout()
        self.gridLayout_5.setContentsMargins(10, 10, -1, 10)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.mysql_usr_lineEdit = QtWidgets.QLineEdit(self.groupBox)
        self.mysql_usr_lineEdit.setObjectName("mysql_usr_lineEdit")
        self.gridLayout_5.addWidget(self.mysql_usr_lineEdit, 3, 1, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.groupBox)
        self.label_11.setObjectName("label_11")
        self.gridLayout_5.addWidget(self.label_11, 3, 0, 1, 1)
        self.mysql_port_lineEdit = QtWidgets.QLineEdit(self.groupBox)
        self.mysql_port_lineEdit.setObjectName("mysql_port_lineEdit")
        self.gridLayout_5.addWidget(self.mysql_port_lineEdit, 2, 1, 1, 1)
        self.label_19 = QtWidgets.QLabel(self.groupBox)
        self.label_19.setObjectName("label_19")
        self.gridLayout_5.addWidget(self.label_19, 2, 0, 1, 1)
        self.mysql_ip_lineEdit = QtWidgets.QLineEdit(self.groupBox)
        self.mysql_ip_lineEdit.setObjectName("mysql_ip_lineEdit")
        self.gridLayout_5.addWidget(self.mysql_ip_lineEdit, 1, 1, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.groupBox)
        self.label_13.setObjectName("label_13")
        self.gridLayout_5.addWidget(self.label_13, 4, 0, 1, 1)
        self.mysql_tmpdir_lineEdit = QtWidgets.QLineEdit(self.groupBox)
        self.mysql_tmpdir_lineEdit.setObjectName("mysql_tmpdir_lineEdit")
        self.gridLayout_5.addWidget(self.mysql_tmpdir_lineEdit, 0, 1, 1, 1)
        self.mysql_tmpdir_pushButton = QtWidgets.QPushButton(self.groupBox)
        self.mysql_tmpdir_pushButton.setObjectName("mysql_tmpdir_pushButton")
        self.gridLayout_5.addWidget(self.mysql_tmpdir_pushButton, 0, 2, 1, 1)
        self.mysql_test_pushButton = QtWidgets.QPushButton(self.groupBox)
        self.mysql_test_pushButton.setObjectName("mysql_test_pushButton")
        self.gridLayout_5.addWidget(self.mysql_test_pushButton, 2, 2, 1, 1)
        self.label_20 = QtWidgets.QLabel(self.groupBox)
        self.label_20.setObjectName("label_20")
        self.gridLayout_5.addWidget(self.label_20, 0, 0, 1, 1)
        self.label_18 = QtWidgets.QLabel(self.groupBox)
        self.label_18.setObjectName("label_18")
        self.gridLayout_5.addWidget(self.label_18, 1, 0, 1, 1)
        self.mysql_pwd_lineEdit = QtWidgets.QLineEdit(self.groupBox)
        self.mysql_pwd_lineEdit.setObjectName("mysql_pwd_lineEdit")
        self.gridLayout_5.addWidget(self.mysql_pwd_lineEdit, 4, 1, 1, 1)
        self.verticalLayout_8.addLayout(self.gridLayout_5)
        self.verticalLayout_9.addWidget(self.groupBox)
        spacerItem11 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_9.addItem(spacerItem11)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.tab_2)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        spacerItem12 = QtWidgets.QSpacerItem(20, 9, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_7.addItem(spacerItem12)
        self.label_43 = QtWidgets.QLabel(self.tab_2)
        self.label_43.setObjectName("label_43")
        self.verticalLayout_7.addWidget(self.label_43)
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setContentsMargins(10, 20, 10, -1)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.sched_import_pushButton = QtWidgets.QPushButton(self.tab_2)
        self.sched_import_pushButton.setObjectName("sched_import_pushButton")
        self.horizontalLayout_11.addWidget(self.sched_import_pushButton)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.label_40 = QtWidgets.QLabel(self.tab_2)
        self.label_40.setObjectName("label_40")
        self.horizontalLayout_14.addWidget(self.label_40)
        self.sched_import_timeEdit = QtWidgets.QTimeEdit(self.tab_2)
        self.sched_import_timeEdit.setObjectName("sched_import_timeEdit")
        self.horizontalLayout_14.addWidget(self.sched_import_timeEdit)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem13)
        self.horizontalLayout_11.addLayout(self.horizontalLayout_14)
        self.verticalLayout_7.addLayout(self.horizontalLayout_11)
        spacerItem14 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_7.addItem(spacerItem14)
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_18.setContentsMargins(10, -1, 10, -1)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.start_import_pushButton = QtWidgets.QPushButton(self.tab_2)
        self.start_import_pushButton.setObjectName("start_import_pushButton")
        self.horizontalLayout_18.addWidget(self.start_import_pushButton)
        self.import_status_label = QtWidgets.QLabel(self.tab_2)
        self.import_status_label.setObjectName("import_status_label")
        self.horizontalLayout_18.addWidget(self.import_status_label)
        spacerItem15 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem15)
        self.verticalLayout_7.addLayout(self.horizontalLayout_18)
        spacerItem16 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_7.addItem(spacerItem16)
        self.groupBox_5 = QtWidgets.QGroupBox(self.tab_2)
        self.groupBox_5.setObjectName("groupBox_5")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.groupBox_5)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.hdf5_time_progressBar = QtWidgets.QProgressBar(self.groupBox_5)
        self.hdf5_time_progressBar.setProperty("value", 0)
        self.hdf5_time_progressBar.setObjectName("hdf5_time_progressBar")
        self.gridLayout.addWidget(self.hdf5_time_progressBar, 4, 1, 1, 1)
        self.hdf5_weight_label = QtWidgets.QLabel(self.groupBox_5)
        self.hdf5_weight_label.setObjectName("hdf5_weight_label")
        self.gridLayout.addWidget(self.hdf5_weight_label, 5, 1, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.groupBox_5)
        self.label_14.setObjectName("label_14")
        self.gridLayout.addWidget(self.label_14, 2, 0, 1, 1)
        self.hdf5_trans_progressBar = QtWidgets.QProgressBar(self.groupBox_5)
        self.hdf5_trans_progressBar.setProperty("value", 0)
        self.hdf5_trans_progressBar.setObjectName("hdf5_trans_progressBar")
        self.gridLayout.addWidget(self.hdf5_trans_progressBar, 3, 1, 1, 1)
        self.label_4 = QtWidgets.QLabel(self.groupBox_5)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 3, 0, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.groupBox_5)
        self.label_8.setObjectName("label_8")
        self.gridLayout.addWidget(self.label_8, 0, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBox_5)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 4, 0, 1, 1)
        self.hdf5_5min_progressBar = QtWidgets.QProgressBar(self.groupBox_5)
        self.hdf5_5min_progressBar.setProperty("value", 0)
        self.hdf5_5min_progressBar.setObjectName("hdf5_5min_progressBar")
        self.gridLayout.addWidget(self.hdf5_5min_progressBar, 1, 1, 1, 1)
        self.hdf5_day_progressBar = QtWidgets.QProgressBar(self.groupBox_5)
        self.hdf5_day_progressBar.setProperty("value", 0)
        self.hdf5_day_progressBar.setObjectName("hdf5_day_progressBar")
        self.gridLayout.addWidget(self.hdf5_day_progressBar, 0, 1, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.groupBox_5)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 1, 0, 1, 1)
        self.hdf5_min_progressBar = QtWidgets.QProgressBar(self.groupBox_5)
        self.hdf5_min_progressBar.setProperty("value", 0)
        self.hdf5_min_progressBar.setObjectName("hdf5_min_progressBar")
        self.gridLayout.addWidget(self.hdf5_min_progressBar, 2, 1, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.groupBox_5)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 5, 0, 1, 1)
        spacerItem17 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout.addItem(spacerItem17, 0, 2, 1, 1)
        self.verticalLayout_5.addLayout(self.gridLayout)
        spacerItem18 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_5.addItem(spacerItem18)
        self.import_detail_textEdit = QtWidgets.QTextEdit(self.groupBox_5)
        self.import_detail_textEdit.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.import_detail_textEdit.setReadOnly(True)
        self.import_detail_textEdit.setObjectName("import_detail_textEdit")
        self.verticalLayout_5.addWidget(self.import_detail_textEdit)
        self.verticalLayout_7.addWidget(self.groupBox_5)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.tab_6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.groupBox_6 = QtWidgets.QGroupBox(self.tab_6)
        self.groupBox_6.setObjectName("groupBox_6")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox_6)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setContentsMargins(20, 20, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label_35 = QtWidgets.QLabel(self.groupBox_6)
        self.label_35.setObjectName("label_35")
        self.verticalLayout.addWidget(self.label_35)
        self.label_36 = QtWidgets.QLabel(self.groupBox_6)
        self.label_36.setObjectName("label_36")
        self.verticalLayout.addWidget(self.label_36)
        self.verticalLayout_4.addLayout(self.verticalLayout)
        spacerItem19 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_4.addItem(spacerItem19)
        self.gridLayout_6 = QtWidgets.QGridLayout()
        self.gridLayout_6.setContentsMargins(20, -1, -1, -1)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.preload_month_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_month_spinBox.setMaximum(999999)
        self.preload_month_spinBox.setObjectName("preload_month_spinBox")
        self.gridLayout_6.addWidget(self.preload_month_spinBox, 2, 2, 1, 1)
        self.preload_quarter_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_quarter_spinBox.setMaximum(999999)
        self.preload_quarter_spinBox.setObjectName("preload_quarter_spinBox")
        self.gridLayout_6.addWidget(self.preload_quarter_spinBox, 3, 2, 1, 1)
        self.preload_min5_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_min5_checkBox.setObjectName("preload_min5_checkBox")
        self.gridLayout_6.addWidget(self.preload_min5_checkBox, 7, 0, 1, 1)
        self.label_33 = QtWidgets.QLabel(self.groupBox_6)
        self.label_33.setObjectName("label_33")
        self.gridLayout_6.addWidget(self.label_33, 9, 1, 1, 1)
        self.label_26 = QtWidgets.QLabel(self.groupBox_6)
        self.label_26.setObjectName("label_26")
        self.gridLayout_6.addWidget(self.label_26, 2, 1, 1, 1)
        self.preload_week_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_week_spinBox.setMaximum(999999)
        self.preload_week_spinBox.setObjectName("preload_week_spinBox")
        self.gridLayout_6.addWidget(self.preload_week_spinBox, 1, 2, 1, 1)
        self.label_27 = QtWidgets.QLabel(self.groupBox_6)
        self.label_27.setObjectName("label_27")
        self.gridLayout_6.addWidget(self.label_27, 3, 1, 1, 1)
        self.preload_year_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_year_checkBox.setObjectName("preload_year_checkBox")
        self.gridLayout_6.addWidget(self.preload_year_checkBox, 5, 0, 1, 1)
        self.preload_halfyear_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_halfyear_spinBox.setMaximum(999999)
        self.preload_halfyear_spinBox.setObjectName("preload_halfyear_spinBox")
        self.gridLayout_6.addWidget(self.preload_halfyear_spinBox, 4, 2, 1, 1)
        self.preload_day_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_day_spinBox.setMaximum(999999)
        self.preload_day_spinBox.setObjectName("preload_day_spinBox")
        self.gridLayout_6.addWidget(self.preload_day_spinBox, 0, 2, 1, 1)
        self.preload_min30_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_min30_checkBox.setObjectName("preload_min30_checkBox")
        self.gridLayout_6.addWidget(self.preload_min30_checkBox, 9, 0, 1, 1)
        self.label_28 = QtWidgets.QLabel(self.groupBox_6)
        self.label_28.setObjectName("label_28")
        self.gridLayout_6.addWidget(self.label_28, 4, 1, 1, 1)
        self.preload_min15_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_min15_spinBox.setMaximum(999999)
        self.preload_min15_spinBox.setObjectName("preload_min15_spinBox")
        self.gridLayout_6.addWidget(self.preload_min15_spinBox, 8, 2, 1, 1)
        self.save_pushButton = QtWidgets.QPushButton(self.groupBox_6)
        self.save_pushButton.setObjectName("save_pushButton")
        self.gridLayout_6.addWidget(self.save_pushButton, 4, 4, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.groupBox_6)
        self.label_34.setObjectName("label_34")
        self.gridLayout_6.addWidget(self.label_34, 10, 1, 1, 1)
        self.preload_hour2_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_hour2_checkBox.setObjectName("preload_hour2_checkBox")
        self.gridLayout_6.addWidget(self.preload_hour2_checkBox, 11, 0, 1, 1)
        self.preload_halfyear_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_halfyear_checkBox.setObjectName("preload_halfyear_checkBox")
        self.gridLayout_6.addWidget(self.preload_halfyear_checkBox, 4, 0, 1, 1)
        self.preload_day_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_day_checkBox.setObjectName("preload_day_checkBox")
        self.gridLayout_6.addWidget(self.preload_day_checkBox, 0, 0, 1, 1)
        self.label_24 = QtWidgets.QLabel(self.groupBox_6)
        self.label_24.setObjectName("label_24")
        self.gridLayout_6.addWidget(self.label_24, 0, 1, 1, 1)
        self.preload_min1_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_min1_checkBox.setObjectName("preload_min1_checkBox")
        self.gridLayout_6.addWidget(self.preload_min1_checkBox, 6, 0, 1, 1)
        self.preload_min15_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_min15_checkBox.setObjectName("preload_min15_checkBox")
        self.gridLayout_6.addWidget(self.preload_min15_checkBox, 8, 0, 1, 1)
        self.preload_min1_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_min1_spinBox.setMaximum(999999)
        self.preload_min1_spinBox.setObjectName("preload_min1_spinBox")
        self.gridLayout_6.addWidget(self.preload_min1_spinBox, 6, 2, 1, 1)
        self.preload_min60_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_min60_checkBox.setObjectName("preload_min60_checkBox")
        self.gridLayout_6.addWidget(self.preload_min60_checkBox, 10, 0, 1, 1)
        self.preload_min60_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_min60_spinBox.setMaximum(999999)
        self.preload_min60_spinBox.setObjectName("preload_min60_spinBox")
        self.gridLayout_6.addWidget(self.preload_min60_spinBox, 10, 2, 1, 1)
        self.label_29 = QtWidgets.QLabel(self.groupBox_6)
        self.label_29.setObjectName("label_29")
        self.gridLayout_6.addWidget(self.label_29, 5, 1, 1, 1)
        self.preload_hour2_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_hour2_spinBox.setMaximum(999999)
        self.preload_hour2_spinBox.setObjectName("preload_hour2_spinBox")
        self.gridLayout_6.addWidget(self.preload_hour2_spinBox, 11, 2, 1, 1)
        self.preload_week_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_week_checkBox.setObjectName("preload_week_checkBox")
        self.gridLayout_6.addWidget(self.preload_week_checkBox, 1, 0, 1, 1)
        spacerItem20 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_6.addItem(spacerItem20, 4, 3, 1, 1)
        self.preload_quarter_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_quarter_checkBox.setObjectName("preload_quarter_checkBox")
        self.gridLayout_6.addWidget(self.preload_quarter_checkBox, 3, 0, 1, 1)
        self.label_31 = QtWidgets.QLabel(self.groupBox_6)
        self.label_31.setObjectName("label_31")
        self.gridLayout_6.addWidget(self.label_31, 7, 1, 1, 1)
        self.label_25 = QtWidgets.QLabel(self.groupBox_6)
        self.label_25.setObjectName("label_25")
        self.gridLayout_6.addWidget(self.label_25, 1, 1, 1, 1)
        self.preload_month_checkBox = QtWidgets.QCheckBox(self.groupBox_6)
        self.preload_month_checkBox.setObjectName("preload_month_checkBox")
        self.gridLayout_6.addWidget(self.preload_month_checkBox, 2, 0, 1, 1)
        self.label_30 = QtWidgets.QLabel(self.groupBox_6)
        self.label_30.setObjectName("label_30")
        self.gridLayout_6.addWidget(self.label_30, 6, 1, 1, 1)
        self.preload_min5_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_min5_spinBox.setMaximum(999999)
        self.preload_min5_spinBox.setObjectName("preload_min5_spinBox")
        self.gridLayout_6.addWidget(self.preload_min5_spinBox, 7, 2, 1, 1)
        self.label_42 = QtWidgets.QLabel(self.groupBox_6)
        self.label_42.setObjectName("label_42")
        self.gridLayout_6.addWidget(self.label_42, 11, 1, 1, 1)
        self.label_32 = QtWidgets.QLabel(self.groupBox_6)
        self.label_32.setObjectName("label_32")
        self.gridLayout_6.addWidget(self.label_32, 8, 1, 1, 1)
        self.preload_min30_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_min30_spinBox.setMaximum(999999)
        self.preload_min30_spinBox.setObjectName("preload_min30_spinBox")
        self.gridLayout_6.addWidget(self.preload_min30_spinBox, 9, 2, 1, 1)
        self.preload_year_spinBox = QtWidgets.QSpinBox(self.groupBox_6)
        self.preload_year_spinBox.setMaximum(999999)
        self.preload_year_spinBox.setObjectName("preload_year_spinBox")
        self.gridLayout_6.addWidget(self.preload_year_spinBox, 5, 2, 1, 1)
        self.verticalLayout_4.addLayout(self.gridLayout_6)
        spacerItem21 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem21)
        self.verticalLayout_3.addWidget(self.groupBox_6)
        self.tabWidget.addTab(self.tab_6, "")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.tab)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        spacerItem22 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_2.addItem(spacerItem22)
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setContentsMargins(20, -1, -1, -1)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.collect_start_pushButton = QtWidgets.QPushButton(self.tab)
        self.collect_start_pushButton.setObjectName("collect_start_pushButton")
        self.horizontalLayout_15.addWidget(self.collect_start_pushButton)
        self.collect_status_label = QtWidgets.QLabel(self.tab)
        self.collect_status_label.setObjectName("collect_status_label")
        self.horizontalLayout_15.addWidget(self.collect_status_label)
        spacerItem23 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_15.addItem(spacerItem23)
        self.verticalLayout_2.addLayout(self.horizontalLayout_15)
        spacerItem24 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_2.addItem(spacerItem24)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setContentsMargins(20, -1, -1, -1)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.label_39 = QtWidgets.QLabel(self.tab)
        self.label_39.setObjectName("label_39")
        self.horizontalLayout_13.addWidget(self.label_39)
        self.collect_source_comboBox = QtWidgets.QComboBox(self.tab)
        self.collect_source_comboBox.setObjectName("collect_source_comboBox")
        self.collect_source_comboBox.addItem("")
        self.collect_source_comboBox.addItem("")
        self.horizontalLayout_13.addWidget(self.collect_source_comboBox)
        spacerItem25 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem25)
        self.verticalLayout_2.addLayout(self.horizontalLayout_13)
        spacerItem26 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_2.addItem(spacerItem26)
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setContentsMargins(20, -1, -1, -1)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.label_23 = QtWidgets.QLabel(self.tab)
        self.label_23.setObjectName("label_23")
        self.horizontalLayout_10.addWidget(self.label_23)
        self.collect_sample_spinBox = QtWidgets.QSpinBox(self.tab)
        self.collect_sample_spinBox.setMaximum(86400)
        self.collect_sample_spinBox.setObjectName("collect_sample_spinBox")
        self.horizontalLayout_10.addWidget(self.collect_sample_spinBox)
        spacerItem27 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem27)
        self.verticalLayout_2.addLayout(self.horizontalLayout_10)
        spacerItem28 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_2.addItem(spacerItem28)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setContentsMargins(20, -1, -1, -1)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.label_21 = QtWidgets.QLabel(self.tab)
        self.label_21.setObjectName("label_21")
        self.horizontalLayout_8.addWidget(self.label_21)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.collect_phase1_start_timeEdit = QtWidgets.QTimeEdit(self.tab)
        self.collect_phase1_start_timeEdit.setCurrentSection(QtWidgets.QDateTimeEdit.HourSection)
        self.collect_phase1_start_timeEdit.setObjectName("collect_phase1_start_timeEdit")
        self.horizontalLayout_6.addWidget(self.collect_phase1_start_timeEdit)
        self.label_22 = QtWidgets.QLabel(self.tab)
        self.label_22.setObjectName("label_22")
        self.horizontalLayout_6.addWidget(self.label_22)
        self.collect_phase1_last_timeEdit = QtWidgets.QTimeEdit(self.tab)
        self.collect_phase1_last_timeEdit.setObjectName("collect_phase1_last_timeEdit")
        self.horizontalLayout_6.addWidget(self.collect_phase1_last_timeEdit)
        spacerItem29 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem29)
        self.horizontalLayout_8.addLayout(self.horizontalLayout_6)
        self.verticalLayout_2.addLayout(self.horizontalLayout_8)
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setContentsMargins(20, -1, -1, -1)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.label_37 = QtWidgets.QLabel(self.tab)
        self.label_37.setObjectName("label_37")
        self.horizontalLayout_9.addWidget(self.label_37)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.collect_phase2_start_timeEdit = QtWidgets.QTimeEdit(self.tab)
        self.collect_phase2_start_timeEdit.setCurrentSection(QtWidgets.QDateTimeEdit.HourSection)
        self.collect_phase2_start_timeEdit.setObjectName("collect_phase2_start_timeEdit")
        self.horizontalLayout_7.addWidget(self.collect_phase2_start_timeEdit)
        self.label_38 = QtWidgets.QLabel(self.tab)
        self.label_38.setObjectName("label_38")
        self.horizontalLayout_7.addWidget(self.label_38)
        self.collect_phase2_last_timeEdit = QtWidgets.QTimeEdit(self.tab)
        self.collect_phase2_last_timeEdit.setObjectName("collect_phase2_last_timeEdit")
        self.horizontalLayout_7.addWidget(self.collect_phase2_last_timeEdit)
        spacerItem30 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem30)
        self.horizontalLayout_9.addLayout(self.horizontalLayout_7)
        self.verticalLayout_2.addLayout(self.horizontalLayout_9)
        spacerItem31 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_2.addItem(spacerItem31)
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_16.setContentsMargins(20, 0, -1, -1)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.collect_use_zhima_checkBox = QtWidgets.QCheckBox(self.tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.collect_use_zhima_checkBox.sizePolicy().hasHeightForWidth())
        self.collect_use_zhima_checkBox.setSizePolicy(sizePolicy)
        self.collect_use_zhima_checkBox.setObjectName("collect_use_zhima_checkBox")
        self.horizontalLayout_16.addWidget(self.collect_use_zhima_checkBox)
        self.verticalLayout_2.addLayout(self.horizontalLayout_16)
        spacerItem32 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_2.addItem(spacerItem32)
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_17.setContentsMargins(20, 0, -1, -1)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.textBrowser = QtWidgets.QTextBrowser(self.tab)
        self.textBrowser.setObjectName("textBrowser")
        self.horizontalLayout_17.addWidget(self.textBrowser)
        self.verticalLayout_2.addLayout(self.horizontalLayout_17)
        self.tabWidget.addTab(self.tab, "")
        self.tab_star = QtWidgets.QWidget()
        self.tab_star.setEnabled(True)
        self.tab_star.setObjectName("tab_star")
        self.label_44 = QtWidgets.QLabel(self.tab_star)
        self.label_44.setGeometry(QtCore.QRect(40, 380, 291, 171))
        self.label_44.setText("")
        self.label_44.setPixmap(QtGui.QPixmap("../images/star.png"))
        self.label_44.setScaledContents(True)
        self.label_44.setObjectName("label_44")
        self.label_46 = QtWidgets.QLabel(self.tab_star)
        self.label_46.setGeometry(QtCore.QRect(30, 20, 501, 211))
        self.label_46.setWordWrap(True)
        self.label_46.setObjectName("label_46")
        self.line_2 = QtWidgets.QFrame(self.tab_star)
        self.line_2.setGeometry(QtCore.QRect(30, 240, 511, 16))
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.label_47 = QtWidgets.QLabel(self.tab_star)
        self.label_47.setGeometry(QtCore.QRect(40, 260, 151, 16))
        font = QtGui.QFont()
        font.setBold(True)
        self.label_47.setFont(font)
        self.label_47.setObjectName("label_47")
        self.line_3 = QtWidgets.QFrame(self.tab_star)
        self.line_3.setGeometry(QtCore.QRect(30, 330, 511, 16))
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_3.setObjectName("line_3")
        self.label_48 = QtWidgets.QLabel(self.tab_star)
        self.label_48.setGeometry(QtCore.QRect(40, 356, 331, 16))
        font = QtGui.QFont()
        font.setBold(True)
        self.label_48.setFont(font)
        self.label_48.setObjectName("label_48")
        self.line_4 = QtWidgets.QFrame(self.tab_star)
        self.line_4.setGeometry(QtCore.QRect(30, 560, 511, 16))
        self.line_4.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_4.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_4.setObjectName("line_4")
        self.label_49 = QtWidgets.QLabel(self.tab_star)
        self.label_49.setGeometry(QtCore.QRect(40, 580, 81, 16))
        font = QtGui.QFont()
        font.setBold(True)
        self.label_49.setFont(font)
        self.label_49.setObjectName("label_49")
        self.label_license = QtWidgets.QLabel(self.tab_star)
        self.label_license.setGeometry(QtCore.QRect(40, 610, 511, 81))
        self.label_license.setObjectName("label_license")
        self.layoutWidget = QtWidgets.QWidget(self.tab_star)
        self.layoutWidget.setGeometry(QtCore.QRect(40, 290, 501, 33))
        self.layoutWidget.setObjectName("layoutWidget")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.layoutWidget)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.label_45 = QtWidgets.QLabel(self.layoutWidget)
        self.label_45.setObjectName("label_45")
        self.horizontalLayout_20.addWidget(self.label_45)
        self.email_lineEdit = QtWidgets.QLineEdit(self.layoutWidget)
        self.email_lineEdit.setObjectName("email_lineEdit")
        self.horizontalLayout_20.addWidget(self.email_lineEdit)
        self.fetch_trial_pushButton = QtWidgets.QPushButton(self.layoutWidget)
        self.fetch_trial_pushButton.setObjectName("fetch_trial_pushButton")
        self.horizontalLayout_20.addWidget(self.fetch_trial_pushButton)
        self.tabWidget.addTab(self.tab_star, "")
        self.horizontalLayout_12.addWidget(self.tabWidget)
        self.verticalLayout_6 = QtWidgets.QVBoxLayout()
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.label_41 = QtWidgets.QLabel(self.centralwidget)
        self.label_41.setObjectName("label_41")
        self.verticalLayout_6.addWidget(self.label_41)
        self.log_textEdit = QtWidgets.QTextEdit(self.centralwidget)
        self.log_textEdit.setReadOnly(True)
        self.log_textEdit.setAcceptRichText(True)
        self.log_textEdit.setObjectName("log_textEdit")
        self.verticalLayout_6.addWidget(self.log_textEdit)
        self.horizontalLayout_12.addLayout(self.verticalLayout_6)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "数据导入工具"))
        self.groupBox_2.setTitle(_translate("MainWindow", "数据源设置"))
        self.pytdx_radioButton.setText(_translate("MainWindow", "使用Pytdx下载数据"))
        self.label_16.setText(_translate("MainWindow", "同时使用"))
        self.label_17.setText(_translate("MainWindow", "个通达信服务器进行下载"))
        self.tdx_radioButton.setText(_translate("MainWindow", "使用通达信盘后数据（不支持分笔、分时数据，需要配置安装路径）"))
        self.select_tdx_dir_pushButton.setText(_translate("MainWindow", "选择"))
        self.label_2.setText(_translate("MainWindow", "通达信安装目录："))
        self.qmt_radioButton.setText(_translate("MainWindow", "使用 qmt 数据下载（需要 miniqmt）（屏蔽，qmt下载数据太慢，且不稳定）"))
        self.groupBox_7.setTitle(_translate("MainWindow", "导入设置"))
        self.import_weight_checkBox.setText(_translate("MainWindow", "下载权息数据"))
        self.import_history_finance_checkBox.setText(_translate("MainWindow", "下载历史财务数据"))
        self.import_block_checkBox.setText(_translate("MainWindow", "下载行业与指数板块"))
        self.import_stock_checkBox.setText(_translate("MainWindow", "股票"))
        self.import_fund_checkBox.setText(_translate("MainWindow", "基金"))
        self.import_future_checkBox.setText(_translate("MainWindow", "期货"))
        self.import_day_checkBox.setText(_translate("MainWindow", "日线"))
        self.import_min5_checkBox.setText(_translate("MainWindow", "5分钟线"))
        self.import_min_checkBox.setText(_translate("MainWindow", "1分钟线"))
        self.import_trans_checkBox.setText(_translate("MainWindow", "分笔"))
        self.import_time_checkBox.setText(_translate("MainWindow", "分时"))
        self.label_7.setText(_translate("MainWindow", "初次导入分时起始日期："))
        self.label_6.setText(_translate("MainWindow", "初次导入分笔起始日期："))
        self.label_3.setText(_translate("MainWindow", "初次导入日线起始日期："))
        self.label_10.setText(_translate("MainWindow", "初次导入5分钟线起始日期："))
        self.label_15.setText(_translate("MainWindow", "初次导入1分钟线起始日期："))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("MainWindow", "导入设置"))
        self.groupBox_4.setTitle(_translate("MainWindow", "存储引擎设置"))
        self.enable_hdf55_radioButton.setText(_translate("MainWindow", "使用HDF5（推荐）"))
        self.enable_mysql_radioButton.setText(_translate("MainWindow", "使用MYSQL"))
        self.groupBox_3.setTitle(_translate("MainWindow", "HDF5存储设置"))
        self.hdf5_dir_pushButton.setText(_translate("MainWindow", "选择"))
        self.label.setText(_translate("MainWindow", "目标数据（HDF5）存放目录："))
        self.groupBox.setTitle(_translate("MainWindow", "MYSQL存储设置"))
        self.label_11.setText(_translate("MainWindow", "用户名："))
        self.label_19.setText(_translate("MainWindow", "端口号："))
        self.label_13.setText(_translate("MainWindow", "密码"))
        self.mysql_tmpdir_pushButton.setText(_translate("MainWindow", "选择"))
        self.mysql_test_pushButton.setText(_translate("MainWindow", "测试连接"))
        self.label_20.setText(_translate("MainWindow", "临时文件目录："))
        self.label_18.setText(_translate("MainWindow", "主机名/IP："))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("MainWindow", "存储设置"))
        self.label_43.setText(_translate("MainWindow", "    注：HDF5存储导入时，请关闭其他 hikyuu 进程，HDF5不支持同时读写！"))
        self.sched_import_pushButton.setText(_translate("MainWindow", "启动定时导入"))
        self.label_40.setText(_translate("MainWindow", "导入执行时间："))
        self.start_import_pushButton.setText(_translate("MainWindow", "手工执行导入"))
        self.import_status_label.setText(_translate("MainWindow", "请勿盘中导入！"))
        self.groupBox_5.setTitle(_translate("MainWindow", "导入进展"))
        self.hdf5_weight_label.setText(_translate("MainWindow", "TextLabel"))
        self.label_14.setText(_translate("MainWindow", "导入1分钟线："))
        self.label_4.setText(_translate("MainWindow", "导入分笔数据："))
        self.label_8.setText(_translate("MainWindow", "导入日线："))
        self.label_5.setText(_translate("MainWindow", "导入分时数据："))
        self.label_12.setText(_translate("MainWindow", "导入5分钟线："))
        self.label_9.setText(_translate("MainWindow", "导入权息数据："))
        self.import_detail_textEdit.setHtml(_translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:\'.AppleSystemUIFont\'; font-size:13pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入上证日线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入深证日线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入上证5分钟线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入深证5分钟线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入上证1分钟线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入深证1分钟线记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入上证分笔记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入深证分笔记录：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入上证分时数据：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入深证分时数据：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入权息数据数：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">导入完毕！</span></p></body></html>"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("MainWindow", "执行导入"))
        self.groupBox_6.setTitle(_translate("MainWindow", "预加载设置"))
        self.label_35.setText(_translate("MainWindow", "此处为 Hikyuu 运行时的数据预加载设置，请根据机器内存大小选择"))
        self.label_36.setText(_translate("MainWindow", "（目前加载全部日线数据目前需要约需900M内存）"))
        self.preload_min5_checkBox.setText(_translate("MainWindow", "预加载5分钟线"))
        self.label_33.setText(_translate("MainWindow", "最大缓存数量："))
        self.label_26.setText(_translate("MainWindow", "最大缓存数量："))
        self.label_27.setText(_translate("MainWindow", "最大缓存数量："))
        self.preload_year_checkBox.setText(_translate("MainWindow", "预加载年线"))
        self.preload_min30_checkBox.setText(_translate("MainWindow", "预加载30分钟线"))
        self.label_28.setText(_translate("MainWindow", "最大缓存数量："))
        self.save_pushButton.setText(_translate("MainWindow", "保存设置"))
        self.label_34.setText(_translate("MainWindow", "最大缓存数量："))
        self.preload_hour2_checkBox.setText(_translate("MainWindow", "预加载120分钟线"))
        self.preload_halfyear_checkBox.setText(_translate("MainWindow", "预加载半年线"))
        self.preload_day_checkBox.setText(_translate("MainWindow", "预加载日线"))
        self.label_24.setText(_translate("MainWindow", "最大缓存数量："))
        self.preload_min1_checkBox.setText(_translate("MainWindow", "预加载1分钟线"))
        self.preload_min15_checkBox.setText(_translate("MainWindow", "预加载15分钟线"))
        self.preload_min60_checkBox.setText(_translate("MainWindow", "预加载60分钟线"))
        self.label_29.setText(_translate("MainWindow", "最大缓存数量："))
        self.preload_week_checkBox.setText(_translate("MainWindow", "预加载周线"))
        self.preload_quarter_checkBox.setText(_translate("MainWindow", "预加载季线"))
        self.label_31.setText(_translate("MainWindow", "最大缓存数量："))
        self.label_25.setText(_translate("MainWindow", "最大缓存数量："))
        self.preload_month_checkBox.setText(_translate("MainWindow", "预加载月线"))
        self.label_30.setText(_translate("MainWindow", "最大缓存数量："))
        self.label_42.setText(_translate("MainWindow", "最大缓存数量："))
        self.label_32.setText(_translate("MainWindow", "最大缓存数量："))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_6), _translate("MainWindow", "预加载设置"))
        self.collect_start_pushButton.setText(_translate("MainWindow", "启动采集"))
        self.collect_status_label.setText(_translate("MainWindow", "TextLabel"))
        self.label_39.setText(_translate("MainWindow", "行情数据源："))
        self.collect_source_comboBox.setItemText(0, _translate("MainWindow", "qq"))
        self.collect_source_comboBox.setItemText(1, _translate("MainWindow", "qmt"))
        self.label_23.setText(_translate("MainWindow", "采集间隔（秒）："))
        self.label_21.setText(_translate("MainWindow", "执行时间段1："))
        self.label_22.setText(_translate("MainWindow", "-"))
        self.label_37.setText(_translate("MainWindow", "执行时间段2："))
        self.label_38.setText(_translate("MainWindow", "-"))
        self.collect_use_zhima_checkBox.setText(_translate("MainWindow", "使用芝麻代理"))
        self.textBrowser.setHtml(_translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:\'.AppleSystemUIFont\'; font-size:13pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">注：</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:10pt; font-weight:600; color:#ff0000;\">1、行情采集服务仅对预加载数据有效</span><span style=\" font-family:\'SimSun\'; font-size:9pt;\">，在行情采集服务运行期间，hikyuu.interactive运行时将自动连接采集服务获取行情数据，并更新预加载的内容数据。</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:\'SimSun\'; font-size:9pt;\"><br /></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt;\">2、如使用芝麻代理（</span><a href=\"http://h.zhimaruanjian.com/\"><span style=\" font-family:\'SimSun\'; font-size:9pt; text-decoration: underline; color:#0000ff;\">http://h.zhimaruanjian.com/</span></a><span style=\" font-family:\'SimSun\'; font-size:9pt;\">），请自行申请（需付费），并确保ip为其白名单。</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:\'SimSun\'; font-size:9pt;\"><br /></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-family:\'SimSun\'; font-size:9pt; font-weight:696; color:#0000ff;\">3、此处采集为网络采集，更推荐直接运行安装目录下gui子目录下的 start_qmt.py ,使用miniqmt 实时服务。该程序独立运行，不用关闭，和这里的采集效果一样。注意：miniqmt需要QMT交易端配合，且在同一机器上执行。</span></p></body></html>"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "行情采集服务"))
        self.label_46.setText(_translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:\'.AppleSystemUIFont\'; font-size:13pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">Hikyuu <span style=\" font-weight:700;\">专注于量化交易领域的核心技术构建，涵盖交易模型开发、极速计算引擎、高效回测框架及实盘拓展能力</span>，定位为量化交易的基础设施级计算引擎，为量化交易爱好者和专业提供高性能底层架构支持。随着社区规模扩大，项目持续迭代及技术支持事务逐渐分散了作者在策略研究上的精力。为保障项目的专注度与可持续性，现对捐赠用户提供部分额外功能作为回馈。诚挚期待社区伙伴的支持，您的参与都将直接助力 Hikyuu 的技术创新，共同推动量化交易基础设施的迭代升级。</p>\n"
"<p style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">捐赠功能以插件的方式提供，采用独立授权许可，完全在 hikyuu 之外，对喜欢自行编译扩展的朋友没有影响。因插件许可授权需要采集硬件信息，如有疑虑只要不申请试用许可和正式许可授权，不会触发硬件信息采集，如申请，视为同意采集。</p>\n"
"<p style=\" margin-top:12px; margin-bottom:12px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-weight:700;\">详情参见：</span><a href=\"https://hikyuu.readthedocs.io/zh-cn/latest/vip/vip-plan.html\"><span style=\" text-decoration: underline; color:#3586ff;\">捐赠权益</span></a><span style=\" font-weight:700;\"> ，感谢大家的支持！</span></p></body></html>"))
        self.label_47.setText(_translate("MainWindow", "申请试用许可（30天试用）"))
        self.label_48.setText(_translate("MainWindow", "加入知识星球，获取星球许可（可同时在3台设备上使用）"))
        self.label_49.setText(_translate("MainWindow", "当前授权信息"))
        self.label_license.setText(_translate("MainWindow", "TextLabel"))
        self.label_45.setText(_translate("MainWindow", "电子邮件地址:"))
        self.fetch_trial_pushButton.setText(_translate("MainWindow", "申请试用许可"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_star), _translate("MainWindow", "项目捐赠"))
        self.label_41.setText(_translate("MainWindow", "执行日志"))
