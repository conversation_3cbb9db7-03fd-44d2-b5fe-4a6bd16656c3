#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu环境测试脚本
验证Hikyuu安装和基本功能是否正常
"""

import sys
import os
from pathlib import Path

# 在模块级别导入
try:
    import hikyuu
    from hikyuu.interactive import *
    HIKYUU_AVAILABLE = True
except ImportError as e:
    HIKYUU_AVAILABLE = False
    IMPORT_ERROR = e
    # 创建占位符以避免未定义错误
    class DummySM:
        def __len__(self): return 0
        def __getitem__(self, key): raise Exception("Hikyuu not available")
    sm = DummySM()
    def get_config_file(): return "N/A"
    def get_data_dir(): return "N/A"

def test_hikyuu_environment():
    """测试Hikyuu环境"""
    print("=" * 60)
    print("  🧪 Hikyuu环境测试")
    print("=" * 60)

    # 1. 测试Hikyuu导入
    print("1. 测试Hikyuu导入...")
    if HIKYUU_AVAILABLE:
        print(f"   ✅ Hikyuu导入成功")
        print(f"   📦 Hikyuu版本: {hikyuu.__version__}")
    else:
        print(f"   ❌ Hikyuu导入失败: {IMPORT_ERROR}")
        return False
    
    # 2. 测试基本配置
    print("\n2. 测试基本配置...")
    try:
        # 检查配置文件
        config_file = get_config_file()
        print(f"   📁 配置文件: {config_file}")
        
        # 检查数据目录
        data_dir = get_data_dir()
        print(f"   📁 数据目录: {data_dir}")
        
        if os.path.exists(data_dir):
            print(f"   ✅ 数据目录存在")
        else:
            print(f"   ⚠️  数据目录不存在，需要配置数据源")
            
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
    
    # 3. 测试股票管理器
    print("\n3. 测试股票管理器...")
    try:
        # 获取股票数量
        stock_count = len(sm)
        print(f"   📊 股票总数: {stock_count}")
        
        if stock_count > 0:
            print(f"   ✅ 股票管理器正常")
            
            # 测试获取具体股票
            try:
                stock = sm['sz000001']
                print(f"   📈 测试股票: {stock.name} ({stock.market_code}{stock.code})")
                print(f"   ✅ 股票获取成功")
            except Exception as e:
                print(f"   ⚠️  获取测试股票失败: {e}")
        else:
            print(f"   ⚠️  没有股票数据，需要下载数据")
            
    except Exception as e:
        print(f"   ❌ 股票管理器测试失败: {e}")
    
    # 4. 测试K线数据
    print("\n4. 测试K线数据...")
    try:
        if len(sm) > 0:
            stock = sm['sz000001']
            kdata = stock.get_kdata(Query(-10))
            print(f"   📊 K线数据: {len(kdata)} 条记录")
            
            if len(kdata) > 0:
                k = kdata[0]
                print(f"   📅 最新数据: {k.datetime}")
                print(f"   💰 价格信息: 开{k.open} 高{k.high} 低{k.low} 收{k.close}")
                print(f"   ✅ K线数据正常")
            else:
                print(f"   ⚠️  没有K线数据")
        else:
            print(f"   ⚠️  跳过K线测试（无股票数据）")
            
    except Exception as e:
        print(f"   ❌ K线数据测试失败: {e}")
    
    # 5. 测试技术指标
    print("\n5. 测试技术指标...")
    try:
        if len(sm) > 0:
            stock = sm['sz000001']
            kdata = stock.get_kdata(Query(-20))
            
            if len(kdata) > 0:
                close_data = CLOSE(kdata)
                ma5 = MA(close_data, n=5)
                ma20 = MA(close_data, n=20)
                
                print(f"   📈 MA5指标: {len(ma5)} 个值")
                print(f"   📈 MA20指标: {len(ma20)} 个值")
                
                if len(ma5) > 0 and len(ma20) > 0:
                    print(f"   💹 最新MA5: {ma5[-1]:.2f}")
                    print(f"   💹 最新MA20: {ma20[-1]:.2f}")
                    print(f"   ✅ 技术指标正常")
                else:
                    print(f"   ⚠️  指标计算结果为空")
            else:
                print(f"   ⚠️  跳过指标测试（无K线数据）")
        else:
            print(f"   ⚠️  跳过指标测试（无股票数据）")
            
    except Exception as e:
        print(f"   ❌ 技术指标测试失败: {e}")
    
    # 6. 测试交易系统
    print("\n6. 测试交易系统...")
    try:
        if len(sm) > 0:
            stock = sm['sz000001']
            kdata = stock.get_kdata(Query(-50))
            
            if len(kdata) > 10:  # 确保有足够数据
                # 创建简单交易系统
                my_tm = crtTM(init_cash=100000)
                close_data = CLOSE(kdata)
                ma5 = MA(close_data, n=5)
                ma10 = MA(close_data, n=10)
                my_sg = SG_Cross(ma5, ma10)
                my_mm = MM_FixedCount(100)
                
                sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
                sys.run(stock, Query(-50))
                
                print(f"   💰 初始资金: {my_tm.init_cash}")
                print(f"   💰 最终资金: {my_tm.current_cash}")
                print(f"   📊 收益: {my_tm.current_cash - my_tm.init_cash:.2f}")
                
                trades = my_tm.get_trade_list()
                print(f"   🔄 交易次数: {len(trades)}")
                print(f"   ✅ 交易系统正常")
            else:
                print(f"   ⚠️  数据不足，跳过交易系统测试")
        else:
            print(f"   ⚠️  跳过交易系统测试（无股票数据）")
            
    except Exception as e:
        print(f"   ❌ 交易系统测试失败: {e}")
    
    # 7. 环境总结
    print("\n" + "=" * 60)
    print("📋 环境测试总结:")
    
    if len(sm) > 0:
        print("✅ Hikyuu环境基本正常")
        print("🎯 可以开始学习Hikyuu策略开发")
        print("\n📚 建议下一步:")
        print("   1. 运行官方示例代码")
        print("   2. 学习技术指标使用")
        print("   3. 构建简单交易策略")
    else:
        print("⚠️  需要配置数据源")
        print("\n🔧 配置步骤:")
        print("   1. 运行: hikyuutdx")
        print("   2. 或运行: python -m hikyuu.gui.HikyuuTdx")
        print("   3. 配置数据源并下载数据")
        print("   4. 重新运行此测试脚本")
    
    print("=" * 60)
    return True

def show_data_config_help():
    """显示数据配置帮助"""
    print("\n🔧 数据配置帮助:")
    print("=" * 40)
    print("如果你还没有配置数据源，请按以下步骤操作：")
    print()
    print("方法1: 使用GUI工具")
    print("   hikyuutdx")
    print()
    print("方法2: 如果命令无法执行")
    print("   python -m hikyuu.gui.HikyuuTdx")
    print()
    print("配置建议:")
    print("   1. 数据目录: 选择项目目录下的data文件夹")
    print("   2. 数据源: 选择通达信数据源")
    print("   3. 下载范围: 先下载主要指数和几只测试股票")
    print("   4. 时间范围: 建议下载最近2-3年的数据")
    print()
    print("测试股票建议:")
    print("   - sz000001 (平安银行)")
    print("   - sz000002 (万科A)")
    print("   - sh000001 (上证指数)")
    print("=" * 40)

if __name__ == "__main__":
    # 运行环境测试
    test_hikyuu_environment()
    
    # 显示配置帮助
    show_data_config_help()
