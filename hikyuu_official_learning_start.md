# 🚀 Hikyuu官方学习起步指南

## 📋 当前环境状态

✅ **已完成**:
- Anaconda base环境
- Hikyuu 2.6.5 已安装
- Python 3.13.0 环境

## 🎯 第一步：验证环境并开始官方学习

### 1. 环境验证

让我们先验证你的Hikyuu环境是否完全正常：

```python
# 验证Hikyuu安装
from hikyuu.interactive import *
print(f"Hikyuu版本: {hikyuu.__version__}")
print(f"数据目录: {get_config_file()}")
```

### 2. 数据配置（重要！）

Hikyuu需要历史数据才能进行回测。你需要配置数据源：

```bash
# 启动数据下载工具
hikyuutdx
```

如果命令无法执行，使用：
```bash
python -m hikyuu.gui.HikyuuTdx
```

**数据配置步骤**:
1. 选择数据存储位置（建议在项目目录下创建data文件夹）
2. 配置通达信数据源
3. 下载基础股票列表
4. 下载历史K线数据（建议先下载主要指数和几只测试股票）

### 3. 第一个官方示例

创建你的第一个Hikyuu策略：

```python
# 文件名: first_hikyuu_strategy.py
from hikyuu.interactive import *

# 设置数据目录（如果需要）
# set_global_context(data_dir="./data")

def first_strategy():
    """第一个Hikyuu策略：双均线交叉"""
    
    # 1. 创建交易账户
    my_tm = crtTM(init_cash=100000)  # 10万初始资金
    
    # 2. 获取股票对象
    stock = sm['sz000001']  # 平安银行
    print(f"选择股票: {stock.name} ({stock.market_code}{stock.code})")
    
    # 3. 获取K线数据
    kdata = stock.get_kdata(Query(-250))  # 最近250个交易日
    print(f"K线数据: {len(kdata)} 条记录")
    
    # 4. 创建技术指标
    close_data = CLOSE(kdata)
    ma5 = MA(close_data, n=5)   # 5日均线
    ma20 = MA(close_data, n=20) # 20日均线
    
    # 5. 创建信号指示器（5日线上穿20日线买入，下穿卖出）
    my_sg = SG_Cross(ma5, ma20)
    
    # 6. 创建资金管理（每次买入1000股）
    my_mm = MM_FixedCount(1000)
    
    # 7. 创建交易系统
    sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
    
    # 8. 运行回测
    sys.run(stock, Query(-250))
    
    # 9. 查看结果
    print(f"\n=== 回测结果 ===")
    print(f"初始资金: {my_tm.init_cash}")
    print(f"最终资金: {my_tm.current_cash}")
    print(f"总收益: {my_tm.current_cash - my_tm.init_cash:.2f}")
    print(f"收益率: {(my_tm.current_cash - my_tm.init_cash) / my_tm.init_cash * 100:.2f}%")
    
    # 10. 获取交易记录
    trades = my_tm.get_trade_list()
    print(f"交易次数: {len(trades)}")
    
    return sys, my_tm

if __name__ == "__main__":
    sys, tm = first_strategy()
```

### 4. 可视化结果

```python
# 文件名: visualize_strategy.py
from hikyuu.interactive import *
import matplotlib.pyplot as plt

def plot_strategy_result(stock_code='sz000001', days=250):
    """绘制策略结果"""
    
    # 获取股票和数据
    stock = sm[stock_code]
    kdata = stock.get_kdata(Query(-days))
    
    # 创建策略（同上）
    my_tm = crtTM(init_cash=100000)
    close_data = CLOSE(kdata)
    ma5 = MA(close_data, n=5)
    ma20 = MA(close_data, n=20)
    my_sg = SG_Cross(ma5, ma20)
    my_mm = MM_FixedCount(1000)
    sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
    sys.run(stock, Query(-days))
    
    # 绘制图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制价格和均线
    dates = [k.datetime for k in kdata]
    prices = [k.close for k in kdata]
    ma5_values = [ma5[i] for i in range(len(ma5))]
    ma20_values = [ma20[i] for i in range(len(ma20))]
    
    ax1.plot(dates, prices, label='收盘价', linewidth=1)
    ax1.plot(dates, ma5_values, label='MA5', linewidth=1)
    ax1.plot(dates, ma20_values, label='MA20', linewidth=1)
    ax1.set_title(f'{stock.name} 双均线策略')
    ax1.legend()
    ax1.grid(True)
    
    # 绘制资金曲线
    trades = my_tm.get_trade_list()
    if len(trades) > 0:
        trade_dates = [trade.datetime for trade in trades]
        # 简化的资金曲线（实际应该用get_funds_curve）
        cash_values = [my_tm.init_cash + (my_tm.current_cash - my_tm.init_cash) * i / len(trades) 
                      for i in range(len(trades))]
        ax2.plot(trade_dates, cash_values, label='资金曲线', color='red')
    
    ax2.set_title('资金曲线')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return sys, my_tm

if __name__ == "__main__":
    plot_strategy_result()
```

## 📚 学习路径建议

### 第1周：基础掌握
1. **Day 1-2**: 环境配置和数据下载
2. **Day 3-4**: 运行基础示例，理解核心概念
3. **Day 5-7**: 学习技术指标和K线数据操作

### 第2周：策略开发
1. **Day 1-3**: 学习信号指示器（SG）
2. **Day 4-5**: 学习资金管理（MM）
3. **Day 6-7**: 学习止损止盈（ST/TP）

### 第3周：高级功能
1. **Day 1-3**: 多因子选股（MF）
2. **Day 4-5**: 投资组合（PF）
3. **Day 6-7**: 策略优化和评估

## 🔧 实用工具脚本

### 快速测试脚本

```python
# 文件名: quick_test.py
from hikyuu.interactive import *

def quick_test():
    """快速测试Hikyuu环境"""
    try:
        # 测试基本功能
        print("1. 测试股票获取...")
        stock = sm['sz000001']
        print(f"   ✅ 股票: {stock.name}")
        
        print("2. 测试K线数据...")
        kdata = stock.get_kdata(Query(-10))
        print(f"   ✅ K线数据: {len(kdata)} 条")
        
        print("3. 测试技术指标...")
        close_data = CLOSE(kdata)
        ma5 = MA(close_data, n=5)
        print(f"   ✅ MA5指标: {len(ma5)} 个值")
        
        print("4. 测试交易系统...")
        tm = crtTM(init_cash=100000)
        sg = SG_Cross(MA(close_data, 5), MA(close_data, 10))
        mm = MM_FixedCount(100)
        sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
        sys.run(stock, Query(-50))
        print(f"   ✅ 交易系统运行完成")
        
        print("\n🎉 所有测试通过！Hikyuu环境正常！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_test()
```

## 📖 推荐学习资源

### 官方资源
1. **官方文档**: https://hikyuu.readthedocs.io/
2. **GitHub仓库**: https://github.com/fasiondog/hikyuu
3. **示例代码**: hikyuu/examples/ 目录
4. **官方教程**: Jupyter Notebook示例

### 学习顺序
1. 先运行官方示例，理解基本概念
2. 学习各个组件的独立使用
3. 组合组件构建完整策略
4. 学习策略评估和优化
5. 探索高级功能

## 🎯 下一步行动

1. **立即执行**: 运行 `quick_test.py` 验证环境
2. **配置数据**: 使用 `hikyuutdx` 下载数据
3. **运行示例**: 执行 `first_hikyuu_strategy.py`
4. **学习文档**: 阅读官方文档的快速入门部分
5. **实践练习**: 修改示例参数，观察结果变化

## 💡 学习建议

1. **从简单开始**: 先掌握基本的双均线策略
2. **理解概念**: 重点理解Hikyuu的组件化设计思想
3. **多做实验**: 尝试不同的参数和组合
4. **记录笔记**: 建立自己的策略库
5. **循序渐进**: 不要急于使用复杂功能

准备好开始你的Hikyuu学习之旅了吗？让我们从验证环境开始！
