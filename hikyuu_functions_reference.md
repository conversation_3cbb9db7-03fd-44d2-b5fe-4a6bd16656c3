# 📚 Hikyuu 2.6.5 全面函数参考手册

## 🏗️ 框架架构概览

Hikyuu采用模块化设计，主要包含以下层次：

```
Hikyuu框架
├── 数据管理层 (Data Management)
├── 技术指标层 (Technical Indicators) 
├── 交易系统核心 (Trading System Core)
├── 风险控制层 (Risk Management)
├── 高级策略层 (Advanced Strategy)
├── 分析工具层 (Analysis Tools)
├── 数据服务层 (Data Services)
├── 扩展功能层 (Extensions)
└── 配置管理层 (Configuration)
```

## 📊 数据管理层

### StockManager (股票管理器)
```python
# 全局股票管理器
sm = StockManager.instance()

# 基本操作
stock = sm['sz000001']           # 获取股票对象
stock_list = sm.get_stock_list() # 获取股票列表
market_info = sm.get_market_info() # 获取市场信息
```

### Stock (股票对象)
```python
# 股票基本信息
stock.name          # 股票名称
stock.code          # 股票代码  
stock.market        # 市场代码
stock.type          # 股票类型
stock.start_datetime # 上市日期

# 数据获取
kdata = stock.get_kdata(Query(-100))     # 获取K线数据
finance = stock.get_finance_data()       # 获取财务数据
weight = stock.get_weight()              # 获取权重数据
```

### KData & Query (K线数据和查询)
```python
# 查询对象
Query(-100)                    # 最近100个交易日
Query(start_date, end_date)    # 指定日期范围
Query.INDEX                    # 按索引查询
Query.DATE                     # 按日期查询

# K线类型
KType.DAY     # 日线
KType.WEEK    # 周线  
KType.MONTH   # 月线
KType.MIN     # 分钟线
KType.MIN5    # 5分钟线
```

### 数据序列提取
```python
# 价格序列
close_data = CLOSE(kdata)    # 收盘价序列
high_data = HIGH(kdata)      # 最高价序列
low_data = LOW(kdata)        # 最低价序列
open_data = OPEN(kdata)      # 开盘价序列
vol_data = VOL(kdata)        # 成交量序列
amo_data = AMO(kdata)        # 成交额序列
```

## 📈 技术指标层

### 趋势指标
```python
# 移动平均线
MA(data, n=5)              # 简单移动平均
EMA(data, n=5)             # 指数移动平均
SMA(data, n=5)             # 平滑移动平均
WMA(data, n=5)             # 加权移动平均

# 趋势指标
MACD(data, fast=12, slow=26, signal=9)  # MACD指标
ADX(kdata, n=14)                        # 平均趋向指数
TRIX(data, n=12)                        # 三重指数平滑移动平均
DMI(kdata, n=14)                        # 趋向指标
```

### 震荡指标
```python
# RSI相关
RSI(data, n=14)            # 相对强弱指标
STOCHRSI(data, n=14)       # 随机RSI

# KDJ指标
KDJ(kdata, n=9, m1=3, m2=3) # KDJ随机指标

# 其他震荡指标
CCI(kdata, n=14)           # 顺势指标
WR(kdata, n=14)            # 威廉指标
STOCH(kdata, n=14)         # 随机指标
```

### 成交量指标
```python
VOL(kdata)                 # 成交量
OBV(kdata)                 # 能量潮指标
AD(kdata)                  # 聚散指标
VRSI(kdata, n=14)          # 成交量RSI
```

### 价格指标
```python
BOLL(data, n=20, p=2)      # 布林带
SAR(kdata, step=0.02, max=0.2) # 抛物线指标
ATR(kdata, n=14)           # 真实波动幅度
BIAS(data, n=6)            # 乖离率
```

## 🎯 交易系统核心

### 信号指示器 (Signal)
```python
# 交叉信号
SG_Cross(fast_line, slow_line)  # 快慢线交叉

# 区间信号  
SG_Band(indicator, lower, upper) # 指标区间突破

# 条件信号
SG_Single(condition)        # 单一条件信号
SG_Bool(condition)          # 布尔条件信号

# 手动信号
SG_Manual()                 # 手动添加信号

# 单边信号
SG_OneSide(indicator, is_buy) # 单边买入/卖出信号
SG_Buy(indicator)           # 单边买入信号
SG_Sell(indicator)          # 单边卖出信号

# 信号运算
signal1 & signal2          # 信号AND运算
signal1 | signal2          # 信号OR运算
signal1 + signal2          # 信号加法运算
signal1 - signal2          # 信号减法运算
```

### 交易管理器 (TradeManager)
```python
# 创建交易管理器
tm = crtTM(
    init_cash=100000,       # 初始资金
    datetime=Datetime(),    # 账户建立日期
    costfunc=TC_Zero(),     # 交易成本算法
    name="SYS"              # 账户名称
)

# 交易操作
trade_record = tm.buy(datetime, stock, price, number)
trade_record = tm.sell(datetime, stock, price, number)

# 信息查询
tm.init_cash               # 初始资金
tm.current_cash            # 当前资金
tm.get_trade_list()        # 获取交易记录列表
tm.get_position_list()     # 获取持仓记录列表
tm.get_funds_curve(dates)  # 获取资金曲线
```

### 资金管理器 (MoneyManager)
```python
# 固定数量
MM_FixedCount(count=1000)  # 每次买入固定股数

# 固定比例
MM_FixedPercent(percent=0.1) # 每次投入固定比例资金

# 固定金额
MM_FixedCash(cash=10000)   # 每次投入固定金额

# 凯利公式
MM_Kelly()                 # 凯利公式资金管理

# 固定资本
MM_FixedCapitalFunds()     # 固定资本资金管理
```

### 交易系统 (System)
```python
# 简单交易系统
sys = SYS_Simple(
    tm=trade_manager,      # 交易管理器
    sg=signal,             # 信号指示器
    mm=money_manager,      # 资金管理器
    ev=environment,        # 市场环境判断(可选)
    cn=condition,          # 系统条件(可选)
    st=stoploss,           # 止损策略(可选)
    tp=takeprofit,         # 止盈策略(可选)
    pg=profit_goal,        # 盈利目标(可选)
    sp=slippage            # 滑点算法(可选)
)

# 运行回测
sys.run(stock, query)
```

## 🛡️ 风险控制层

### 止损策略 (Stoploss)
```python
ST_FixedPercent(percent=0.05)  # 固定百分比止损
ST_Indicator(indicator)        # 指标止损
ST_Saftyloss()                # 安全止损
```

### 止盈策略 (TakeProfit)
```python
TP_FixedPercent(percent=0.10)  # 固定百分比止盈
TP_Indicator(indicator)        # 指标止盈
```

### 市场环境判断 (Environment)
```python
EV_Bool(condition)             # 布尔条件环境判断
# 示例：只在牛市交易
ev = EV_Bool(MA(CLOSE, 20) > MA(CLOSE, 60))
```

### 系统条件 (Condition)
```python
CN_Bool(condition)             # 布尔条件系统判断
# 示例：只在特定条件下启用系统
cn = CN_Bool(RSI(CLOSE, 14) < 80)
```

### 盈利目标 (ProfitGoal)
```python
PG_FixedPercent(percent=0.20)  # 固定百分比盈利目标
PG_NoGoal()                    # 无盈利目标
```

### 滑点算法 (Slippage)
```python
SP_FixedPercent(percent=0.001) # 固定百分比滑点
SP_FixedValue(value=0.01)      # 固定数值滑点
```

## 🚀 高级策略层

### 多因子模型 (MultiFactor)
```python
# 创建多因子模型
mf = MF_MultiFactor()

# 添加因子
mf.add_factor('ROE', FINANCE('roe'))
mf.add_factor('PE', 1/FINANCE('pe'))

# 获取评分
scores = mf.get_scores(datetime)
```

### 选股器 (Selector)
```python
# 固定选股
SE_Fixed(stock_list, system)

# 多因子选股
SE_MultiFactor(multifactor, topn=10)

# 信号选股
SE_Signal(signal, topn=10)
```

### 投资组合 (Portfolio)
```python
# 简单投资组合
pf = PF_Simple(
    tm=trade_manager,
    af=allocate_funds,
    se=selector
)

# 运行组合
pf.run(query)
```

### 资金分配器 (AllocateFunds)
```python
AF_EqualWeight()               # 等权重分配
AF_FixedWeight(weights)        # 固定权重分配
AF_MultiFactor(multifactor)    # 多因子权重分配
```

## 📊 分析工具层

### 绩效分析
```python
# 绩效对象
perf = Performance()

# 绩效指标
perf.get_return()              # 获取收益率
perf.get_max_drawdown()        # 获取最大回撤
perf.get_sharpe_ratio()        # 获取夏普比率
perf.get_win_rate()            # 获取胜率
```

### 回测分析
```python
# 事件驱动回测
from hikyuu.backtest import backtest

# 滚动优化系统
WalkForwardSystem()
```

## 🔧 工具函数

### 日期时间处理
```python
Datetime()                     # 创建日期时间对象
Datetime(202501010000)         # 指定日期时间
DatetimeList()                 # 日期时间列表
```

### 数据转换
```python
# 序列化
hku_save(object, filename)     # 保存对象
hku_load(filename)             # 加载对象

# 数据转换
to_df(trade_list)              # 转换为DataFrame
to_np(position_list)           # 转换为NumPy数组
```

### 系统配置
```python
# 初始化系统
load_hikyuu(**options)

# 全局上下文
get_global_context()
set_global_context(kdata)
```

## 📝 使用示例

### 完整策略示例
```python
from hikyuu.interactive import *

# 1. 获取股票和数据
stock = sm['sz000001']
kdata = stock.get_kdata(Query(-250))

# 2. 计算技术指标
close_data = CLOSE(kdata)
ma5 = MA(close_data, 5)
ma20 = MA(close_data, 20)
rsi = RSI(close_data, 14)

# 3. 创建信号
sg = SG_Cross(ma5, ma20) & SG_Band(rsi, 30, 70)

# 4. 创建交易管理器
tm = crtTM(init_cash=100000)

# 5. 创建资金管理器
mm = MM_FixedCount(1000)

# 6. 创建风险控制
st = ST_FixedPercent(0.05)  # 5%止损

# 7. 创建交易系统
sys = SYS_Simple(tm=tm, sg=sg, mm=mm, st=st)

# 8. 运行回测
sys.run(stock, Query(-250))

# 9. 分析结果
print(f"收益率: {(tm.current_cash - tm.init_cash) / tm.init_cash * 100:.2f}%")
print(f"交易次数: {len(tm.get_trade_list())}")
```

---

*本参考手册基于Hikyuu 2.6.5版本编写，涵盖了框架的主要功能和API。*
*更多详细信息请参考官方文档：https://hikyuu.readthedocs.io/*
