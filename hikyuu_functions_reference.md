# 📚 Hikyuu 2.6.5 全面编程参考手册

## 🏗️ 框架架构概览

Hikyuu采用模块化设计，主要包含以下层次：

```
Hikyuu框架
├── 数据管理层 (Data Management)
├── 技术指标层 (Technical Indicators)
├── 交易系统核心 (Trading System Core)
├── 风险控制层 (Risk Management)
├── 高级策略层 (Advanced Strategy)
├── 分析工具层 (Analysis Tools)
├── 数据服务层 (Data Services)
├── 扩展功能层 (Extensions)
└── 配置管理层 (Configuration)
```

## 🔧 核心数据类型

### 基础类型定义
```python
# 价格类型
price_t = float                    # 价格类型，通常为float

# 日期时间类型
Datetime                          # 日期时间对象
DatetimeList                      # 日期时间列表

# 股票相关类型
Stock                            # 股票对象
StockManager                     # 股票管理器
KData                           # K线数据对象
KRecord                         # 单条K线记录

# 指标类型
Indicator                       # 技术指标对象
IndicatorList                   # 指标列表

# 交易相关类型
TradeRecord                     # 交易记录
TradeRecordList                 # 交易记录列表
PositionRecord                  # 持仓记录
PositionRecordList              # 持仓记录列表
```

## 📊 数据管理层

### StockManager (股票管理器)
```python
class StockManager:
    """股票管理器单例类"""

    @staticmethod
    def instance() -> StockManager:
        """获取全局股票管理器实例"""

    def __getitem__(self, code: str) -> Stock:
        """通过股票代码获取股票对象
        Args:
            code: 股票代码，如'sz000001', 'sh600000'
        Returns:
            Stock: 股票对象
        """

    def get_stock_list(self) -> list[Stock]:
        """获取所有股票列表
        Returns:
            list[Stock]: 股票对象列表
        """

    def get_market_info(self) -> dict:
        """获取市场信息
        Returns:
            dict: 市场信息字典
        """

    def __len__(self) -> int:
        """获取股票总数"""

# 全局实例
sm: StockManager = StockManager.instance()
```

### Stock (股票对象)
```python
class Stock:
    """股票对象类"""

    # 构造函数
    def __init__(self, market: str = "", code: str = "", name: str = ""):
        """初始化股票对象
        Args:
            market: 市场代码，如'SZ', 'SH'
            code: 股票代码，如'000001'
            name: 股票名称，如'平安银行'
        """

    # 基本属性 (只读)
    @property
    def name(self) -> str:
        """股票名称"""

    @property
    def code(self) -> str:
        """股票代码"""

    @property
    def market(self) -> str:
        """市场代码"""

    @property
    def market_code(self) -> str:
        """市场+代码，如'SZ000001'"""

    @property
    def type(self) -> int:
        """股票类型：1-A股, 2-指数, 3-B股等"""

    @property
    def start_datetime(self) -> Datetime:
        """上市日期"""

    @property
    def last_datetime(self) -> Datetime:
        """最后交易日期"""

    @property
    def tick(self) -> float:
        """最小价格变动单位"""

    @property
    def tick_value(self) -> float:
        """最小价格变动价值"""

    @property
    def precision(self) -> int:
        """价格精度"""

    @property
    def min_trade_number(self) -> float:
        """最小交易数量"""

    @property
    def max_trade_number(self) -> float:
        """最大交易数量"""

    # 数据获取方法
    def get_kdata(self, query: Query, ktype: KType = KType.DAY) -> KData:
        """获取K线数据
        Args:
            query: 查询条件
            ktype: K线类型，默认日线
        Returns:
            KData: K线数据对象
        """

    def get_finance_data(self) -> dict:
        """获取财务数据
        Returns:
            dict: 财务数据字典
        """

    def get_weight(self, start: Datetime = None, end: Datetime = None) -> list:
        """获取权重数据
        Args:
            start: 开始日期
            end: 结束日期
        Returns:
            list: 权重数据列表
        """

    def get_count(self, ktype: KType = KType.DAY) -> int:
        """获取K线数据总数
        Args:
            ktype: K线类型
        Returns:
            int: K线数据总数
        """

    # 比较操作
    def __eq__(self, other: Stock) -> bool:
        """相等比较"""

    def __ne__(self, other: Stock) -> bool:
        """不等比较"""

    def __str__(self) -> str:
        """字符串表示"""
```

### Query (查询对象)
```python
class Query:
    """K线数据查询对象"""

    # 查询类型枚举
    INDEX = 0    # 按索引查询
    DATE = 1     # 按日期查询

    def __init__(self, start: int | Datetime, end: int | Datetime = None):
        """初始化查询对象
        Args:
            start: 开始位置/日期，负数表示从末尾倒数
            end: 结束位置/日期，可选
        Examples:
            Query(-100)                    # 最近100个交易日
            Query(-250, -1)               # 最近250到最近1个交易日
            Query(Datetime(20240101), Datetime(20241231))  # 指定日期范围
        """

    @property
    def query_type(self) -> int:
        """查询类型：INDEX或DATE"""

    @property
    def start(self) -> int | Datetime:
        """开始位置/日期"""

    @property
    def end(self) -> int | Datetime:
        """结束位置/日期"""
```

### KType (K线类型)
```python
class KType:
    """K线类型枚举"""
    DAY = "DAY"         # 日线
    WEEK = "WEEK"       # 周线
    MONTH = "MONTH"     # 月线
    QUARTER = "QUARTER" # 季线
    HALFYEAR = "HALFYEAR" # 半年线
    YEAR = "YEAR"       # 年线
    MIN = "MIN"         # 1分钟线
    MIN5 = "MIN5"       # 5分钟线
    MIN15 = "MIN15"     # 15分钟线
    MIN30 = "MIN30"     # 30分钟线
    MIN60 = "MIN60"     # 60分钟线
```

### KData & KRecord (K线数据)
```python
class KRecord:
    """单条K线记录"""

    @property
    def datetime(self) -> Datetime:
        """交易日期时间"""

    @property
    def open(self) -> float:
        """开盘价"""

    @property
    def high(self) -> float:
        """最高价"""

    @property
    def low(self) -> float:
        """最低价"""

    @property
    def close(self) -> float:
        """收盘价"""

    @property
    def volume(self) -> float:
        """成交量"""

    @property
    def amount(self) -> float:
        """成交额"""

class KData:
    """K线数据集合"""

    def __len__(self) -> int:
        """K线数据条数"""

    def __getitem__(self, index: int) -> KRecord:
        """获取指定索引的K线记录
        Args:
            index: 索引位置，支持负数
        Returns:
            KRecord: K线记录
        """

    @property
    def start_pos(self) -> int:
        """起始位置"""

    @property
    def end_pos(self) -> int:
        """结束位置"""

    @property
    def last_pos(self) -> int:
        """最后位置"""

    def empty(self) -> bool:
        """是否为空"""

    def size(self) -> int:
        """数据大小"""
```

### 数据序列提取函数
```python
def CLOSE(kdata: KData) -> Indicator:
    """提取收盘价序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 收盘价指标对象
    """

def HIGH(kdata: KData) -> Indicator:
    """提取最高价序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 最高价指标对象
    """

def LOW(kdata: KData) -> Indicator:
    """提取最低价序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 最低价指标对象
    """

def OPEN(kdata: KData) -> Indicator:
    """提取开盘价序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 开盘价指标对象
    """

def VOL(kdata: KData) -> Indicator:
    """提取成交量序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 成交量指标对象
    """

def AMO(kdata: KData) -> Indicator:
    """提取成交额序列
    Args:
        kdata: K线数据
    Returns:
        Indicator: 成交额指标对象
    """
```

## 📈 技术指标层

### Indicator (指标基类)
```python
class Indicator:
    """技术指标基类"""

    def __len__(self) -> int:
        """指标数据长度"""

    def __getitem__(self, index: int) -> float:
        """获取指定位置的指标值
        Args:
            index: 索引位置，支持负数
        Returns:
            float: 指标值，无效值返回nan
        """

    def empty(self) -> bool:
        """是否为空"""

    def size(self) -> int:
        """数据大小"""

    def get_result(self, result_index: int = 0) -> Indicator:
        """获取指定输出结果
        Args:
            result_index: 结果索引，多输出指标使用
        Returns:
            Indicator: 指定的输出结果
        """

    def get_result_num(self) -> int:
        """获取输出结果数量"""

    # 指标运算支持
    def __add__(self, other: Indicator | float) -> Indicator:
        """加法运算"""

    def __sub__(self, other: Indicator | float) -> Indicator:
        """减法运算"""

    def __mul__(self, other: Indicator | float) -> Indicator:
        """乘法运算"""

    def __truediv__(self, other: Indicator | float) -> Indicator:
        """除法运算"""

    def __gt__(self, other: Indicator | float) -> Indicator:
        """大于比较"""

    def __lt__(self, other: Indicator | float) -> Indicator:
        """小于比较"""

    def __ge__(self, other: Indicator | float) -> Indicator:
        """大于等于比较"""

    def __le__(self, other: Indicator | float) -> Indicator:
        """小于等于比较"""

    def __eq__(self, other: Indicator | float) -> Indicator:
        """等于比较"""

    def __ne__(self, other: Indicator | float) -> Indicator:
        """不等于比较"""
```

### 趋势指标
```python
def MA(data: Indicator, n: int = 5) -> Indicator:
    """简单移动平均线
    Args:
        data: 输入数据序列
        n: 计算周期，默认5
    Returns:
        Indicator: MA指标
    """

def EMA(data: Indicator, n: int = 5) -> Indicator:
    """指数移动平均线
    Args:
        data: 输入数据序列
        n: 计算周期，默认5
    Returns:
        Indicator: EMA指标
    """

def SMA(data: Indicator, n: int = 5, m: float = 1.0) -> Indicator:
    """平滑移动平均线
    Args:
        data: 输入数据序列
        n: 计算周期，默认5
        m: 平滑系数，默认1.0
    Returns:
        Indicator: SMA指标
    """

def WMA(data: Indicator, n: int = 5) -> Indicator:
    """加权移动平均线
    Args:
        data: 输入数据序列
        n: 计算周期，默认5
    Returns:
        Indicator: WMA指标
    """

def MACD(data: Indicator, fast: int = 12, slow: int = 26, signal: int = 9) -> Indicator:
    """MACD指标
    Args:
        data: 输入数据序列
        fast: 快线周期，默认12
        slow: 慢线周期，默认26
        signal: 信号线周期，默认9
    Returns:
        Indicator: MACD指标，包含3个输出：MACD线、信号线、柱状图
    """

def ADX(kdata: KData, n: int = 14) -> Indicator:
    """平均趋向指数
    Args:
        kdata: K线数据
        n: 计算周期，默认14
    Returns:
        Indicator: ADX指标
    """

def TRIX(data: Indicator, n: int = 12) -> Indicator:
    """三重指数平滑移动平均
    Args:
        data: 输入数据序列
        n: 计算周期，默认12
    Returns:
        Indicator: TRIX指标
    """
```

### 震荡指标
```python
def RSI(data: Indicator, n: int = 14) -> Indicator:
    """相对强弱指标
    Args:
        data: 输入数据序列
        n: 计算周期，默认14
    Returns:
        Indicator: RSI指标，值域0-100
    """

def STOCHRSI(data: Indicator, n: int = 14, m: int = 3, p: int = 3) -> Indicator:
    """随机RSI指标
    Args:
        data: 输入数据序列
        n: RSI周期，默认14
        m: K值平滑周期，默认3
        p: D值平滑周期，默认3
    Returns:
        Indicator: 随机RSI指标
    """

def KDJ(kdata: KData, n: int = 9, m1: int = 3, m2: int = 3) -> Indicator:
    """KDJ随机指标
    Args:
        kdata: K线数据
        n: 计算周期，默认9
        m1: K值平滑周期，默认3
        m2: D值平滑周期，默认3
    Returns:
        Indicator: KDJ指标，包含3个输出：K值、D值、J值
    """

def CCI(kdata: KData, n: int = 14) -> Indicator:
    """顺势指标
    Args:
        kdata: K线数据
        n: 计算周期，默认14
    Returns:
        Indicator: CCI指标
    """

def WR(kdata: KData, n: int = 14) -> Indicator:
    """威廉指标
    Args:
        kdata: K线数据
        n: 计算周期，默认14
    Returns:
        Indicator: WR指标，值域-100到0
    """

def STOCH(kdata: KData, n: int = 14, m1: int = 3, m2: int = 3) -> Indicator:
    """随机指标
    Args:
        kdata: K线数据
        n: 计算周期，默认14
        m1: K值平滑周期，默认3
        m2: D值平滑周期，默认3
    Returns:
        Indicator: 随机指标，包含2个输出：K值、D值
    """
```

### 成交量指标
```python
def OBV(kdata: KData) -> Indicator:
    """能量潮指标
    Args:
        kdata: K线数据
    Returns:
        Indicator: OBV指标
    """

def AD(kdata: KData) -> Indicator:
    """聚散指标
    Args:
        kdata: K线数据
    Returns:
        Indicator: A/D指标
    """

def VRSI(kdata: KData, n: int = 14) -> Indicator:
    """成交量RSI指标
    Args:
        kdata: K线数据
        n: 计算周期，默认14
    Returns:
        Indicator: 成交量RSI指标
    """
```

### 价格指标
```python
def BOLL(data: Indicator, n: int = 20, p: float = 2.0) -> Indicator:
    """布林带指标
    Args:
        data: 输入数据序列
        n: 计算周期，默认20
        p: 标准差倍数，默认2.0
    Returns:
        Indicator: 布林带指标，包含3个输出：上轨、中轨、下轨
    """

def SAR(kdata: KData, step: float = 0.02, max_step: float = 0.2) -> Indicator:
    """抛物线指标
    Args:
        kdata: K线数据
        step: 步长，默认0.02
        max_step: 最大步长，默认0.2
    Returns:
        Indicator: SAR指标
    """

def ATR(kdata: KData, n: int = 14) -> Indicator:
    """真实波动幅度
    Args:
        kdata: K线数据
        n: 计算周期，默认14
    Returns:
        Indicator: ATR指标
    """

def BIAS(data: Indicator, n: int = 6) -> Indicator:
    """乖离率指标
    Args:
        data: 输入数据序列
        n: 计算周期，默认6
    Returns:
        Indicator: BIAS指标
    """
```

## 🎯 交易系统核心

### SignalBase (信号指示器基类)
```python
class SignalBase:
    """信号指示器基类"""

    def __init__(self):
        """初始化信号指示器"""

    @property
    def name(self) -> str:
        """信号指示器名称"""

    def name(self, name: str) -> None:
        """设置信号指示器名称"""

    def should_buy(self, datetime: Datetime) -> bool:
        """判断指定时间是否应该买入
        Args:
            datetime: 判断时间
        Returns:
            bool: True表示买入信号
        """

    def should_sell(self, datetime: Datetime) -> bool:
        """判断指定时间是否应该卖出
        Args:
            datetime: 判断时间
        Returns:
            bool: True表示卖出信号
        """

    def get_buy_signal(self) -> list[Datetime]:
        """获取所有买入信号时间点
        Returns:
            list[Datetime]: 买入信号时间列表
        """

    def get_sell_signal(self) -> list[Datetime]:
        """获取所有卖出信号时间点
        Returns:
            list[Datetime]: 卖出信号时间列表
        """

    def reset(self) -> None:
        """重置信号指示器"""

    def clone(self) -> 'SignalBase':
        """克隆信号指示器"""

    # 信号运算支持
    def __and__(self, other: 'SignalBase') -> 'SignalBase':
        """AND运算：两个信号都为真时才产生信号"""

    def __or__(self, other: 'SignalBase') -> 'SignalBase':
        """OR运算：任一信号为真时产生信号"""

    def __add__(self, other: 'SignalBase') -> 'SignalBase':
        """加法运算：信号叠加"""

    def __sub__(self, other: 'SignalBase') -> 'SignalBase':
        """减法运算：信号相减"""

# 类型别名
SignalPtr = SignalBase
SGPtr = SignalBase
```

### 信号指示器创建函数
```python
def SG_Cross(fast_line: Indicator, slow_line: Indicator) -> SignalBase:
    """双线交叉信号指示器
    Args:
        fast_line: 快线指标
        slow_line: 慢线指标
    Returns:
        SignalBase: 当快线上穿慢线时买入，下穿时卖出
    """

def SG_Band(indicator: Indicator, lower: float, upper: float) -> SignalBase:
    """区间突破信号指示器
    Args:
        indicator: 输入指标
        lower: 下轨数值
        upper: 上轨数值
    Returns:
        SignalBase: 当指标突破上轨时买入，跌破下轨时卖出
    """

def SG_Band(indicator: Indicator, lower: Indicator, upper: Indicator) -> SignalBase:
    """区间突破信号指示器（指标版）
    Args:
        indicator: 输入指标
        lower: 下轨指标
        upper: 上轨指标
    Returns:
        SignalBase: 当指标突破上轨指标时买入，跌破下轨指标时卖出
    """

def SG_Single(condition: Indicator) -> SignalBase:
    """单一条件信号指示器
    Args:
        condition: 条件指标，>0时买入，<=0时卖出
    Returns:
        SignalBase: 单一条件信号
    """

def SG_Bool(condition: Indicator) -> SignalBase:
    """布尔条件信号指示器
    Args:
        condition: 布尔条件指标
    Returns:
        SignalBase: 布尔条件信号
    """

def SG_Manual() -> SignalBase:
    """手动信号指示器
    Returns:
        SignalBase: 可手动添加买卖信号的指示器
    """

def SG_OneSide(indicator: Indicator, is_buy: bool) -> SignalBase:
    """单边信号指示器
    Args:
        indicator: 输入指标
        is_buy: True为买入信号，False为卖出信号
    Returns:
        SignalBase: 单边信号指示器
    """

def SG_Buy(indicator: Indicator) -> SignalBase:
    """单边买入信号指示器
    Args:
        indicator: 输入指标，>0时产生买入信号
    Returns:
        SignalBase: 单边买入信号
    """

def SG_Sell(indicator: Indicator) -> SignalBase:
    """单边卖出信号指示器
    Args:
        indicator: 输入指标，>0时产生卖出信号
    Returns:
        SignalBase: 单边卖出信号
    """
```

### TradeManager (交易管理器)
```python
class TradeManager:
    """交易管理器类"""

    def __init__(self, datetime: Datetime = Datetime(199001010000),
                 init_cash: float = 100000.0,
                 costfunc: 'TradeCostBase' = None,
                 name: str = "SYS"):
        """初始化交易管理器
        Args:
            datetime: 账户建立日期，默认1990-01-01
            init_cash: 初始资金，默认100000
            costfunc: 交易成本算法，默认零成本
            name: 账户名称，默认"SYS"
        """

    # 基本属性
    @property
    def init_cash(self) -> float:
        """初始资金"""

    @property
    def current_cash(self) -> float:
        """当前现金"""

    @property
    def init_datetime(self) -> Datetime:
        """账户建立日期"""

    @property
    def name(self) -> str:
        """账户名称"""

    # 交易操作
    def buy(self, datetime: Datetime, stock: Stock, real_price: float,
            number: float, stoploss: float = 0.0, goal_price: float = 0.0,
            plan_price: float = 0.0, part: str = "", remark: str = "") -> 'TradeRecord':
        """买入操作
        Args:
            datetime: 买入时间
            stock: 买入股票
            real_price: 实际买入价格
            number: 买入数量
            stoploss: 止损价，默认0.0
            goal_price: 目标价格，默认0.0
            plan_price: 计划买入价格，默认0.0
            part: 系统部件标识，默认""
            remark: 备注，默认""
        Returns:
            TradeRecord: 交易记录，失败时business为INVALID
        """

    def sell(self, datetime: Datetime, stock: Stock, real_price: float,
             number: float = 0.0, stoploss: float = 0.0, goal_price: float = 0.0,
             plan_price: float = 0.0, part: str = "", remark: str = "") -> 'TradeRecord':
        """卖出操作
        Args:
            datetime: 卖出时间
            stock: 卖出股票
            real_price: 实际卖出价格
            number: 卖出数量，0表示全部卖出
            stoploss: 止损价，默认0.0
            goal_price: 目标价格，默认0.0
            plan_price: 计划卖出价格，默认0.0
            part: 系统部件标识，默认""
            remark: 备注，默认""
        Returns:
            TradeRecord: 交易记录，失败时business为INVALID
        """

    # 信息查询
    def get_trade_list(self) -> list['TradeRecord']:
        """获取交易记录列表
        Returns:
            list[TradeRecord]: 所有交易记录
        """

    def get_position_list(self) -> list['PositionRecord']:
        """获取持仓记录列表
        Returns:
            list[PositionRecord]: 所有持仓记录
        """

    def get_funds_curve(self, dates: list[Datetime]) -> list[float]:
        """获取资金曲线
        Args:
            dates: 日期列表
        Returns:
            list[float]: 对应日期的资金值
        """

    def get_stock_number(self, datetime: Datetime, stock: Stock) -> float:
        """获取指定时间股票持仓数量
        Args:
            datetime: 查询时间
            stock: 查询股票
        Returns:
            float: 持仓数量
        """

    def get_hold_number(self, datetime: Datetime, stock: Stock) -> float:
        """获取指定时间股票可卖数量
        Args:
            datetime: 查询时间
            stock: 查询股票
        Returns:
            float: 可卖数量
        """

    def get_cash(self, datetime: Datetime, ktype: KType = KType.DAY) -> float:
        """获取指定时间现金
        Args:
            datetime: 查询时间
            ktype: K线类型
        Returns:
            float: 现金数量
        """

    # 账户状态
    def have(self, datetime: Datetime, stock: Stock) -> bool:
        """判断指定时间是否持有股票
        Args:
            datetime: 查询时间
            stock: 查询股票
        Returns:
            bool: True表示持有
        """

    def reset(self) -> None:
        """重置交易管理器"""

    def clone(self) -> 'TradeManager':
        """克隆交易管理器"""

def crtTM(datetime: Datetime = Datetime(199001010000),
          init_cash: float = 100000.0,
          costfunc: 'TradeCostBase' = None,
          name: str = "SYS") -> TradeManager:
    """创建交易管理器
    Args:
        datetime: 账户建立日期，默认1990-01-01
        init_cash: 初始资金，默认100000
        costfunc: 交易成本算法，默认零成本
        name: 账户名称，默认"SYS"
    Returns:
        TradeManager: 交易管理器实例
    """
```

### TradeRecord (交易记录)
```python
class TradeRecord:
    """交易记录类"""

    @property
    def datetime(self) -> Datetime:
        """交易时间"""

    @property
    def stock(self) -> Stock:
        """交易股票"""

    @property
    def business(self) -> int:
        """交易类型：0-初始化, 1-买入, 2-卖出等"""

    @property
    def real_price(self) -> float:
        """实际成交价格"""

    @property
    def plan_price(self) -> float:
        """计划价格"""

    @property
    def goal_price(self) -> float:
        """目标价格"""

    @property
    def number(self) -> float:
        """成交数量"""

    @property
    def cost(self) -> float:
        """交易成本"""

    @property
    def stoploss(self) -> float:
        """止损价"""

    @property
    def cash(self) -> float:
        """现金余额"""

    @property
    def part(self) -> str:
        """系统部件标识"""

    @property
    def remark(self) -> str:
        """备注信息"""

class PositionRecord:
    """持仓记录类"""

    @property
    def stock(self) -> Stock:
        """持仓股票"""

    @property
    def take_datetime(self) -> Datetime:
        """建仓时间"""

    @property
    def clean_datetime(self) -> Datetime:
        """清仓时间"""

    @property
    def number(self) -> float:
        """持仓数量"""

    @property
    def buy_money(self) -> float:
        """买入金额"""

    @property
    def sell_money(self) -> float:
        """卖出金额"""

    @property
    def profit_loss(self) -> float:
        """盈亏金额"""

    @property
    def profit_loss_rate(self) -> float:
        """盈亏比例"""
```

### MoneyManager (资金管理器)
```python
class MoneyManagerBase:
    """资金管理器基类"""

    def get_buy_number(self, datetime: Datetime, stock: Stock, price: float,
                       risk: float, part: str) -> float:
        """计算买入数量
        Args:
            datetime: 交易时间
            stock: 交易股票
            price: 交易价格
            risk: 风险度
            part: 系统部件标识
        Returns:
            float: 建议买入数量
        """

def MM_FixedCount(count: int) -> MoneyManagerBase:
    """固定数量资金管理器
    Args:
        count: 每次买入固定股数
    Returns:
        MoneyManagerBase: 固定数量资金管理器
    """

def MM_FixedPercent(percent: float) -> MoneyManagerBase:
    """固定比例资金管理器
    Args:
        percent: 每次投入资金比例，如0.1表示10%
    Returns:
        MoneyManagerBase: 固定比例资金管理器
    """

def MM_FixedCash(cash: float) -> MoneyManagerBase:
    """固定金额资金管理器
    Args:
        cash: 每次投入固定金额
    Returns:
        MoneyManagerBase: 固定金额资金管理器
    """

def MM_Kelly() -> MoneyManagerBase:
    """凯利公式资金管理器
    Returns:
        MoneyManagerBase: 凯利公式资金管理器
    """

def MM_FixedCapitalFunds() -> MoneyManagerBase:
    """固定资本资金管理器
    Returns:
        MoneyManagerBase: 固定资本资金管理器
    """
```

### System (交易系统)
```python
class System:
    """交易系统类"""

    def __init__(self, tm: TradeManager = None, sg: SignalBase = None,
                 mm: MoneyManagerBase = None, ev: 'EnvironmentBase' = None,
                 cn: 'ConditionBase' = None, st: 'StoplossBase' = None,
                 tp: 'TakeProfitBase' = None, pg: 'ProfitGoalBase' = None,
                 sp: 'SlippageBase' = None):
        """初始化交易系统
        Args:
            tm: 交易管理器
            sg: 信号指示器
            mm: 资金管理器
            ev: 市场环境判断器，可选
            cn: 系统条件判断器，可选
            st: 止损策略，可选
            tp: 止盈策略，可选
            pg: 盈利目标，可选
            sp: 滑点算法，可选
        """

    @property
    def tm(self) -> TradeManager:
        """交易管理器"""

    @property
    def sg(self) -> SignalBase:
        """信号指示器"""

    @property
    def mm(self) -> MoneyManagerBase:
        """资金管理器"""

    def run(self, stock: Stock, query: Query) -> None:
        """运行回测
        Args:
            stock: 回测股票
            query: 回测时间范围
        """

    def run_mo(self, stocks: list[Stock], query: Query) -> None:
        """多股票回测
        Args:
            stocks: 股票列表
            query: 回测时间范围
        """

    def reset(self) -> None:
        """重置交易系统"""

    def clone(self) -> 'System':
        """克隆交易系统"""

    def get_trade_record_list(self) -> list[TradeRecord]:
        """获取交易记录列表"""

    def get_buy_trade_request(self, datetime: Datetime) -> 'TradeRequest':
        """获取买入交易请求"""

    def get_sell_trade_request(self, datetime: Datetime) -> 'TradeRequest':
        """获取卖出交易请求"""

def SYS_Simple(tm: TradeManager = None, sg: SignalBase = None,
               mm: MoneyManagerBase = None, ev: 'EnvironmentBase' = None,
               cn: 'ConditionBase' = None, st: 'StoplossBase' = None,
               tp: 'TakeProfitBase' = None, pg: 'ProfitGoalBase' = None,
               sp: 'SlippageBase' = None) -> System:
    """创建简单交易系统
    Args:
        tm: 交易管理器
        sg: 信号指示器
        mm: 资金管理器
        ev: 市场环境判断器，可选
        cn: 系统条件判断器，可选
        st: 止损策略，可选
        tp: 止盈策略，可选
        pg: 盈利目标，可选
        sp: 滑点算法，可选
    Returns:
        System: 交易系统实例
    """
```

## 🛡️ 风险控制层

### StoplossBase (止损策略基类)
```python
class StoplossBase:
    """止损策略基类"""

    def get_price(self, datetime: Datetime, price: float) -> float:
        """获取止损价格
        Args:
            datetime: 当前时间
            price: 当前价格
        Returns:
            float: 止损价格
        """

    def reset(self) -> None:
        """重置止损策略"""

    def clone(self) -> 'StoplossBase':
        """克隆止损策略"""

def ST_FixedPercent(percent: float) -> StoplossBase:
    """固定百分比止损策略
    Args:
        percent: 止损百分比，如0.05表示5%止损
    Returns:
        StoplossBase: 固定百分比止损策略
    """

def ST_Indicator(indicator: Indicator) -> StoplossBase:
    """指标止损策略
    Args:
        indicator: 止损指标
    Returns:
        StoplossBase: 指标止损策略
    """

def ST_Saftyloss() -> StoplossBase:
    """安全止损策略
    Returns:
        StoplossBase: 安全止损策略
    """
```

### TakeProfitBase (止盈策略基类)
```python
class TakeProfitBase:
    """止盈策略基类"""

    def get_price(self, datetime: Datetime, price: float) -> float:
        """获取止盈价格
        Args:
            datetime: 当前时间
            price: 当前价格
        Returns:
            float: 止盈价格
        """

    def reset(self) -> None:
        """重置止盈策略"""

    def clone(self) -> 'TakeProfitBase':
        """克隆止盈策略"""

def TP_FixedPercent(percent: float) -> TakeProfitBase:
    """固定百分比止盈策略
    Args:
        percent: 止盈百分比，如0.10表示10%止盈
    Returns:
        TakeProfitBase: 固定百分比止盈策略
    """

def TP_Indicator(indicator: Indicator) -> TakeProfitBase:
    """指标止盈策略
    Args:
        indicator: 止盈指标
    Returns:
        TakeProfitBase: 指标止盈策略
    """
```

### EnvironmentBase (市场环境判断基类)
```python
class EnvironmentBase:
    """市场环境判断基类"""

    def is_valid(self, datetime: Datetime) -> bool:
        """判断指定时间市场环境是否有效
        Args:
            datetime: 判断时间
        Returns:
            bool: True表示市场环境有效，可以交易
        """

    def reset(self) -> None:
        """重置环境判断器"""

    def clone(self) -> 'EnvironmentBase':
        """克隆环境判断器"""

def EV_Bool(condition: Indicator) -> EnvironmentBase:
    """布尔条件环境判断器
    Args:
        condition: 布尔条件指标，>0时市场环境有效
    Returns:
        EnvironmentBase: 布尔条件环境判断器
    Example:
        # 只在牛市交易（20日均线在60日均线之上）
        ev = EV_Bool(MA(CLOSE(kdata), 20) > MA(CLOSE(kdata), 60))
    """
```

### ConditionBase (系统条件基类)
```python
class ConditionBase:
    """系统条件基类"""

    def is_valid(self, datetime: Datetime) -> bool:
        """判断指定时间系统条件是否有效
        Args:
            datetime: 判断时间
        Returns:
            bool: True表示系统条件有效
        """

    def reset(self) -> None:
        """重置条件判断器"""

    def clone(self) -> 'ConditionBase':
        """克隆条件判断器"""

def CN_Bool(condition: Indicator) -> ConditionBase:
    """布尔条件系统判断器
    Args:
        condition: 布尔条件指标，>0时系统条件有效
    Returns:
        ConditionBase: 布尔条件系统判断器
    Example:
        # 只在RSI不超买时启用系统
        cn = CN_Bool(RSI(CLOSE(kdata), 14) < 80)
    """
```

### ProfitGoalBase (盈利目标基类)
```python
class ProfitGoalBase:
    """盈利目标基类"""

    def get_goal(self, datetime: Datetime, price: float) -> float:
        """获取盈利目标价格
        Args:
            datetime: 当前时间
            price: 当前价格
        Returns:
            float: 盈利目标价格
        """

    def reset(self) -> None:
        """重置盈利目标"""

    def clone(self) -> 'ProfitGoalBase':
        """克隆盈利目标"""

def PG_FixedPercent(percent: float) -> ProfitGoalBase:
    """固定百分比盈利目标
    Args:
        percent: 盈利目标百分比，如0.20表示20%盈利目标
    Returns:
        ProfitGoalBase: 固定百分比盈利目标
    """

def PG_NoGoal() -> ProfitGoalBase:
    """无盈利目标
    Returns:
        ProfitGoalBase: 无盈利目标策略
    """
```

### SlippageBase (滑点算法基类)
```python
class SlippageBase:
    """滑点算法基类"""

    def get_real_buy_price(self, datetime: Datetime, plan_price: float) -> float:
        """获取实际买入价格
        Args:
            datetime: 交易时间
            plan_price: 计划价格
        Returns:
            float: 实际成交价格
        """

    def get_real_sell_price(self, datetime: Datetime, plan_price: float) -> float:
        """获取实际卖出价格
        Args:
            datetime: 交易时间
            plan_price: 计划价格
        Returns:
            float: 实际成交价格
        """

    def reset(self) -> None:
        """重置滑点算法"""

    def clone(self) -> 'SlippageBase':
        """克隆滑点算法"""

def SP_FixedPercent(percent: float) -> SlippageBase:
    """固定百分比滑点算法
    Args:
        percent: 滑点百分比，如0.001表示0.1%滑点
    Returns:
        SlippageBase: 固定百分比滑点算法
    """

def SP_FixedValue(value: float) -> SlippageBase:
    """固定数值滑点算法
    Args:
        value: 固定滑点数值
    Returns:
        SlippageBase: 固定数值滑点算法
    """
```

## 🚀 高级策略层

### 多因子模型 (MultiFactor)
```python
# 创建多因子模型
mf = MF_MultiFactor()

# 添加因子
mf.add_factor('ROE', FINANCE('roe'))
mf.add_factor('PE', 1/FINANCE('pe'))

# 获取评分
scores = mf.get_scores(datetime)
```

### 选股器 (Selector)
```python
# 固定选股
SE_Fixed(stock_list, system)

# 多因子选股
SE_MultiFactor(multifactor, topn=10)

# 信号选股
SE_Signal(signal, topn=10)
```

### 投资组合 (Portfolio)
```python
# 简单投资组合
pf = PF_Simple(
    tm=trade_manager,
    af=allocate_funds,
    se=selector
)

# 运行组合
pf.run(query)
```

### 资金分配器 (AllocateFunds)
```python
AF_EqualWeight()               # 等权重分配
AF_FixedWeight(weights)        # 固定权重分配
AF_MultiFactor(multifactor)    # 多因子权重分配
```

## 📊 分析工具层

### 绩效分析
```python
# 绩效对象
perf = Performance()

# 绩效指标
perf.get_return()              # 获取收益率
perf.get_max_drawdown()        # 获取最大回撤
perf.get_sharpe_ratio()        # 获取夏普比率
perf.get_win_rate()            # 获取胜率
```

### 回测分析
```python
# 事件驱动回测
from hikyuu.backtest import backtest

# 滚动优化系统
WalkForwardSystem()
```

## 🔧 工具函数层

### Datetime (日期时间类)
```python
class Datetime:
    """日期时间类"""

    def __init__(self, year: int = 1400, month: int = 1, day: int = 1,
                 hour: int = 0, minute: int = 0, second: int = 0):
        """通过年月日时分秒构造
        Args:
            year: 年份，默认1400
            month: 月份，默认1
            day: 日期，默认1
            hour: 小时，默认0
            minute: 分钟，默认0
            second: 秒，默认0
        """

    def __init__(self, datetime_number: int):
        """通过数字构造日期时间
        Args:
            datetime_number: 格式为yyyyMMddHHmmss的整数
        Example:
            Datetime(202501011030)  # 2025年1月1日10:30:00
        """

    # 属性
    @property
    def year(self) -> int:
        """年份"""

    @property
    def month(self) -> int:
        """月份"""

    @property
    def day(self) -> int:
        """日期"""

    @property
    def hour(self) -> int:
        """小时"""

    @property
    def minute(self) -> int:
        """分钟"""

    @property
    def second(self) -> int:
        """秒"""

    @property
    def number(self) -> int:
        """数字表示，格式yyyyMMddHHmmss"""

    # 方法
    def __str__(self) -> str:
        """字符串表示"""

    def __add__(self, days: int) -> 'Datetime':
        """日期加法"""

    def __sub__(self, other: 'Datetime' | int) -> 'Datetime' | int:
        """日期减法"""

    def __eq__(self, other: 'Datetime') -> bool:
        """相等比较"""

    def __lt__(self, other: 'Datetime') -> bool:
        """小于比较"""

    def __le__(self, other: 'Datetime') -> bool:
        """小于等于比较"""

    def __gt__(self, other: 'Datetime') -> bool:
        """大于比较"""

    def __ge__(self, other: 'Datetime') -> bool:
        """大于等于比较"""

    def is_null(self) -> bool:
        """是否为空日期"""

    def date(self) -> 'Datetime':
        """获取日期部分（时间设为0）"""

    def start_of_week(self) -> 'Datetime':
        """获取所在周的开始日期"""

    def start_of_month(self) -> 'Datetime':
        """获取所在月的开始日期"""

    def start_of_quarter(self) -> 'Datetime':
        """获取所在季度的开始日期"""

    def start_of_halfyear(self) -> 'Datetime':
        """获取所在半年的开始日期"""

    def start_of_year(self) -> 'Datetime':
        """获取所在年的开始日期"""

class DatetimeList:
    """日期时间列表类"""

    def __init__(self):
        """初始化空列表"""

    def append(self, datetime: Datetime) -> None:
        """添加日期时间
        Args:
            datetime: 要添加的日期时间
        """

    def __len__(self) -> int:
        """列表长度"""

    def __getitem__(self, index: int) -> Datetime:
        """获取指定索引的日期时间
        Args:
            index: 索引位置
        Returns:
            Datetime: 日期时间对象
        """

    def empty(self) -> bool:
        """是否为空"""

    def size(self) -> int:
        """列表大小"""

# 特殊日期时间常量
null_datetime: Datetime  # 空日期时间
```

### 序列化函数
```python
def hku_save(obj: Any, filename: str) -> None:
    """保存对象到文件
    Args:
        obj: 要保存的对象
        filename: 文件名
    """

def hku_load(filename: str) -> Any:
    """从文件加载对象
    Args:
        filename: 文件名
    Returns:
        Any: 加载的对象
    """
```

### 数据转换函数
```python
def to_df(trade_list: list[TradeRecord]) -> 'DataFrame':
    """将交易记录列表转换为pandas DataFrame
    Args:
        trade_list: 交易记录列表
    Returns:
        DataFrame: pandas DataFrame对象
    """

def to_np(position_list: list[PositionRecord]) -> 'ndarray':
    """将持仓记录列表转换为numpy数组
    Args:
        position_list: 持仓记录列表
    Returns:
        ndarray: numpy数组
    """
```

### 系统配置函数
```python
def load_hikyuu(config_file: str = None, **kwargs) -> None:
    """初始化Hikyuu系统
    Args:
        config_file: 配置文件路径，可选
        **kwargs: 其他配置参数
    """

def hikyuu_init(config_file: str) -> None:
    """初始化Hikyuu系统（旧版本兼容）
    Args:
        config_file: 配置文件路径
    """

def get_global_context() -> KData:
    """获取全局上下文K线数据
    Returns:
        KData: 全局K线数据
    """

def set_global_context(kdata: KData) -> None:
    """设置全局上下文K线数据
    Args:
        kdata: K线数据
    """

def get_config_file() -> str:
    """获取配置文件路径
    Returns:
        str: 配置文件路径
    """

def get_data_dir() -> str:
    """获取数据目录路径
    Returns:
        str: 数据目录路径
    """
```

## 📝 使用示例

### 完整策略示例
```python
from hikyuu.interactive import *

# 1. 获取股票和数据
stock = sm['sz000001']
kdata = stock.get_kdata(Query(-250))

# 2. 计算技术指标
close_data = CLOSE(kdata)
ma5 = MA(close_data, 5)
ma20 = MA(close_data, 20)
rsi = RSI(close_data, 14)

# 3. 创建信号
sg = SG_Cross(ma5, ma20) & SG_Band(rsi, 30, 70)

# 4. 创建交易管理器
tm = crtTM(init_cash=100000)

# 5. 创建资金管理器
mm = MM_FixedCount(1000)

# 6. 创建风险控制
st = ST_FixedPercent(0.05)  # 5%止损

# 7. 创建交易系统
sys = SYS_Simple(tm=tm, sg=sg, mm=mm, st=st)

# 8. 运行回测
sys.run(stock, Query(-250))

# 9. 分析结果
print(f"收益率: {(tm.current_cash - tm.init_cash) / tm.init_cash * 100:.2f}%")
print(f"交易次数: {len(tm.get_trade_list())}")
```

## 📋 常量和枚举

### BUSINESS (业务类型枚举)
```python
class BUSINESS:
    """交易业务类型枚举"""
    INIT = 0        # 初始化
    BUY = 1         # 买入
    SELL = 2        # 卖出
    GIFT = 3        # 送股
    BONUS = 4       # 分红
    CHECKIN = 5     # 存入现金
    CHECKOUT = 6    # 取出现金
    INVALID = 7     # 无效交易
```

### SystemPart (系统部件标识)
```python
class SystemPart:
    """系统部件标识常量"""
    ENVIRONMENT = "EV"      # 市场环境判断
    CONDITION = "CN"        # 系统条件
    SIGNAL = "SG"          # 信号指示器
    STOPLOSS = "ST"        # 止损策略
    TAKEPROFIT = "TP"      # 止盈策略
    MONEYMANAGER = "MM"    # 资金管理
    PROFITGOAL = "PG"      # 盈利目标
    SLIPPAGE = "SP"        # 滑点算法
```

### 特殊值常量
```python
# 特殊数值常量
null_datetime: Datetime     # 空日期时间 (1400-01-01)
null_price: float          # 空价格 (0.0)
null_int: int             # 空整数 (-1)
null_size: int            # 空大小 (0)

# 最大最小值
constant.max_double: float    # 最大浮点数
constant.min_double: float    # 最小浮点数
constant.max_int: int        # 最大整数
constant.min_int: int        # 最小整数
```

### 股票类型枚举
```python
class StockType:
    """股票类型枚举"""
    A = 1           # A股
    INDEX = 2       # 指数
    B = 3           # B股
    FUND = 4        # 基金
    ETF = 5         # ETF
    ND = 6          # 国债
    BOND = 7        # 其他债券
    GEM = 8         # 创业板
```

### 市场代码枚举
```python
class Market:
    """市场代码枚举"""
    SH = "SH"       # 上海证券交易所
    SZ = "SZ"       # 深圳证券交易所
    BJ = "BJ"       # 北京证券交易所
```

## 📝 完整使用示例

### 高级策略示例
```python
from hikyuu.interactive import *

def advanced_strategy_example():
    """高级策略示例：多指标组合+风险控制"""

    # 1. 获取股票和数据
    stock = sm['sz000001']
    kdata = stock.get_kdata(Query(-500))

    # 2. 计算多个技术指标
    close_data = CLOSE(kdata)
    high_data = HIGH(kdata)
    low_data = LOW(kdata)

    # 趋势指标
    ma5 = MA(close_data, 5)
    ma20 = MA(close_data, 20)
    ma60 = MA(close_data, 60)
    macd = MACD(close_data)

    # 震荡指标
    rsi = RSI(close_data, 14)
    kdj = KDJ(kdata, 9, 3, 3)

    # 成交量指标
    vol_data = VOL(kdata)
    vol_ma = MA(vol_data, 20)

    # 3. 创建复合信号
    # 趋势信号：短期均线上穿长期均线
    trend_signal = SG_Cross(ma5, ma20)

    # 震荡信号：RSI从超卖区域回升
    rsi_signal = SG_Band(rsi, 30, 70)

    # 成交量信号：成交量放大
    vol_signal = SG_Single(vol_data > vol_ma * 1.5)

    # 组合信号：趋势+震荡+成交量
    combined_signal = trend_signal & rsi_signal & vol_signal

    # 4. 市场环境判断：只在中长期趋势向上时交易
    market_env = EV_Bool(ma20 > ma60)

    # 5. 系统条件：避免在极端市场条件下交易
    system_condition = CN_Bool(rsi < 80)  # RSI不超买

    # 6. 风险控制
    stop_loss = ST_FixedPercent(0.05)      # 5%止损
    take_profit = TP_FixedPercent(0.15)    # 15%止盈
    slippage = SP_FixedPercent(0.001)      # 0.1%滑点

    # 7. 资金管理：固定比例
    money_mgr = MM_FixedPercent(0.2)       # 每次投入20%资金

    # 8. 创建交易管理器
    trade_mgr = crtTM(
        init_cash=1000000,                 # 100万初始资金
        datetime=Datetime(20200101),       # 从2020年开始
        name="AdvancedStrategy"
    )

    # 9. 构建完整交易系统
    system = SYS_Simple(
        tm=trade_mgr,
        sg=combined_signal,
        mm=money_mgr,
        ev=market_env,
        cn=system_condition,
        st=stop_loss,
        tp=take_profit,
        sp=slippage
    )

    # 10. 运行回测
    system.run(stock, Query(-500))

    # 11. 分析结果
    final_cash = trade_mgr.current_cash
    total_return = final_cash - trade_mgr.init_cash
    return_rate = (total_return / trade_mgr.init_cash) * 100

    trades = trade_mgr.get_trade_list()
    positions = trade_mgr.get_position_list()

    print(f"=== 高级策略回测结果 ===")
    print(f"初始资金: {trade_mgr.init_cash:,.0f}")
    print(f"最终资金: {final_cash:,.0f}")
    print(f"总收益: {total_return:,.0f}")
    print(f"收益率: {return_rate:.2f}%")
    print(f"交易次数: {len(trades)}")
    print(f"持仓记录: {len(positions)}")

    # 12. 详细分析
    if len(trades) > 0:
        buy_trades = [t for t in trades if t.business == BUSINESS.BUY]
        sell_trades = [t for t in trades if t.business == BUSINESS.SELL]

        print(f"买入次数: {len(buy_trades)}")
        print(f"卖出次数: {len(sell_trades)}")

        if len(positions) > 0:
            profit_positions = [p for p in positions if p.profit_loss > 0]
            loss_positions = [p for p in positions if p.profit_loss < 0]

            win_rate = len(profit_positions) / len(positions) * 100
            avg_profit = sum(p.profit_loss for p in profit_positions) / len(profit_positions) if profit_positions else 0
            avg_loss = sum(p.profit_loss for p in loss_positions) / len(loss_positions) if loss_positions else 0

            print(f"胜率: {win_rate:.1f}%")
            print(f"平均盈利: {avg_profit:.0f}")
            print(f"平均亏损: {avg_loss:.0f}")

    return system, trade_mgr

if __name__ == "__main__":
    system, tm = advanced_strategy_example()
```

## 🎯 最佳实践建议

### 1. 代码组织
- 将策略逻辑封装成类
- 使用配置文件管理参数
- 建立模块化的代码结构

### 2. 参数管理
- 避免硬编码参数
- 使用参数优化工具
- 进行敏感性分析

### 3. 风险控制
- 始终设置止损
- 控制单次交易风险
- 监控最大回撤

### 4. 回测验证
- 使用样本外数据验证
- 考虑交易成本
- 进行压力测试

### 5. 性能优化
- 缓存计算结果
- 避免重复计算
- 使用向量化操作

---

*本参考手册基于Hikyuu 2.6.5版本编写，涵盖了框架的主要功能和API。*
*包含详细的类型信息、方法签名、参数说明和完整示例。*
*更多详细信息请参考官方文档：https://hikyuu.readthedocs.io/*
