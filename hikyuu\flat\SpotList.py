# automatically generated by the FlatBuffers compiler, do not modify

# namespace: flat

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class SpotList(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = SpotList()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSpotList(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # SpotList
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # SpotList
    def Spot(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from hikyuu.flat.Spot import Spot
            obj = Spot()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SpotList
    def SpotLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SpotList
    def SpotIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

def SpotListStart(builder):
    builder.StartObject(1)

def Start(builder):
    SpotListStart(builder)

def SpotListAddSpot(builder, spot):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(spot), 0)

def AddSpot(builder, spot):
    SpotListAddSpot(builder, spot)

def SpotListStartSpotVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartSpotVector(builder, numElems):
    return SpotListStartSpotVector(builder, numElems)

def SpotListEnd(builder):
    return builder.EndObject()

def End(builder):
    return SpotListEnd(builder)
