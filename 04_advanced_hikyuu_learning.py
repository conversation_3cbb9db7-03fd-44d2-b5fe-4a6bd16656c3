#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hikyuu进阶学习：止损止盈、市场环境判断、多股票策略
"""

from hikyuu.interactive import *

def lesson6_stop_loss_profit():
    """第6课：止损止盈策略"""
    print("=" * 60)
    print("  🛡️ 第6课：止损止盈策略")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 150
    
    # 基础策略组件
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    mm = MM_FixedCount(1000)
    
    strategies = []
    
    # 1. 无止损止盈
    tm1 = crtTM(init_cash=init_cash)
    sys1 = SYS_Simple(tm=tm1, sg=sg, mm=mm)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("无止损止盈", ret1, len(tm1.get_trade_list())))
    
    # 2. 固定百分比止损
    tm2 = crtTM(init_cash=init_cash)
    st2 = ST_FixedPercent(0.05)  # 5%止损
    sys2 = SYS_Simple(tm=tm2, sg=sg, mm=mm, st=st2)
    sys2.run(stock, Query(-days))
    ret2 = ((tm2.current_cash - init_cash) / init_cash) * 100
    strategies.append(("5%固定止损", ret2, len(tm2.get_trade_list())))
    
    # 3. 固定百分比止盈
    tm3 = crtTM(init_cash=init_cash)
    tp3 = TP_FixedPercent(0.10)  # 10%止盈
    sys3 = SYS_Simple(tm=tm3, sg=sg, mm=mm, tp=tp3)
    sys3.run(stock, Query(-days))
    ret3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    strategies.append(("10%固定止盈", ret3, len(tm3.get_trade_list())))
    
    # 4. 止损+止盈组合
    tm4 = crtTM(init_cash=init_cash)
    st4 = ST_FixedPercent(0.05)  # 5%止损
    tp4 = TP_FixedPercent(0.08)  # 8%止盈
    sys4 = SYS_Simple(tm=tm4, sg=sg, mm=mm, st=st4, tp=tp4)
    sys4.run(stock, Query(-days))
    ret4 = ((tm4.current_cash - init_cash) / init_cash) * 100
    strategies.append(("5%止损+8%止盈", ret4, len(tm4.get_trade_list())))
    
    print("止损止盈策略比较:")
    for name, ret, trades in strategies:
        print(f"  {name:15}: 收益率 {ret:6.2f}%, 交易次数 {trades:2d}")
    
    best = max(strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return strategies

def lesson7_market_environment():
    """第7课：市场环境判断"""
    print("\n" + "=" * 60)
    print("  🌍 第7课：市场环境判断")
    print("=" * 60)
    
    stock = sm['sz000001']
    index_stock = sm['sh000001']  # 上证指数
    init_cash = 100000
    days = 200
    
    # 基础策略
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    mm = MM_FixedCount(1000)
    
    strategies = []
    
    # 1. 无市场环境判断
    tm1 = crtTM(init_cash=init_cash)
    sys1 = SYS_Simple(tm=tm1, sg=sg, mm=mm)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("无环境判断", ret1))
    
    # 2. 基于指数的市场环境
    try:
        index_kdata = index_stock.get_kdata(Query(-days))
        if len(index_kdata) > 20:
            index_close = CLOSE(index_kdata)
            index_ma20 = MA(index_close, 20)
            
            # 当指数在20日均线之上时才交易
            ev2 = EV_Bool(index_close > index_ma20)
            
            tm2 = crtTM(init_cash=init_cash)
            sys2 = SYS_Simple(tm=tm2, sg=sg, mm=mm, ev=ev2)
            sys2.run(stock, Query(-days))
            ret2 = ((tm2.current_cash - init_cash) / init_cash) * 100
            strategies.append(("指数趋势判断", ret2))
        else:
            strategies.append(("指数趋势判断", 0.0))
    except:
        strategies.append(("指数趋势判断", 0.0))
    
    # 3. 基于股票自身的环境判断
    stock_ma60 = MA(close_data, 60)
    ev3 = EV_Bool(close_data > stock_ma60)  # 股价在60日均线之上
    
    tm3 = crtTM(init_cash=init_cash)
    sys3 = SYS_Simple(tm=tm3, sg=sg, mm=mm, ev=ev3)
    sys3.run(stock, Query(-days))
    ret3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    strategies.append(("股价趋势判断", ret3))
    
    print("市场环境策略比较:")
    for name, ret in strategies:
        print(f"  {name:15}: 收益率 {ret:6.2f}%")
    
    best = max(strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return strategies

def lesson8_multi_stock_strategy():
    """第8课：多股票策略"""
    print("\n" + "=" * 60)
    print("  📊 第8课：多股票策略")
    print("=" * 60)
    
    # 选择几只测试股票
    stock_codes = ['sz000001', 'sz000002', 'sz000858']
    init_cash = 300000  # 增加初始资金
    days = 100
    
    print(f"测试股票池:")
    stocks = []
    for code in stock_codes:
        try:
            stock = sm[code]
            stocks.append(stock)
            print(f"  {stock.name} ({code})")
        except:
            print(f"  {code} - 无法获取")
    
    if len(stocks) == 0:
        print("❌ 无可用股票，跳过多股票测试")
        return []
    
    strategies = []
    
    # 1. 单股票策略（对比基准）
    stock = stocks[0]
    tm1 = crtTM(init_cash=init_cash)
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    mm = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg, mm=mm)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("单股票策略", ret1))
    
    # 2. 多股票轮换策略
    total_return = 0
    valid_stocks = 0
    
    for stock in stocks:
        try:
            tm = crtTM(init_cash=init_cash//len(stocks))  # 平均分配资金
            kdata = stock.get_kdata(Query(-days))
            if len(kdata) > 30:  # 确保有足够数据
                close_data = CLOSE(kdata)
                sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
                mm = MM_FixedCount(500)  # 减少每次买入量
                sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
                sys.run(stock, Query(-days))
                
                stock_return = ((tm.current_cash - (init_cash//len(stocks))) / (init_cash//len(stocks))) * 100
                total_return += stock_return
                valid_stocks += 1
                print(f"  {stock.name}: {stock_return:.2f}%")
        except Exception as e:
            print(f"  {stock.name}: 策略失败 - {e}")
    
    if valid_stocks > 0:
        avg_return = total_return / valid_stocks
        strategies.append(("多股票策略", avg_return))
    else:
        strategies.append(("多股票策略", 0.0))
    
    print(f"\n多股票策略比较:")
    for name, ret in strategies:
        print(f"  {name:15}: 收益率 {ret:6.2f}%")
    
    return strategies

def lesson9_custom_indicators():
    """第9课：自定义指标应用"""
    print("\n" + "=" * 60)
    print("  🔧 第9课：自定义指标应用")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 150
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    high_data = HIGH(kdata)
    low_data = LOW(kdata)
    
    strategies = []
    
    # 1. 基础双均线
    tm1 = crtTM(init_cash=init_cash)
    sg1 = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    mm = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg1, mm=mm)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("双均线策略", ret1))
    
    # 2. EMA策略
    tm2 = crtTM(init_cash=init_cash)
    sg2 = SG_Cross(EMA(close_data, 5), EMA(close_data, 20))
    sys2 = SYS_Simple(tm=tm2, sg=sg2, mm=mm)
    sys2.run(stock, Query(-days))
    ret2 = ((tm2.current_cash - init_cash) / init_cash) * 100
    strategies.append(("双EMA策略", ret2))
    
    # 3. 价格突破策略
    tm3 = crtTM(init_cash=init_cash)
    ma20 = MA(close_data, 20)
    # 价格突破20日均线
    sg3 = SG_Single(close_data > ma20)
    sys3 = SYS_Simple(tm=tm3, sg=sg3, mm=mm)
    sys3.run(stock, Query(-days))
    ret3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    strategies.append(("价格突破策略", ret3))
    
    # 4. 组合指标策略
    tm4 = crtTM(init_cash=init_cash)
    rsi = RSI(close_data, 14)
    ma_signal = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    rsi_signal = SG_Band(rsi, 30, 70)
    # 组合信号：双均线交叉 AND RSI条件
    combined_sg = ma_signal & rsi_signal
    sys4 = SYS_Simple(tm=tm4, sg=combined_sg, mm=mm)
    sys4.run(stock, Query(-days))
    ret4 = ((tm4.current_cash - init_cash) / init_cash) * 100
    strategies.append(("组合指标策略", ret4))
    
    print("自定义指标策略比较:")
    for name, ret in strategies:
        print(f"  {name:15}: 收益率 {ret:6.2f}%")
    
    best = max(strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return strategies

def lesson10_strategy_optimization():
    """第10课：策略参数优化"""
    print("\n" + "=" * 60)
    print("  ⚡ 第10课：策略参数优化")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 200
    
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    
    print("测试不同参数组合:")
    
    best_return = float('-inf')
    best_params = None
    results = []
    
    # 测试不同的均线组合
    fast_periods = [3, 5, 8, 10]
    slow_periods = [15, 20, 25, 30]
    
    for fast in fast_periods:
        for slow in slow_periods:
            if fast >= slow:
                continue
                
            try:
                tm = crtTM(init_cash=init_cash)
                sg = SG_Cross(MA(close_data, fast), MA(close_data, slow))
                mm = MM_FixedCount(1000)
                sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
                sys.run(stock, Query(-days))
                
                ret = ((tm.current_cash - init_cash) / init_cash) * 100
                trades = len(tm.get_trade_list())
                
                results.append((fast, slow, ret, trades))
                
                if ret > best_return:
                    best_return = ret
                    best_params = (fast, slow)
                    
            except Exception as e:
                print(f"  MA({fast},{slow}): 失败 - {e}")
    
    # 显示前5个最佳结果
    results.sort(key=lambda x: x[2], reverse=True)
    print(f"\n前5个最佳参数组合:")
    for i, (fast, slow, ret, trades) in enumerate(results[:5]):
        print(f"  {i+1}. MA({fast},{slow}): 收益率 {ret:6.2f}%, 交易次数 {trades}")
    
    if best_params:
        print(f"\n🏆 最佳参数: MA({best_params[0]},{best_params[1]}) - 收益率: {best_return:.2f}%")
    
    return results

def main():
    """进阶学习主流程"""
    print("🚀 Hikyuu进阶学习教程")
    print("=" * 60)
    
    # 第6课：止损止盈
    lesson6_stop_loss_profit()
    
    # 第7课：市场环境判断
    lesson7_market_environment()
    
    # 第8课：多股票策略
    lesson8_multi_stock_strategy()
    
    # 第9课：自定义指标
    lesson9_custom_indicators()
    
    # 第10课：策略优化
    lesson10_strategy_optimization()
    
    # 学习总结
    print("\n" + "=" * 60)
    print("  🎓 进阶学习总结")
    print("=" * 60)
    print("✅ 已完成的进阶内容:")
    print("  6. 止损止盈策略 - 风险控制方法")
    print("  7. 市场环境判断 - 择时交易技巧")
    print("  8. 多股票策略 - 分散投资方法")
    print("  9. 自定义指标 - 指标组合应用")
    print("  10. 策略参数优化 - 参数调优技术")
    
    print("\n📚 核心进阶概念:")
    print("  • ST/TP: 止损止盈策略")
    print("  • EV: 市场环境判断")
    print("  • 信号组合: & | 逻辑运算")
    print("  • 参数优化: 网格搜索")
    
    print("\n🎯 下一步高级学习:")
    print("  1. 多因子选股模型 (MF)")
    print("  2. 投资组合管理 (PF)")
    print("  3. 事件驱动回测")
    print("  4. 实盘交易接口")
    print("  5. 风险管理系统")
    
    print("\n💡 实战建议:")
    print("  • 结合基本面分析")
    print("  • 建立完整的交易系统")
    print("  • 进行样本外验证")
    print("  • 控制回撤和风险")
    
    print("=" * 60)
    print("🎉 恭喜完成Hikyuu进阶学习！")

if __name__ == "__main__":
    main()
