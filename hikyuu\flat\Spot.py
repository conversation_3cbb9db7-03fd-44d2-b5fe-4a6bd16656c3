# automatically generated by the FlatBuffers compiler, do not modify

# namespace: flat

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Spot(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Spot()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSpot(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Spot
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Spot
    def Market(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Spot
    def Code(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Spot
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Spot
    def Datetime(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Spot
    def YesterdayClose(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Open(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def High(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Low(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Close(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Volume(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(24))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid1(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(26))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid1Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(28))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid2(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(30))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid2Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(32))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid3(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(34))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid3Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(36))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid4(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(38))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid4Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(40))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid5(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(42))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Bid5Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(44))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask1(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(46))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask1Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(48))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask2(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(50))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask2Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(52))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask3(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(54))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask3Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(56))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask4(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(58))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask4Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(60))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask5(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(62))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

    # Spot
    def Ask5Amount(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(64))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float64Flags, o + self._tab.Pos)
        return 0.0

def SpotStart(builder):
    builder.StartObject(31)

def Start(builder):
    SpotStart(builder)

def SpotAddMarket(builder, market):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(market), 0)

def AddMarket(builder, market):
    SpotAddMarket(builder, market)

def SpotAddCode(builder, code):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(code), 0)

def AddCode(builder, code):
    SpotAddCode(builder, code)

def SpotAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def AddName(builder, name):
    SpotAddName(builder, name)

def SpotAddDatetime(builder, datetime):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(datetime), 0)

def AddDatetime(builder, datetime):
    SpotAddDatetime(builder, datetime)

def SpotAddYesterdayClose(builder, yesterdayClose):
    builder.PrependFloat64Slot(4, yesterdayClose, 0.0)

def AddYesterdayClose(builder, yesterdayClose):
    SpotAddYesterdayClose(builder, yesterdayClose)

def SpotAddOpen(builder, open):
    builder.PrependFloat64Slot(5, open, 0.0)

def AddOpen(builder, open):
    SpotAddOpen(builder, open)

def SpotAddHigh(builder, high):
    builder.PrependFloat64Slot(6, high, 0.0)

def AddHigh(builder, high):
    SpotAddHigh(builder, high)

def SpotAddLow(builder, low):
    builder.PrependFloat64Slot(7, low, 0.0)

def AddLow(builder, low):
    SpotAddLow(builder, low)

def SpotAddClose(builder, close):
    builder.PrependFloat64Slot(8, close, 0.0)

def AddClose(builder, close):
    SpotAddClose(builder, close)

def SpotAddAmount(builder, amount):
    builder.PrependFloat64Slot(9, amount, 0.0)

def AddAmount(builder, amount):
    SpotAddAmount(builder, amount)

def SpotAddVolume(builder, volume):
    builder.PrependFloat64Slot(10, volume, 0.0)

def AddVolume(builder, volume):
    SpotAddVolume(builder, volume)

def SpotAddBid1(builder, bid1):
    builder.PrependFloat64Slot(11, bid1, 0.0)

def AddBid1(builder, bid1):
    SpotAddBid1(builder, bid1)

def SpotAddBid1Amount(builder, bid1Amount):
    builder.PrependFloat64Slot(12, bid1Amount, 0.0)

def AddBid1Amount(builder, bid1Amount):
    SpotAddBid1Amount(builder, bid1Amount)

def SpotAddBid2(builder, bid2):
    builder.PrependFloat64Slot(13, bid2, 0.0)

def AddBid2(builder, bid2):
    SpotAddBid2(builder, bid2)

def SpotAddBid2Amount(builder, bid2Amount):
    builder.PrependFloat64Slot(14, bid2Amount, 0.0)

def AddBid2Amount(builder, bid2Amount):
    SpotAddBid2Amount(builder, bid2Amount)

def SpotAddBid3(builder, bid3):
    builder.PrependFloat64Slot(15, bid3, 0.0)

def AddBid3(builder, bid3):
    SpotAddBid3(builder, bid3)

def SpotAddBid3Amount(builder, bid3Amount):
    builder.PrependFloat64Slot(16, bid3Amount, 0.0)

def AddBid3Amount(builder, bid3Amount):
    SpotAddBid3Amount(builder, bid3Amount)

def SpotAddBid4(builder, bid4):
    builder.PrependFloat64Slot(17, bid4, 0.0)

def AddBid4(builder, bid4):
    SpotAddBid4(builder, bid4)

def SpotAddBid4Amount(builder, bid4Amount):
    builder.PrependFloat64Slot(18, bid4Amount, 0.0)

def AddBid4Amount(builder, bid4Amount):
    SpotAddBid4Amount(builder, bid4Amount)

def SpotAddBid5(builder, bid5):
    builder.PrependFloat64Slot(19, bid5, 0.0)

def AddBid5(builder, bid5):
    SpotAddBid5(builder, bid5)

def SpotAddBid5Amount(builder, bid5Amount):
    builder.PrependFloat64Slot(20, bid5Amount, 0.0)

def AddBid5Amount(builder, bid5Amount):
    SpotAddBid5Amount(builder, bid5Amount)

def SpotAddAsk1(builder, ask1):
    builder.PrependFloat64Slot(21, ask1, 0.0)

def AddAsk1(builder, ask1):
    SpotAddAsk1(builder, ask1)

def SpotAddAsk1Amount(builder, ask1Amount):
    builder.PrependFloat64Slot(22, ask1Amount, 0.0)

def AddAsk1Amount(builder, ask1Amount):
    SpotAddAsk1Amount(builder, ask1Amount)

def SpotAddAsk2(builder, ask2):
    builder.PrependFloat64Slot(23, ask2, 0.0)

def AddAsk2(builder, ask2):
    SpotAddAsk2(builder, ask2)

def SpotAddAsk2Amount(builder, ask2Amount):
    builder.PrependFloat64Slot(24, ask2Amount, 0.0)

def AddAsk2Amount(builder, ask2Amount):
    SpotAddAsk2Amount(builder, ask2Amount)

def SpotAddAsk3(builder, ask3):
    builder.PrependFloat64Slot(25, ask3, 0.0)

def AddAsk3(builder, ask3):
    SpotAddAsk3(builder, ask3)

def SpotAddAsk3Amount(builder, ask3Amount):
    builder.PrependFloat64Slot(26, ask3Amount, 0.0)

def AddAsk3Amount(builder, ask3Amount):
    SpotAddAsk3Amount(builder, ask3Amount)

def SpotAddAsk4(builder, ask4):
    builder.PrependFloat64Slot(27, ask4, 0.0)

def AddAsk4(builder, ask4):
    SpotAddAsk4(builder, ask4)

def SpotAddAsk4Amount(builder, ask4Amount):
    builder.PrependFloat64Slot(28, ask4Amount, 0.0)

def AddAsk4Amount(builder, ask4Amount):
    SpotAddAsk4Amount(builder, ask4Amount)

def SpotAddAsk5(builder, ask5):
    builder.PrependFloat64Slot(29, ask5, 0.0)

def AddAsk5(builder, ask5):
    SpotAddAsk5(builder, ask5)

def SpotAddAsk5Amount(builder, ask5Amount):
    builder.PrependFloat64Slot(30, ask5Amount, 0.0)

def AddAsk5Amount(builder, ask5Amount):
    SpotAddAsk5Amount(builder, ask5Amount)

def SpotEnd(builder):
    return builder.EndObject()

def End(builder):
    return SpotEnd(builder)
