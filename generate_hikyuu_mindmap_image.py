#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成Hikyuu思维导图图片
使用matplotlib和networkx创建可视化思维导图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_hikyuu_mindmap():
    """创建Hikyuu思维导图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    # 定义颜色
    colors = {
        'root': '#2E86AB',
        'data': '#A23B72', 
        'indicator': '#F18F01',
        'trading': '#C73E1D',
        'risk': '#8E44AD',
        'advanced': '#27AE60',
        'tools': '#34495E'
    }
    
    # 根节点
    root_box = FancyBboxPatch((8, 7), 4, 2, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['root'], 
                             edgecolor='white', 
                             linewidth=2)
    ax.add_patch(root_box)
    ax.text(10, 8, 'Hikyuu 2.6.5\n量化交易框架', 
            ha='center', va='center', fontsize=16, fontweight='bold', color='white')
    
    # 主要模块位置
    modules = [
        # (x, y, width, height, color, title, items)
        (1, 12, 3.5, 3, colors['data'], '数据管理层', [
            'StockManager', 'Stock对象', 'KData数据', 'Query查询'
        ]),
        (6, 12, 3.5, 3, colors['indicator'], '技术指标层', [
            'MA/EMA', 'RSI/KDJ', 'MACD/BOLL', 'OBV/ATR'
        ]),
        (11, 12, 3.5, 3, colors['trading'], '交易系统核心', [
            'Signal信号', 'TradeManager', 'MoneyManager', 'System系统'
        ]),
        (16, 12, 3.5, 3, colors['risk'], '风险控制层', [
            'Stoploss止损', 'TakeProfit止盈', 'Environment环境', 'Slippage滑点'
        ]),
        (1, 1, 3.5, 3, colors['advanced'], '高级策略层', [
            'MultiFactor', 'Selector选股', 'Portfolio组合', 'AllocateFunds'
        ]),
        (6, 1, 3.5, 3, colors['tools'], '工具扩展层', [
            'Datetime日期', '数据转换', '系统配置', '常量枚举'
        ])
    ]
    
    # 绘制模块
    for x, y, w, h, color, title, items in modules:
        # 主模块框
        module_box = FancyBboxPatch((x, y), w, h,
                                   boxstyle="round,pad=0.1",
                                   facecolor=color,
                                   edgecolor='white',
                                   linewidth=2,
                                   alpha=0.8)
        ax.add_patch(module_box)
        
        # 模块标题
        ax.text(x + w/2, y + h - 0.3, title, 
                ha='center', va='center', fontsize=12, fontweight='bold', color='white')
        
        # 模块项目
        for i, item in enumerate(items):
            ax.text(x + w/2, y + h - 0.8 - i*0.4, f'• {item}', 
                    ha='center', va='center', fontsize=9, color='white')
    
    # 绘制连接线
    connections = [
        # 从根节点到各模块的连接
        ((10, 9), (2.75, 12)),    # 到数据管理层
        ((10, 9), (7.75, 12)),    # 到技术指标层
        ((10, 9), (12.75, 12)),   # 到交易系统核心
        ((10, 9), (17.75, 12)),   # 到风险控制层
        ((10, 7), (2.75, 4)),     # 到高级策略层
        ((10, 7), (7.75, 4)),     # 到工具扩展层
    ]
    
    for start, end in connections:
        ax.plot([start[0], end[0]], [start[1], end[1]], 
                'k-', linewidth=2, alpha=0.6)
    
    # 添加核心流程说明
    flow_box = FancyBboxPatch((11, 5), 8, 3,
                             boxstyle="round,pad=0.1",
                             facecolor='#ECF0F1',
                             edgecolor='#BDC3C7',
                             linewidth=1)
    ax.add_patch(flow_box)
    
    ax.text(15, 7.5, '核心使用流程', ha='center', va='center', 
            fontsize=12, fontweight='bold', color='#2C3E50')
    
    flow_steps = [
        '1. 数据获取: sm["sz000001"].get_kdata()',
        '2. 指标计算: MA(CLOSE(kdata), 5)',
        '3. 信号生成: SG_Cross(ma5, ma20)',
        '4. 系统构建: SYS_Simple(tm, sg, mm)',
        '5. 回测运行: system.run(stock, query)',
        '6. 结果分析: tm.get_trade_list()'
    ]
    
    for i, step in enumerate(flow_steps):
        ax.text(11.2, 7 - i*0.25, step, ha='left', va='center', 
                fontsize=9, color='#2C3E50')
    
    # 添加标题
    ax.text(10, 15.5, 'Hikyuu 量化交易框架思维导图', 
            ha='center', va='center', fontsize=20, fontweight='bold', color='#2C3E50')
    
    # 添加版本信息
    ax.text(19, 0.5, 'Version: 2.6.5', ha='right', va='center', 
            fontsize=10, color='#7F8C8D', style='italic')
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('hikyuu_mindmap.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('hikyuu_mindmap.jpg', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("✅ 思维导图已生成:")
    print("   📄 hikyuu_mindmap.png (PNG格式)")
    print("   📄 hikyuu_mindmap.jpg (JPG格式)")
    
    # 显示图片
    plt.show()
    
    return fig

def create_detailed_api_diagram():
    """创建详细的API结构图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(24, 18))
    ax.set_xlim(0, 24)
    ax.set_ylim(0, 18)
    ax.axis('off')
    
    # 标题
    ax.text(12, 17, 'Hikyuu 详细API结构图', 
            ha='center', va='center', fontsize=22, fontweight='bold', color='#2C3E50')
    
    # 定义API模块
    api_modules = [
        # (x, y, width, height, color, title, apis)
        (0.5, 13, 5, 3.5, '#3498DB', 'StockManager & Stock', [
            'sm = StockManager.instance()',
            'stock = sm["sz000001"]',
            'stock.name, stock.code',
            'kdata = stock.get_kdata(Query(-100))',
            'finance = stock.get_finance_data()'
        ]),
        (6.5, 13, 5, 3.5, '#E74C3C', 'KData & Indicators', [
            'close = CLOSE(kdata)',
            'ma5 = MA(close, 5)',
            'rsi = RSI(close, 14)',
            'macd = MACD(close)',
            'kdj = KDJ(kdata, 9, 3, 3)'
        ]),
        (12.5, 13, 5, 3.5, '#F39C12', 'Signal & Trading', [
            'sg = SG_Cross(ma5, ma20)',
            'tm = crtTM(init_cash=100000)',
            'mm = MM_FixedCount(1000)',
            'sys = SYS_Simple(tm, sg, mm)',
            'sys.run(stock, Query(-250))'
        ]),
        (18.5, 13, 5, 3.5, '#9B59B6', 'Risk & Advanced', [
            'st = ST_FixedPercent(0.05)',
            'tp = TP_FixedPercent(0.10)',
            'ev = EV_Bool(condition)',
            'mf = MF_MultiFactor()',
            'se = SE_Fixed(stocks, sys)'
        ]),
        (0.5, 8, 11, 4, '#27AE60', '核心类型定义', [
            'class Stock: name: str, code: str, get_kdata(Query, KType) → KData',
            'class Indicator: __getitem__(int) → float, 运算符重载: +, -, *, /, >, <',
            'class SignalBase: should_buy(Datetime) → bool, should_sell(Datetime) → bool',
            'class TradeManager: buy(), sell(), get_trade_list() → list[TradeRecord]',
            'class System: run(Stock, Query) → None, 组合: tm + sg + mm',
            'class TradeRecord: datetime, stock, real_price, number, business',
            'class Query: __init__(start, end), INDEX=0, DATE=1'
        ]),
        (12.5, 8, 11, 4, '#34495E', '常用函数签名', [
            'MA(data: Indicator, n: int = 5) → Indicator',
            'RSI(data: Indicator, n: int = 14) → Indicator',
            'SG_Cross(fast: Indicator, slow: Indicator) → SignalBase',
            'SG_Band(ind: Indicator, lower: float, upper: float) → SignalBase',
            'MM_FixedCount(count: int) → MoneyManagerBase',
            'crtTM(init_cash: float = 100000) → TradeManager',
            'SYS_Simple(tm, sg, mm, ev=None, st=None) → System'
        ]),
        (0.5, 2, 23, 5, '#95A5A6', '完整示例代码', [
            '# 1. 获取数据',
            'stock = sm["sz000001"]',
            'kdata = stock.get_kdata(Query(-250))',
            '',
            '# 2. 计算指标',
            'close = CLOSE(kdata)',
            'ma5 = MA(close, 5)',
            'ma20 = MA(close, 20)',
            'rsi = RSI(close, 14)',
            '',
            '# 3. 创建信号',
            'sg_ma = SG_Cross(ma5, ma20)',
            'sg_rsi = SG_Band(rsi, 30, 70)',
            'sg_combined = sg_ma & sg_rsi',
            '',
            '# 4. 构建系统',
            'tm = crtTM(init_cash=100000)',
            'mm = MM_FixedCount(1000)',
            'st = ST_FixedPercent(0.05)',
            'sys = SYS_Simple(tm=tm, sg=sg_combined, mm=mm, st=st)',
            '',
            '# 5. 运行回测',
            'sys.run(stock, Query(-250))',
            '',
            '# 6. 分析结果',
            'trades = tm.get_trade_list()',
            'return_rate = (tm.current_cash - tm.init_cash) / tm.init_cash * 100',
            'print(f"收益率: {return_rate:.2f}%")'
        ])
    ]
    
    # 绘制API模块
    for x, y, w, h, color, title, apis in api_modules:
        # 模块框
        module_box = FancyBboxPatch((x, y), w, h,
                                   boxstyle="round,pad=0.1",
                                   facecolor=color,
                                   edgecolor='white',
                                   linewidth=2,
                                   alpha=0.9)
        ax.add_patch(module_box)
        
        # 标题
        ax.text(x + w/2, y + h - 0.2, title, 
                ha='center', va='center', fontsize=11, fontweight='bold', color='white')
        
        # API列表
        for i, api in enumerate(apis):
            if api:  # 跳过空行
                ax.text(x + 0.1, y + h - 0.6 - i*0.25, api, 
                        ha='left', va='center', fontsize=8, color='white',
                        fontfamily='monospace')
    
    plt.tight_layout()
    
    # 保存详细API图
    plt.savefig('hikyuu_detailed_api.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('hikyuu_detailed_api.jpg', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("✅ 详细API图已生成:")
    print("   📄 hikyuu_detailed_api.png (PNG格式)")
    print("   📄 hikyuu_detailed_api.jpg (JPG格式)")
    
    plt.show()
    
    return fig

if __name__ == "__main__":
    print("🚀 开始生成Hikyuu思维导图...")
    
    # 生成主要思维导图
    fig1 = create_hikyuu_mindmap()
    
    # 生成详细API图
    fig2 = create_detailed_api_diagram()
    
    print("\n🎉 所有图片生成完成！")
    print("📁 生成的文件:")
    print("   🖼️ hikyuu_mindmap.png - 主要思维导图")
    print("   🖼️ hikyuu_mindmap.jpg - 主要思维导图(JPG)")
    print("   🖼️ hikyuu_detailed_api.png - 详细API图")
    print("   🖼️ hikyuu_detailed_api.jpg - 详细API图(JPG)")
    print("\n💡 提示: 图片已保存在当前目录，可以直接打开查看！")
