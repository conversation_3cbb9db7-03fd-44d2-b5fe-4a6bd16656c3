{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["std::cout are redirected to python::stdout\n", "std::cerr are redirected to python::stderr\n", "2021-02-12 17:03:43.490 [HKU-I] - Using SQLITE3 BaseInfoDriver (BaseInfoDriver.cpp:58)\n", "2021-02-12 17:03:43.492 [HKU-I] - Loading market information... (StockManager.cpp:473)\n", "2021-02-12 17:03:43.494 [HKU-I] - Loading stock type information... (StockManager.cpp:486)\n", "2021-02-12 17:03:43.495 [HKU-I] - Loading stock information... (StockManager.cpp:422)\n", "2021-02-12 17:03:43.578 [HKU-I] - Loading stock weight... (StockManager.cpp:503)\n", "2021-02-12 17:03:44.859 [HKU-I] - Loading KData... (StockManager.cpp:138)\n", "2021-02-12 17:03:44.864 [HKU-I] - Preloading all day kdata to buffer! (StockManager.cpp:161)\n", "2021-02-12 17:03:44.889 [HKU-I] - 0.03s Loaded Data. (StockManager.cpp:149)\n", "Wall time: 2.62 s\n"]}], "source": ["%matplotlib inline\n", "%time from hikyuu.interactive import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 获取实时日线数据\n", "\n", "目前仅支持获取实时日线数据，使用函数 realtimeUpdate(source, delta=60)。其中，source支持 'sina' | 'qq' | 'tushare'，默认使用 tushare。\n", "\n", "tushare 需安装 Python tushare 库，pip install tushare.\n", "\n", "**使用 sina 或 qq 时，应注意控制两次获取数据之间的间隔时长（使用参数delta，默认时长60s），以免 ip 被 sina 或 qq 列入黑名单。**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["更新完毕！ 2021-02-12 17:03:51.398109\n", "Wall time: 6.1 s\n"]}], "source": ["%time realtime_update('sina')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["更新间隔小于60秒，未更新\n", "上次更新时间:  2021-02-12 17:03:51.398109\n", "Wall time: 999 µs\n"]}], "source": ["%time realtime_update('qq')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 2}