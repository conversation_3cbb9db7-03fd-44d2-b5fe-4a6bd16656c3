                    ╔══════════════════════════════════════╗
                    ║        Hikyuu 2.6.5 量化交易框架        ║
                    ╚══════════════════════════════════════╝
                                        │
                ┌───────────────────────┼───────────────────────┐
                │                       │                       │
        ┌───────▼────────┐     ┌───────▼────────┐     ┌───────▼────────┐
        │   数据管理层    │     │   技术指标层    │     │ 交易系统核心   │
        └───────┬────────┘     └───────┬────────┘     └───────┬────────┘
                │                       │                       │
    ┌───────────┼───────────┐          │              ┌────────┼────────┐
    │           │           │          │              │        │        │
┌───▼───┐  ┌───▼───┐  ┌───▼───┐  ┌───▼───┐      ┌───▼───┐ ┌─▼──┐ ┌─▼──┐
│StockMgr│  │Stock  │  │KData  │  │Indicator│     │Signal │ │ TM │ │ MM │
└───┬───┘  └───┬───┘  └───┬───┘  └───┬───┘      └───┬───┘ └─┬──┘ └─┬──┘
    │          │          │          │              │       │      │
    │          │          │          │              │       │      │
┌───▼───┐  ┌───▼───┐  ┌───▼───┐  ┌───▼───┐      ┌───▼───┐ ┌─▼──┐ ┌─▼──┐
│sm[''] │  │name   │  │Query  │  │MA/RSI │      │Cross  │ │buy │ │Fixed│
│get_   │  │code   │  │CLOSE  │  │MACD   │      │Band   │ │sell│ │Count│
│stock_ │  │market │  │HIGH   │  │KDJ    │      │Bool   │ │get_│ │Percent│
│list() │  │get_   │  │LOW    │  │BOLL   │      │& | +  │ │list│ │Cash │
└───────┘  │kdata()│  │VOL    │  │ATR    │      └───────┘ └────┘ └─────┘
           └───────┘  └───────┘  └───────┘

                ┌───────────────────────┼───────────────────────┐
                │                       │                       │
        ┌───────▼────────┐     ┌───────▼────────┐     ┌───────▼────────┐
        │   风险控制层    │     │  高级策略层     │     │  工具扩展层     │
        └───────┬────────┘     └───────┬────────┘     └───────┬────────┘
                │                       │                       │
    ┌───────────┼───────────┐          │              ┌────────┼────────┐
    │           │           │          │              │        │        │
┌───▼───┐  ┌───▼───┐  ┌───▼───┐  ┌───▼───┐      ┌───▼───┐ ┌─▼──┐ ┌─▼──┐
│ ST    │  │ TP    │  │ EV    │  │MultiFactor│   │Datetime│ │save│ │load│
│止损   │  │止盈   │  │环境   │  │Selector │     │List   │ │obj │ │obj │
└───┬───┘  └───┬───┘  └───┬───┘  └───┬───┘      └───┬───┘ └─┬──┘ └─┬──┘
    │          │          │          │              │       │      │
┌───▼───┐  ┌───▼───┐  ┌───▼───┐  ┌───▼───┐      ┌───▼───┐ ┌─▼──┐ ┌─▼──┐
│Fixed  │  │Fixed  │  │Bool   │  │Portfolio│     │year   │ │hku_│ │get_│
│Percent│  │Percent│  │condition│ │AF     │      │month  │ │save│ │config│
│Indicator│ │Indicator│ │       │  │SE     │      │day    │ │load│ │context│
└───────┘  └───────┘  └───────┘  └───────┘      └───────┘ └────┘ └───────┘

═══════════════════════════════════════════════════════════════════════════════

📋 核心组件说明：

🔹 数据管理层 (Data Management)
   ├─ StockManager: sm['sz000001'] 获取股票
   ├─ Stock: 股票对象，包含name, code, market等属性
   └─ KData: K线数据容器，支持Query查询和数据提取

🔹 技术指标层 (Technical Indicators)
   ├─ 趋势指标: MA(), EMA(), MACD(), ADX()
   ├─ 震荡指标: RSI(), KDJ(), CCI(), WR()
   ├─ 成交量指标: OBV(), VOL(), AD()
   └─ 价格指标: BOLL(), SAR(), ATR()

🔹 交易系统核心 (Trading System Core)
   ├─ Signal: SG_Cross(), SG_Band(), SG_Bool() + 信号运算
   ├─ TradeManager: buy(), sell(), get_trade_list()
   └─ MoneyManager: MM_FixedCount(), MM_FixedPercent()

🔹 风险控制层 (Risk Management)
   ├─ Stoploss: ST_FixedPercent() 止损策略
   ├─ TakeProfit: TP_FixedPercent() 止盈策略
   └─ Environment: EV_Bool() 市场环境判断

🔹 高级策略层 (Advanced Strategy)
   ├─ MultiFactor: 多因子模型
   ├─ Selector: 选股器
   └─ Portfolio: 投资组合管理

🔹 工具扩展层 (Extensions)
   ├─ DateTime: 日期时间处理
   ├─ Serialization: hku_save(), hku_load()
   └─ Configuration: 系统配置管理

═══════════════════════════════════════════════════════════════════════════════

🎯 使用流程：

1️⃣ 数据获取: stock = sm['sz000001'] → kdata = stock.get_kdata(Query(-100))
2️⃣ 指标计算: ma5 = MA(CLOSE(kdata), 5) → rsi = RSI(CLOSE(kdata), 14)
3️⃣ 信号生成: sg = SG_Cross(ma5, ma20) → combined = sg1 & sg2
4️⃣ 系统构建: sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
5️⃣ 回测运行: sys.run(stock, Query(-250))
6️⃣ 结果分析: trades = tm.get_trade_list() → 计算收益率

═══════════════════════════════════════════════════════════════════════════════

💡 关键API速查：

📊 数据: sm['code'], stock.get_kdata(), CLOSE(), HIGH(), LOW(), VOL()
📈 指标: MA(data,n), RSI(data,n), MACD(data), KDJ(kdata)
🎯 信号: SG_Cross(fast,slow), SG_Band(ind,low,high), SG_Bool(condition)
💰 交易: crtTM(cash), tm.buy(), tm.sell(), tm.get_trade_list()
🛡️ 风控: ST_FixedPercent(%), TP_FixedPercent(%), EV_Bool(condition)
🚀 系统: SYS_Simple(tm,sg,mm), system.run(stock,query)

═══════════════════════════════════════════════════════════════════════════════
