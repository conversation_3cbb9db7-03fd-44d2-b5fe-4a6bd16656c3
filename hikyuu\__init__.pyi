from __future__ import annotations
from datetime import date
from datetime import datetime
from datetime import timedelta
from hikyuu.analysis.analysis import analysis_sys_list
from hikyuu.analysis.analysis import analysis_sys_list_multi
from hikyuu.analysis.analysis import combinate_ind_analysis
from hikyuu.analysis.analysis import combinate_ind_analysis_multi
from hikyuu.cpp.core39 import ABS
from hikyuu.cpp.core39 import ACOS
from hikyuu.cpp.core39 import AD
from hikyuu.cpp.core39 import ADVANCE
from hikyuu.cpp.core39 import AF_EqualWeight
from hikyuu.cpp.core39 import AF_FixedWeight
from hikyuu.cpp.core39 import AF_FixedWeightList
from hikyuu.cpp.core39 import AF_MultiFactor
from hikyuu.cpp.core39 import ALIGN
from hikyuu.cpp.core39 import AMA
from hikyuu.cpp.core39 import ASIN
from hikyuu.cpp.core39 import ATAN
from hikyuu.cpp.core39 import ATR
from hikyuu.cpp.core39 import AVEDEV
from hikyuu.cpp.core39 import AllocateFundsBase
from hikyuu.cpp.core39 import BACKSET
from hikyuu.cpp.core39 import BARSCOUNT
from hikyuu.cpp.core39 import BARSLAST
from hikyuu.cpp.core39 import BARSLASTCOUNT
from hikyuu.cpp.core39 import BARSSINCE
from hikyuu.cpp.core39 import BARSSINCEN
from hikyuu.cpp.core39 import BETWEEN
from hikyuu.cpp.core39 import BLOCKSETNUM
from hikyuu.cpp.core39 import BUSINESS
from hikyuu.cpp.core39 import Block
from hikyuu.cpp.core39 import BlockInfoDriver
from hikyuu.cpp.core39 import BorrowRecord
from hikyuu.cpp.core39 import BrokerPositionRecord
from hikyuu.cpp.core39 import CEILING
from hikyuu.cpp.core39 import CN_Bool
from hikyuu.cpp.core39 import CN_OPLine
from hikyuu.cpp.core39 import CONTEXT
from hikyuu.cpp.core39 import CONTEXT_K
from hikyuu.cpp.core39 import CORR
from hikyuu.cpp.core39 import COS
from hikyuu.cpp.core39 import COST
from hikyuu.cpp.core39 import COUNT
from hikyuu.cpp.core39 import CROSS
from hikyuu.cpp.core39 import CVAL
from hikyuu.cpp.core39 import CYCLE
from hikyuu.cpp.core39 import C_AMO
from hikyuu.cpp.core39 import C_CLOSE
from hikyuu.cpp.core39 import C_HIGH
from hikyuu.cpp.core39 import C_KDATA
from hikyuu.cpp.core39 import C_LOW
from hikyuu.cpp.core39 import C_OPEN
from hikyuu.cpp.core39 import C_VOL
from hikyuu.cpp.core39 import ConditionBase
from hikyuu.cpp.core39 import Constant
from hikyuu.cpp.core39 import CostRecord
from hikyuu.cpp.core39 import DATE
from hikyuu.cpp.core39 import DAY
from hikyuu.cpp.core39 import DECLINE
from hikyuu.cpp.core39 import DEVSQ
from hikyuu.cpp.core39 import DIFF
from hikyuu.cpp.core39 import DIRECT
from hikyuu.cpp.core39 import DISCARD
from hikyuu.cpp.core39 import DMA
from hikyuu.cpp.core39 import DOWNNDAY
from hikyuu.cpp.core39 import DROPNA
from hikyuu.cpp.core39 import DataDriverFactory
from hikyuu.cpp.core39 import Datetime as D
from hikyuu.cpp.core39 import Datetime
from hikyuu.cpp.core39 import DatetimeList
from hikyuu.cpp.core39 import Days
from hikyuu.cpp.core39 import EMA
from hikyuu.cpp.core39 import EVERY
from hikyuu.cpp.core39 import EV_Bool
from hikyuu.cpp.core39 import EV_TwoLine
from hikyuu.cpp.core39 import EXIST
from hikyuu.cpp.core39 import EXP
from hikyuu.cpp.core39 import EnvironmentBase
from hikyuu.cpp.core39 import FILTER
from hikyuu.cpp.core39 import FINANCE
from hikyuu.cpp.core39 import FLOOR
from hikyuu.cpp.core39 import FundsRecord
from hikyuu.cpp.core39 import HHV
from hikyuu.cpp.core39 import HHVBARS
from hikyuu.cpp.core39 import HKUException
from hikyuu.cpp.core39 import HOUR
from hikyuu.cpp.core39 import HSL
from hikyuu.cpp.core39 import Hours
from hikyuu.cpp.core39 import IC
from hikyuu.cpp.core39 import ICIR
from hikyuu.cpp.core39 import IF
from hikyuu.cpp.core39 import INBLOCK
from hikyuu.cpp.core39 import INDEXA
from hikyuu.cpp.core39 import INDEXADV
from hikyuu.cpp.core39 import INDEXC
from hikyuu.cpp.core39 import INDEXDEC
from hikyuu.cpp.core39 import INDEXH
from hikyuu.cpp.core39 import INDEXL
from hikyuu.cpp.core39 import INDEXO
from hikyuu.cpp.core39 import INDEXV
from hikyuu.cpp.core39 import INSUM
from hikyuu.cpp.core39 import INTPART
from hikyuu.cpp.core39 import IR
from hikyuu.cpp.core39 import ISINF
from hikyuu.cpp.core39 import ISINFA
from hikyuu.cpp.core39 import ISLASTBAR
from hikyuu.cpp.core39 import ISNA
from hikyuu.cpp.core39 import IndParam
from hikyuu.cpp.core39 import Indicator
from hikyuu.cpp.core39 import IndicatorImp
from hikyuu.cpp.core39 import JUMPDOWN
from hikyuu.cpp.core39 import JUMPUP
from hikyuu.cpp.core39 import KALMAN
from hikyuu.cpp.core39 import KDATA_PART
from hikyuu.cpp.core39 import KData
from hikyuu.cpp.core39 import KDataDriver
from hikyuu.cpp.core39 import KDataToHdf5Importer
from hikyuu.cpp.core39 import KRecord
from hikyuu.cpp.core39 import KRecordList
from hikyuu.cpp.core39 import LAST
from hikyuu.cpp.core39 import LASTVALUE as CONST
from hikyuu.cpp.core39 import LASTVALUE
from hikyuu.cpp.core39 import LIUTONGPAN as CAPITAL
from hikyuu.cpp.core39 import LIUTONGPAN
from hikyuu.cpp.core39 import LLV
from hikyuu.cpp.core39 import LLVBARS
from hikyuu.cpp.core39 import LN
from hikyuu.cpp.core39 import LOG
from hikyuu.cpp.core39 import LOG_LEVEL
from hikyuu.cpp.core39 import LONGCROSS
from hikyuu.cpp.core39 import LoanRecord
from hikyuu.cpp.core39 import MA
from hikyuu.cpp.core39 import MACD
from hikyuu.cpp.core39 import MAX
from hikyuu.cpp.core39 import MDD
from hikyuu.cpp.core39 import MF_EqualWeight
from hikyuu.cpp.core39 import MF_ICIRWeight
from hikyuu.cpp.core39 import MF_ICWeight
from hikyuu.cpp.core39 import MF_Weight
from hikyuu.cpp.core39 import MIN
from hikyuu.cpp.core39 import MINUTE
from hikyuu.cpp.core39 import MM_FixedCapital
from hikyuu.cpp.core39 import MM_FixedCapitalFunds
from hikyuu.cpp.core39 import MM_FixedCount
from hikyuu.cpp.core39 import MM_FixedCountTps
from hikyuu.cpp.core39 import MM_FixedPercent
from hikyuu.cpp.core39 import MM_FixedRisk
from hikyuu.cpp.core39 import MM_FixedUnits
from hikyuu.cpp.core39 import MM_Nothing
from hikyuu.cpp.core39 import MM_WilliamsFixedRisk
from hikyuu.cpp.core39 import MOD
from hikyuu.cpp.core39 import MONTH
from hikyuu.cpp.core39 import MRR
from hikyuu.cpp.core39 import MarketInfo
from hikyuu.cpp.core39 import Microseconds
from hikyuu.cpp.core39 import Milliseconds
from hikyuu.cpp.core39 import Minutes
from hikyuu.cpp.core39 import MoneyManagerBase
from hikyuu.cpp.core39 import MultiFactorBase
from hikyuu.cpp.core39 import NDAY
from hikyuu.cpp.core39 import NOT
from hikyuu.cpp.core39 import OrderBrokerBase
from hikyuu.cpp.core39 import PF_Simple
from hikyuu.cpp.core39 import PF_WithoutAF
from hikyuu.cpp.core39 import PG_FixedHoldDays
from hikyuu.cpp.core39 import PG_FixedPercent
from hikyuu.cpp.core39 import PG_NoGoal
from hikyuu.cpp.core39 import POS
from hikyuu.cpp.core39 import POW
from hikyuu.cpp.core39 import PRICELIST
from hikyuu.cpp.core39 import PRICELIST as VALUE
from hikyuu.cpp.core39 import Parameter
from hikyuu.cpp.core39 import Performance
from hikyuu.cpp.core39 import Portfolio
from hikyuu.cpp.core39 import PositionRecord
from hikyuu.cpp.core39 import PositionRecordList
from hikyuu.cpp.core39 import ProfitGoalBase
from hikyuu.cpp.core39 import Query as Q
from hikyuu.cpp.core39 import Query
from hikyuu.cpp.core39 import RANK
from hikyuu.cpp.core39 import RECOVER_BACKWARD
from hikyuu.cpp.core39 import RECOVER_EQUAL_BACKWARD
from hikyuu.cpp.core39 import RECOVER_EQUAL_FORWARD
from hikyuu.cpp.core39 import RECOVER_FORWARD
from hikyuu.cpp.core39 import REF
from hikyuu.cpp.core39 import REPLACE
from hikyuu.cpp.core39 import RESULT
from hikyuu.cpp.core39 import REVERSE
from hikyuu.cpp.core39 import ROC
from hikyuu.cpp.core39 import ROCP
from hikyuu.cpp.core39 import ROCR
from hikyuu.cpp.core39 import ROCR100
from hikyuu.cpp.core39 import ROUND
from hikyuu.cpp.core39 import ROUNDDOWN
from hikyuu.cpp.core39 import ROUNDUP
from hikyuu.cpp.core39 import RSI
from hikyuu.cpp.core39 import SAFTYLOSS
from hikyuu.cpp.core39 import SE_EvaluateOptimal
from hikyuu.cpp.core39 import SE_Fixed
from hikyuu.cpp.core39 import SE_MaxFundsOptimal
from hikyuu.cpp.core39 import SE_MultiFactor
from hikyuu.cpp.core39 import SE_PerformanceOptimal
from hikyuu.cpp.core39 import SE_Signal
from hikyuu.cpp.core39 import SGN
from hikyuu.cpp.core39 import SG_Add
from hikyuu.cpp.core39 import SG_AllwaysBuy
from hikyuu.cpp.core39 import SG_And
from hikyuu.cpp.core39 import SG_Band
from hikyuu.cpp.core39 import SG_Bool
from hikyuu.cpp.core39 import SG_Buy
from hikyuu.cpp.core39 import SG_Cross
from hikyuu.cpp.core39 import SG_CrossGold
from hikyuu.cpp.core39 import SG_Cycle
from hikyuu.cpp.core39 import SG_Div
from hikyuu.cpp.core39 import SG_Flex
from hikyuu.cpp.core39 import SG_Mul
from hikyuu.cpp.core39 import SG_OneSide
from hikyuu.cpp.core39 import SG_Or
from hikyuu.cpp.core39 import SG_Sell
from hikyuu.cpp.core39 import SG_Single
from hikyuu.cpp.core39 import SG_Single2
from hikyuu.cpp.core39 import SG_Sub
from hikyuu.cpp.core39 import SIN
from hikyuu.cpp.core39 import SLICE
from hikyuu.cpp.core39 import SLOPE
from hikyuu.cpp.core39 import SMA
from hikyuu.cpp.core39 import SPEARMAN
from hikyuu.cpp.core39 import SP_FixedPercent
from hikyuu.cpp.core39 import SP_FixedValue
from hikyuu.cpp.core39 import SQRT
from hikyuu.cpp.core39 import STDEV
from hikyuu.cpp.core39 import STDEV as STD
from hikyuu.cpp.core39 import STDP
from hikyuu.cpp.core39 import ST_FixedPercent
from hikyuu.cpp.core39 import ST_Indicator
from hikyuu.cpp.core39 import ST_Saftyloss
from hikyuu.cpp.core39 import SUM
from hikyuu.cpp.core39 import SUMBARS
from hikyuu.cpp.core39 import SYS_Simple
from hikyuu.cpp.core39 import SYS_WalkForward
from hikyuu.cpp.core39 import ScoreRecord
from hikyuu.cpp.core39 import ScoreRecordList
from hikyuu.cpp.core39 import Seconds
from hikyuu.cpp.core39 import SelectorBase
from hikyuu.cpp.core39 import SignalBase
from hikyuu.cpp.core39 import SlippageBase
from hikyuu.cpp.core39 import SpotRecord
from hikyuu.cpp.core39 import Stock
from hikyuu.cpp.core39 import StockManager
from hikyuu.cpp.core39 import StockTypeInfo
from hikyuu.cpp.core39 import StockWeight
from hikyuu.cpp.core39 import StockWeightList
from hikyuu.cpp.core39 import StoplossBase
from hikyuu.cpp.core39 import Strategy
from hikyuu.cpp.core39 import StrategyContext
from hikyuu.cpp.core39 import System
from hikyuu.cpp.core39 import SystemPart
from hikyuu.cpp.core39 import SystemWeight
from hikyuu.cpp.core39 import SystemWeightList
from hikyuu.cpp.core39 import TAN
from hikyuu.cpp.core39 import TA_ACCBANDS
from hikyuu.cpp.core39 import TA_ACOS
from hikyuu.cpp.core39 import TA_AD
from hikyuu.cpp.core39 import TA_ADD
from hikyuu.cpp.core39 import TA_ADOSC
from hikyuu.cpp.core39 import TA_ADX
from hikyuu.cpp.core39 import TA_ADXR
from hikyuu.cpp.core39 import TA_APO
from hikyuu.cpp.core39 import TA_AROON
from hikyuu.cpp.core39 import TA_AROONOSC
from hikyuu.cpp.core39 import TA_ASIN
from hikyuu.cpp.core39 import TA_ATAN
from hikyuu.cpp.core39 import TA_ATR
from hikyuu.cpp.core39 import TA_AVGDEV
from hikyuu.cpp.core39 import TA_AVGPRICE
from hikyuu.cpp.core39 import TA_BBANDS
from hikyuu.cpp.core39 import TA_BETA
from hikyuu.cpp.core39 import TA_BOP
from hikyuu.cpp.core39 import TA_CCI
from hikyuu.cpp.core39 import TA_CDL2CROWS
from hikyuu.cpp.core39 import TA_CDL3BLACKCROWS
from hikyuu.cpp.core39 import TA_CDL3INSIDE
from hikyuu.cpp.core39 import TA_CDL3LINESTRIKE
from hikyuu.cpp.core39 import TA_CDL3OUTSIDE
from hikyuu.cpp.core39 import TA_CDL3STARSINSOUTH
from hikyuu.cpp.core39 import TA_CDL3WHITESOLDIERS
from hikyuu.cpp.core39 import TA_CDLABANDONEDBABY
from hikyuu.cpp.core39 import TA_CDLADVANCEBLOCK
from hikyuu.cpp.core39 import TA_CDLBELTHOLD
from hikyuu.cpp.core39 import TA_CDLBREAKAWAY
from hikyuu.cpp.core39 import TA_CDLCLOSINGMARUBOZU
from hikyuu.cpp.core39 import TA_CDLCONCEALBABYSWALL
from hikyuu.cpp.core39 import TA_CDLCOUNTERATTACK
from hikyuu.cpp.core39 import TA_CDLDARKCLOUDCOVER
from hikyuu.cpp.core39 import TA_CDLDOJI
from hikyuu.cpp.core39 import TA_CDLDOJISTAR
from hikyuu.cpp.core39 import TA_CDLDRAGONFLYDOJI
from hikyuu.cpp.core39 import TA_CDLENGULFING
from hikyuu.cpp.core39 import TA_CDLEVENINGDOJISTAR
from hikyuu.cpp.core39 import TA_CDLEVENINGSTAR
from hikyuu.cpp.core39 import TA_CDLGAPSIDESIDEWHITE
from hikyuu.cpp.core39 import TA_CDLGRAVESTONEDOJI
from hikyuu.cpp.core39 import TA_CDLHAMMER
from hikyuu.cpp.core39 import TA_CDLHANGINGMAN
from hikyuu.cpp.core39 import TA_CDLHARAMI
from hikyuu.cpp.core39 import TA_CDLHARAMICROSS
from hikyuu.cpp.core39 import TA_CDLHIGHWAVE
from hikyuu.cpp.core39 import TA_CDLHIKKAKE
from hikyuu.cpp.core39 import TA_CDLHIKKAKEMOD
from hikyuu.cpp.core39 import TA_CDLHOMINGPIGEON
from hikyuu.cpp.core39 import TA_CDLIDENTICAL3CROWS
from hikyuu.cpp.core39 import TA_CDLINNECK
from hikyuu.cpp.core39 import TA_CDLINVERTEDHAMMER
from hikyuu.cpp.core39 import TA_CDLKICKING
from hikyuu.cpp.core39 import TA_CDLKICKINGBYLENGTH
from hikyuu.cpp.core39 import TA_CDLLADDERBOTTOM
from hikyuu.cpp.core39 import TA_CDLLONGLEGGEDDOJI
from hikyuu.cpp.core39 import TA_CDLLONGLINE
from hikyuu.cpp.core39 import TA_CDLMARUBOZU
from hikyuu.cpp.core39 import TA_CDLMATCHINGLOW
from hikyuu.cpp.core39 import TA_CDLMATHOLD
from hikyuu.cpp.core39 import TA_CDLMORNINGDOJISTAR
from hikyuu.cpp.core39 import TA_CDLMORNINGSTAR
from hikyuu.cpp.core39 import TA_CDLONNECK
from hikyuu.cpp.core39 import TA_CDLPIERCING
from hikyuu.cpp.core39 import TA_CDLRICKSHAWMAN
from hikyuu.cpp.core39 import TA_CDLRISEFALL3METHODS
from hikyuu.cpp.core39 import TA_CDLSEPARATINGLINES
from hikyuu.cpp.core39 import TA_CDLSHOOTINGSTAR
from hikyuu.cpp.core39 import TA_CDLSHORTLINE
from hikyuu.cpp.core39 import TA_CDLSPINNINGTOP
from hikyuu.cpp.core39 import TA_CDLSTALLEDPATTERN
from hikyuu.cpp.core39 import TA_CDLSTICKSANDWICH
from hikyuu.cpp.core39 import TA_CDLTAKURI
from hikyuu.cpp.core39 import TA_CDLTASUKIGAP
from hikyuu.cpp.core39 import TA_CDLTHRUSTING
from hikyuu.cpp.core39 import TA_CDLTRISTAR
from hikyuu.cpp.core39 import TA_CDLUNIQUE3RIVER
from hikyuu.cpp.core39 import TA_CDLUPSIDEGAP2CROWS
from hikyuu.cpp.core39 import TA_CDLXSIDEGAP3METHODS
from hikyuu.cpp.core39 import TA_CEIL
from hikyuu.cpp.core39 import TA_CMO
from hikyuu.cpp.core39 import TA_CORREL
from hikyuu.cpp.core39 import TA_COS
from hikyuu.cpp.core39 import TA_COSH
from hikyuu.cpp.core39 import TA_DEMA
from hikyuu.cpp.core39 import TA_DIV
from hikyuu.cpp.core39 import TA_DX
from hikyuu.cpp.core39 import TA_EMA
from hikyuu.cpp.core39 import TA_EXP
from hikyuu.cpp.core39 import TA_FLOOR
from hikyuu.cpp.core39 import TA_HT_DCPERIOD
from hikyuu.cpp.core39 import TA_HT_DCPHASE
from hikyuu.cpp.core39 import TA_HT_PHASOR
from hikyuu.cpp.core39 import TA_HT_SINE
from hikyuu.cpp.core39 import TA_HT_TRENDLINE
from hikyuu.cpp.core39 import TA_HT_TRENDMODE
from hikyuu.cpp.core39 import TA_IMI
from hikyuu.cpp.core39 import TA_KAMA
from hikyuu.cpp.core39 import TA_LINEARREG
from hikyuu.cpp.core39 import TA_LINEARREG_ANGLE
from hikyuu.cpp.core39 import TA_LINEARREG_INTERCEPT
from hikyuu.cpp.core39 import TA_LINEARREG_SLOPE
from hikyuu.cpp.core39 import TA_LN
from hikyuu.cpp.core39 import TA_LOG10
from hikyuu.cpp.core39 import TA_MA
from hikyuu.cpp.core39 import TA_MACD
from hikyuu.cpp.core39 import TA_MACDEXT
from hikyuu.cpp.core39 import TA_MACDFIX
from hikyuu.cpp.core39 import TA_MAMA
from hikyuu.cpp.core39 import TA_MAVP
from hikyuu.cpp.core39 import TA_MAX
from hikyuu.cpp.core39 import TA_MAXINDEX
from hikyuu.cpp.core39 import TA_MEDPRICE
from hikyuu.cpp.core39 import TA_MFI
from hikyuu.cpp.core39 import TA_MIDPOINT
from hikyuu.cpp.core39 import TA_MIDPRICE
from hikyuu.cpp.core39 import TA_MIN
from hikyuu.cpp.core39 import TA_MININDEX
from hikyuu.cpp.core39 import TA_MINMAX
from hikyuu.cpp.core39 import TA_MINMAXINDEX
from hikyuu.cpp.core39 import TA_MINUS_DI
from hikyuu.cpp.core39 import TA_MINUS_DM
from hikyuu.cpp.core39 import TA_MOM
from hikyuu.cpp.core39 import TA_MULT
from hikyuu.cpp.core39 import TA_NATR
from hikyuu.cpp.core39 import TA_OBV
from hikyuu.cpp.core39 import TA_PLUS_DI
from hikyuu.cpp.core39 import TA_PLUS_DM
from hikyuu.cpp.core39 import TA_PPO
from hikyuu.cpp.core39 import TA_ROC
from hikyuu.cpp.core39 import TA_ROCP
from hikyuu.cpp.core39 import TA_ROCR
from hikyuu.cpp.core39 import TA_ROCR100
from hikyuu.cpp.core39 import TA_RSI
from hikyuu.cpp.core39 import TA_SAR
from hikyuu.cpp.core39 import TA_SAREXT
from hikyuu.cpp.core39 import TA_SIN
from hikyuu.cpp.core39 import TA_SINH
from hikyuu.cpp.core39 import TA_SMA
from hikyuu.cpp.core39 import TA_SQRT
from hikyuu.cpp.core39 import TA_STDDEV
from hikyuu.cpp.core39 import TA_STOCH
from hikyuu.cpp.core39 import TA_STOCHF
from hikyuu.cpp.core39 import TA_STOCHRSI
from hikyuu.cpp.core39 import TA_SUB
from hikyuu.cpp.core39 import TA_SUM
from hikyuu.cpp.core39 import TA_T3
from hikyuu.cpp.core39 import TA_TAN
from hikyuu.cpp.core39 import TA_TANH
from hikyuu.cpp.core39 import TA_TEMA
from hikyuu.cpp.core39 import TA_TRANGE
from hikyuu.cpp.core39 import TA_TRIMA
from hikyuu.cpp.core39 import TA_TRIX
from hikyuu.cpp.core39 import TA_TSF
from hikyuu.cpp.core39 import TA_TYPPRICE
from hikyuu.cpp.core39 import TA_ULTOSC
from hikyuu.cpp.core39 import TA_VAR
from hikyuu.cpp.core39 import TA_WCLPRICE
from hikyuu.cpp.core39 import TA_WILLR
from hikyuu.cpp.core39 import TA_WMA
from hikyuu.cpp.core39 import TC_FixedA
from hikyuu.cpp.core39 import TC_FixedA2015
from hikyuu.cpp.core39 import TC_FixedA2017
from hikyuu.cpp.core39 import TC_TestStub
from hikyuu.cpp.core39 import TC_Zero
from hikyuu.cpp.core39 import TIME
from hikyuu.cpp.core39 import TIMELINE
from hikyuu.cpp.core39 import TIMELINEVOL
from hikyuu.cpp.core39 import TR
from hikyuu.cpp.core39 import TURNOVER
from hikyuu.cpp.core39 import TimeDelta
from hikyuu.cpp.core39 import TimeLineList
from hikyuu.cpp.core39 import TimeLineRecord
from hikyuu.cpp.core39 import TradeCostBase
from hikyuu.cpp.core39 import TradeManager
from hikyuu.cpp.core39 import TradeRecord
from hikyuu.cpp.core39 import TradeRecordList
from hikyuu.cpp.core39 import TradeRequest
from hikyuu.cpp.core39 import TransList
from hikyuu.cpp.core39 import TransRecord
from hikyuu.cpp.core39 import UPNDAY
from hikyuu.cpp.core39 import UTCOffset
from hikyuu.cpp.core39 import VAR
from hikyuu.cpp.core39 import VARP
from hikyuu.cpp.core39 import VIGOR
from hikyuu.cpp.core39 import WEAVE
from hikyuu.cpp.core39 import WEEK
from hikyuu.cpp.core39 import WINNER
from hikyuu.cpp.core39 import WITHDAY
from hikyuu.cpp.core39 import WITHHALFYEAR
from hikyuu.cpp.core39 import WITHHOUR
from hikyuu.cpp.core39 import WITHHOUR2
from hikyuu.cpp.core39 import WITHHOUR4
from hikyuu.cpp.core39 import WITHKTYPE
from hikyuu.cpp.core39 import WITHMIN
from hikyuu.cpp.core39 import WITHMIN15
from hikyuu.cpp.core39 import WITHMIN30
from hikyuu.cpp.core39 import WITHMIN5
from hikyuu.cpp.core39 import WITHMIN60
from hikyuu.cpp.core39 import WITHMONTH
from hikyuu.cpp.core39 import WITHQUARTER
from hikyuu.cpp.core39 import WITHWEEK
from hikyuu.cpp.core39 import WITHYEAR
from hikyuu.cpp.core39 import WMA
from hikyuu.cpp.core39 import YEAR
from hikyuu.cpp.core39 import ZHBOND10
from hikyuu.cpp.core39 import ZONGGUBEN
from hikyuu.cpp.core39 import ZSCORE
from hikyuu.cpp.core39 import __init__ as old_Query_init
from hikyuu.cpp.core39 import active_device
from hikyuu.cpp.core39 import backtest
from hikyuu.cpp.core39 import batch_calculate_inds
from hikyuu.cpp.core39 import can_upgrade
from hikyuu.cpp.core39 import close_ostream_to_python
from hikyuu.cpp.core39 import close_spend_time
from hikyuu.cpp.core39 import combinate_ind
from hikyuu.cpp.core39 import combinate_index
from hikyuu.cpp.core39 import crtBrokerTM
from hikyuu.cpp.core39 import crtSEOptimal
from hikyuu.cpp.core39 import crtTM
from hikyuu.cpp.core39 import crt_pf_strategy
from hikyuu.cpp.core39 import crt_sys_strategy
from hikyuu.cpp.core39 import fetch_trial_license
from hikyuu.cpp.core39 import find_optimal_system
from hikyuu.cpp.core39 import find_optimal_system_multi
from hikyuu.cpp.core39 import get_block
from hikyuu.cpp.core39 import get_business_name
from hikyuu.cpp.core39 import get_data_from_buffer_server
from hikyuu.cpp.core39 import get_date_range
from hikyuu.cpp.core39 import get_kdata
from hikyuu.cpp.core39 import get_last_version
from hikyuu.cpp.core39 import get_log_level
from hikyuu.cpp.core39 import get_stock
from hikyuu.cpp.core39 import get_system_part_enum
from hikyuu.cpp.core39 import get_system_part_name
from hikyuu.cpp.core39 import get_version
from hikyuu.cpp.core39 import get_version_git
from hikyuu.cpp.core39 import get_version_with_build
from hikyuu.cpp.core39 import hikyuu_init
from hikyuu.cpp.core39 import inner_analysis_sys_list
from hikyuu.cpp.core39 import inner_combinate_ind_analysis
from hikyuu.cpp.core39 import inner_combinate_ind_analysis_with_block
from hikyuu.cpp.core39 import is_valid_license
from hikyuu.cpp.core39 import isinf
from hikyuu.cpp.core39 import isnan
from hikyuu.cpp.core39 import open_ostream_to_python
from hikyuu.cpp.core39 import open_spend_time
from hikyuu.cpp.core39 import remove_license
from hikyuu.cpp.core39 import roundDown
from hikyuu.cpp.core39 import roundEx
from hikyuu.cpp.core39 import roundUp
from hikyuu.cpp.core39 import run_in_strategy
from hikyuu.cpp.core39 import set_log_level
from hikyuu.cpp.core39 import set_python_in_interactive
from hikyuu.cpp.core39 import set_python_in_jupyter
from hikyuu.cpp.core39 import start_data_server
from hikyuu.cpp.core39 import start_spot_agent
from hikyuu.cpp.core39 import stop_data_server
from hikyuu.cpp.core39 import stop_spot_agent
from hikyuu.cpp.core39 import toPriceList
from hikyuu.cpp.core39 import view_license
from hikyuu.draw.drawplot import adjust_axes_show
from hikyuu.draw.drawplot import ax_draw_macd
from hikyuu.draw.drawplot import ax_draw_macd2
from hikyuu.draw.drawplot import ax_set_locator_formatter
from hikyuu.draw.drawplot import create_figure
from hikyuu.draw.drawplot import gca
from hikyuu.draw.drawplot import gcf
from hikyuu.draw.drawplot import get_current_draw_engine
from hikyuu.draw.drawplot.matplotlib_draw import DRAWBAND
from hikyuu.draw.drawplot.matplotlib_draw import DRAWICON
from hikyuu.draw.drawplot.matplotlib_draw import DRAWIMG
from hikyuu.draw.drawplot.matplotlib_draw import DRAWIMG as DRAWBMP
from hikyuu.draw.drawplot.matplotlib_draw import DRAWLINE
from hikyuu.draw.drawplot.matplotlib_draw import DRAWNUMBER
from hikyuu.draw.drawplot.matplotlib_draw import DRAWNUMBER_FIX
from hikyuu.draw.drawplot.matplotlib_draw import DRAWRECTREL
from hikyuu.draw.drawplot.matplotlib_draw import DRAWSL
from hikyuu.draw.drawplot.matplotlib_draw import DRAWTEXT
from hikyuu.draw.drawplot.matplotlib_draw import DRAWTEXT_FIX
from hikyuu.draw.drawplot.matplotlib_draw import PLOYLINE
from hikyuu.draw.drawplot.matplotlib_draw import RGB
from hikyuu.draw.drawplot.matplotlib_draw import SHOWICONS
from hikyuu.draw.drawplot.matplotlib_draw import STICKLINE
from hikyuu.draw.drawplot import show_gcf
from hikyuu.draw.drawplot import use_draw_engine
from hikyuu.draw import elder as el
from hikyuu.draw import kaufman as kf
from hikyuu.draw import volume as vl
from hikyuu.extend import DatetimeList_to_df
from hikyuu.extend import DatetimeList_to_np
from hikyuu.extend import Datetime_date
from hikyuu.extend import Datetime_datetime
from hikyuu.extend import KData_getitem
from hikyuu.extend import KData_iter
from hikyuu.extend import KData_to_df
from hikyuu.extend import KData_to_np
from hikyuu.extend import Parameter_items
from hikyuu.extend import Parameter_iter
from hikyuu.extend import Parameter_keys
from hikyuu.extend import Parameter_to_dict
from hikyuu.extend import TimeDelta_timedelta
from hikyuu.extend import TimeLine_to_df
from hikyuu.extend import TimeLine_to_np
from hikyuu.extend import TransList_to_df
from hikyuu.extend import TransList_to_np
from hikyuu.extend import new_Query_init
from hikyuu.hub import add_local_hub
from hikyuu.hub import add_remote_hub
from hikyuu.hub import build_hub
from hikyuu.hub import get_current_hub
from hikyuu.hub import get_hub_name_list
from hikyuu.hub import get_hub_path
from hikyuu.hub import get_part
from hikyuu.hub import get_part_info
from hikyuu.hub import get_part_list
from hikyuu.hub import get_part_module
from hikyuu.hub import get_part_name_list
from hikyuu.hub import print_part_info
from hikyuu.hub import print_part_info as help_part
from hikyuu.hub import remove_hub
from hikyuu.hub import search_part
from hikyuu.hub import update_hub
from hikyuu.indicator import indicator
from hikyuu.indicator.indicator import concat_to_df
from hikyuu.indicator.indicator import df_to_ind
from hikyuu.indicator.indicator import indicator_getitem
from hikyuu.indicator.indicator import indicator_iter
from hikyuu.indicator.indicator import indicator_to_df
from hikyuu.indicator import pyind
from hikyuu.indicator.pyind import KDJ
from hikyuu.indicator import talib_wrap
from hikyuu.trade_manage import broker
from hikyuu.trade_manage.broker import OrderBrokerWrap
from hikyuu.trade_manage.broker import TestOrderBroker
from hikyuu.trade_manage.broker import crtOB
from hikyuu.trade_manage import broker_easytrader
from hikyuu.trade_manage.broker_easytrader import EasyTraderOrderBroker
from hikyuu.trade_manage import broker_mail
from hikyuu.trade_manage.broker_mail import MailOrderBroker
from hikyuu.trade_manage import trade
from hikyuu.trade_manage.trade import Performance_to_df
from hikyuu.trade_manage.trade import PositionList_to_df
from hikyuu.trade_manage.trade import PositionList_to_np
from hikyuu.trade_manage.trade import TradeList_to_df
from hikyuu.trade_manage.trade import TradeList_to_np
from hikyuu.trade_sys import trade_sys
from hikyuu.trade_sys.trade_sys import crtAF
from hikyuu.trade_sys.trade_sys import crtCN
from hikyuu.trade_sys.trade_sys import crtEV
from hikyuu.trade_sys.trade_sys import crtMF
from hikyuu.trade_sys.trade_sys import crtMM
from hikyuu.trade_sys.trade_sys import crtPG
from hikyuu.trade_sys.trade_sys import crtSE
from hikyuu.trade_sys.trade_sys import crtSG
from hikyuu.trade_sys.trade_sys import crtSP
from hikyuu.trade_sys.trade_sys import crtST
from hikyuu.trade_sys.trade_sys import part_clone
from hikyuu.trade_sys.trade_sys import part_init
from hikyuu.trade_sys.trade_sys import part_iter
from hikyuu.util.check import HKUCheckError
from hikyuu.util.check import hku_catch
from hikyuu.util.check import hku_check
from hikyuu.util.check import hku_check_ignore
from hikyuu.util.check import hku_check_throw
from hikyuu.util.check import hku_to_async
from hikyuu.util.mylog import LoggingContext
from hikyuu.util.mylog import add_class_logger_handler
from hikyuu.util.mylog import capture_multiprocess_all_logger
from hikyuu.util.mylog import class_logger
from hikyuu.util.mylog import hku_benchmark
from hikyuu.util.mylog import hku_debug
from hikyuu.util.mylog import hku_debug as hku_trace
from hikyuu.util.mylog import hku_debug_if
from hikyuu.util.mylog import hku_debug_if as hku_trace_if
from hikyuu.util.mylog import hku_error
from hikyuu.util.mylog import hku_error_if
from hikyuu.util.mylog import hku_fatal
from hikyuu.util.mylog import hku_fatal_if
from hikyuu.util.mylog import hku_info
from hikyuu.util.mylog import hku_info_if
from hikyuu.util.mylog import hku_warn
from hikyuu.util.mylog import hku_warn_if
from hikyuu.util.mylog import set_my_logger_file
from hikyuu.util.mylog import spend_time
from hikyuu.util.mylog import with_trace
from hikyuu.util.notebook import in_interactive_session
from hikyuu.util.notebook import in_ipython_frontend
from hikyuu.util.slice import list_getitem
from hikyuu.util.timeout import timeout
import logging
import numpy as np
import os as os
import pandas as pd
from pathlib import Path
import pickle as pickle
import sys as sys
import traceback as traceback
from . import analysis
from . import core
from . import cpp
from . import draw
from . import extend
from . import hub
from . import trade_manage
from . import util
__all__ = ['A', 'ABS', 'ACOS', 'AD', 'ADVANCE', 'AF_EqualWeight', 'AF_FixedWeight', 'AF_FixedWeightList', 'AF_MultiFactor', 'ALIGN', 'AMA', 'AMO', 'ASIN', 'ATAN', 'ATR', 'AVEDEV', 'AllocateFundsBase', 'BACKSET', 'BARSCOUNT', 'BARSLAST', 'BARSLASTCOUNT', 'BARSSINCE', 'BARSSINCEN', 'BASE_DIR', 'BETWEEN', 'BLOCKSETNUM', 'BUSINESS', 'Block', 'BlockInfoDriver', 'BorrowRecord', 'BrokerPositionRecord', 'C', 'CAPITAL', 'CEILING', 'CLOSE', 'CN_Bool', 'CN_OPLine', 'CONST', 'CONTEXT', 'CONTEXT_K', 'CORR', 'COS', 'COST', 'COUNT', 'CROSS', 'CVAL', 'CYCLE', 'C_AMO', 'C_CLOSE', 'C_HIGH', 'C_KDATA', 'C_LOW', 'C_OPEN', 'C_VOL', 'ConditionBase', 'Constant', 'CostRecord', 'D', 'DATE', 'DAY', 'DEBUG', 'DECLINE', 'DEVSQ', 'DIFF', 'DIRECT', 'DISCARD', 'DMA', 'DOWNNDAY', 'DRAWBAND', 'DRAWBMP', 'DRAWICON', 'DRAWIMG', 'DRAWLINE', 'DRAWNULL', 'DRAWNUMBER', 'DRAWNUMBER_FIX', 'DRAWRECTREL', 'DRAWSL', 'DRAWTEXT', 'DRAWTEXT_FIX', 'DROPNA', 'DataDriverFactory', 'Datetime', 'DatetimeList', 'DatetimeList_to_df', 'DatetimeList_to_np', 'Datetime_date', 'Datetime_datetime', 'Days', 'EMA', 'ERROR', 'EVERY', 'EV_Bool', 'EV_TwoLine', 'EXIST', 'EXP', 'EasyTraderOrderBroker', 'EnvironmentBase', 'FATAL', 'FILTER', 'FINANCE', 'FLOOR', 'FundsRecord', 'H', 'HHV', 'HHVBARS', 'HIGH', 'HKUCheckError', 'HKUException', 'HOUR', 'HSL', 'Hours', 'IC', 'ICIR', 'IF', 'INBLOCK', 'INDEXA', 'INDEXADV', 'INDEXC', 'INDEXDEC', 'INDEXH', 'INDEXL', 'INDEXO', 'INDEXV', 'INFO', 'INSUM', 'INTPART', 'IR', 'ISINF', 'ISINFA', 'ISLASTBAR', 'ISNA', 'IndParam', 'Indicator', 'IndicatorImp', 'JUMPDOWN', 'JUMPUP', 'K', 'KALMAN', 'KDATA', 'KDATA_PART', 'KDJ', 'KData', 'KDataDriver', 'KDataToHdf5Importer', 'KData_getitem', 'KData_iter', 'KData_to_df', 'KData_to_np', 'KRecord', 'KRecordList', 'L', 'LAST', 'LASTVALUE', 'LIUTONGPAN', 'LLV', 'LLVBARS', 'LN', 'LOG', 'LOG_LEVEL', 'LONGCROSS', 'LOW', 'LoanRecord', 'LoggingContext', 'MA', 'MACD', 'MAX', 'MDD', 'MF_EqualWeight', 'MF_ICIRWeight', 'MF_ICWeight', 'MF_Weight', 'MIN', 'MINUTE', 'MM_FixedCapital', 'MM_FixedCapitalFunds', 'MM_FixedCount', 'MM_FixedCountTps', 'MM_FixedPercent', 'MM_FixedRisk', 'MM_FixedUnits', 'MM_Nothing', 'MM_WilliamsFixedRisk', 'MOD', 'MONTH', 'MRR', 'MailOrderBroker', 'MarketInfo', 'Microseconds', 'Milliseconds', 'Minutes', 'MoneyManagerBase', 'MultiFactorBase', 'NDAY', 'NOT', 'O', 'OFF', 'OPEN', 'OrderBrokerBase', 'OrderBrokerWrap', 'PF_Simple', 'PF_WithoutAF', 'PG_FixedHoldDays', 'PG_FixedPercent', 'PG_NoGoal', 'PLOYLINE', 'POS', 'POW', 'PRICELIST', 'Parameter', 'Parameter_items', 'Parameter_iter', 'Parameter_keys', 'Parameter_to_dict', 'Path', 'Performance', 'Performance_to_df', 'Portfolio', 'PositionList_to_df', 'PositionList_to_np', 'PositionRecord', 'PositionRecordList', 'ProfitGoalBase', 'Q', 'Query', 'RANK', 'RECOVER_BACKWARD', 'RECOVER_EQUAL_BACKWARD', 'RECOVER_EQUAL_FORWARD', 'RECOVER_FORWARD', 'REF', 'REPLACE', 'RESULT', 'REVERSE', 'RGB', 'ROC', 'ROCP', 'ROCR', 'ROCR100', 'ROUND', 'ROUNDDOWN', 'ROUNDUP', 'RSI', 'SAFTYLOSS', 'SE_EvaluateOptimal', 'SE_Fixed', 'SE_MaxFundsOptimal', 'SE_MultiFactor', 'SE_PerformanceOptimal', 'SE_Signal', 'SGN', 'SG_Add', 'SG_AllwaysBuy', 'SG_And', 'SG_Band', 'SG_Bool', 'SG_Buy', 'SG_Cross', 'SG_CrossGold', 'SG_Cycle', 'SG_Div', 'SG_Flex', 'SG_Mul', 'SG_OneSide', 'SG_Or', 'SG_Sell', 'SG_Single', 'SG_Single2', 'SG_Sub', 'SHOWICONS', 'SIN', 'SLICE', 'SLOPE', 'SMA', 'SPEARMAN', 'SP_FixedPercent', 'SP_FixedValue', 'SQRT', 'STD', 'STDEV', 'STDP', 'STICKLINE', 'ST_FixedPercent', 'ST_Indicator', 'ST_Saftyloss', 'SUM', 'SUMBARS', 'SYS_Simple', 'SYS_WalkForward', 'ScoreRecord', 'ScoreRecordList', 'Seconds', 'SelectorBase', 'SignalBase', 'SlippageBase', 'SpotRecord', 'Stock', 'StockManager', 'StockTypeInfo', 'StockWeight', 'StockWeightList', 'StoplossBase', 'Strategy', 'StrategyContext', 'System', 'SystemPart', 'SystemWeight', 'SystemWeightList', 'TAN', 'TA_ACCBANDS', 'TA_ACOS', 'TA_AD', 'TA_ADD', 'TA_ADOSC', 'TA_ADX', 'TA_ADXR', 'TA_APO', 'TA_AROON', 'TA_AROONOSC', 'TA_ASIN', 'TA_ATAN', 'TA_ATR', 'TA_AVGDEV', 'TA_AVGPRICE', 'TA_BBANDS', 'TA_BETA', 'TA_BOP', 'TA_CCI', 'TA_CDL2CROWS', 'TA_CDL3BLACKCROWS', 'TA_CDL3INSIDE', 'TA_CDL3LINESTRIKE', 'TA_CDL3OUTSIDE', 'TA_CDL3STARSINSOUTH', 'TA_CDL3WHITESOLDIERS', 'TA_CDLABANDONEDBABY', 'TA_CDLADVANCEBLOCK', 'TA_CDLBELTHOLD', 'TA_CDLBREAKAWAY', 'TA_CDLCLOSINGMARUBOZU', 'TA_CDLCONCEALBABYSWALL', 'TA_CDLCOUNTERATTACK', 'TA_CDLDARKCLOUDCOVER', 'TA_CDLDOJI', 'TA_CDLDOJISTAR', 'TA_CDLDRAGONFLYDOJI', 'TA_CDLENGULFING', 'TA_CDLEVENINGDOJISTAR', 'TA_CDLEVENINGSTAR', 'TA_CDLGAPSIDESIDEWHITE', 'TA_CDLGRAVESTONEDOJI', 'TA_CDLHAMMER', 'TA_CDLHANGINGMAN', 'TA_CDLHARAMI', 'TA_CDLHARAMICROSS', 'TA_CDLHIGHWAVE', 'TA_CDLHIKKAKE', 'TA_CDLHIKKAKEMOD', 'TA_CDLHOMINGPIGEON', 'TA_CDLIDENTICAL3CROWS', 'TA_CDLINNECK', 'TA_CDLINVERTEDHAMMER', 'TA_CDLKICKING', 'TA_CDLKICKINGBYLENGTH', 'TA_CDLLADDERBOTTOM', 'TA_CDLLONGLEGGEDDOJI', 'TA_CDLLONGLINE', 'TA_CDLMARUBOZU', 'TA_CDLMATCHINGLOW', 'TA_CDLMATHOLD', 'TA_CDLMORNINGDOJISTAR', 'TA_CDLMORNINGSTAR', 'TA_CDLONNECK', 'TA_CDLPIERCING', 'TA_CDLRICKSHAWMAN', 'TA_CDLRISEFALL3METHODS', 'TA_CDLSEPARATINGLINES', 'TA_CDLSHOOTINGSTAR', 'TA_CDLSHORTLINE', 'TA_CDLSPINNINGTOP', 'TA_CDLSTALLEDPATTERN', 'TA_CDLSTICKSANDWICH', 'TA_CDLTAKURI', 'TA_CDLTASUKIGAP', 'TA_CDLTHRUSTING', 'TA_CDLTRISTAR', 'TA_CDLUNIQUE3RIVER', 'TA_CDLUPSIDEGAP2CROWS', 'TA_CDLXSIDEGAP3METHODS', 'TA_CEIL', 'TA_CMO', 'TA_CORREL', 'TA_COS', 'TA_COSH', 'TA_DEMA', 'TA_DIV', 'TA_DX', 'TA_EMA', 'TA_EXP', 'TA_FLOOR', 'TA_HT_DCPERIOD', 'TA_HT_DCPHASE', 'TA_HT_PHASOR', 'TA_HT_SINE', 'TA_HT_TRENDLINE', 'TA_HT_TRENDMODE', 'TA_IMI', 'TA_KAMA', 'TA_LINEARREG', 'TA_LINEARREG_ANGLE', 'TA_LINEARREG_INTERCEPT', 'TA_LINEARREG_SLOPE', 'TA_LN', 'TA_LOG10', 'TA_MA', 'TA_MACD', 'TA_MACDEXT', 'TA_MACDFIX', 'TA_MAMA', 'TA_MAVP', 'TA_MAX', 'TA_MAXINDEX', 'TA_MEDPRICE', 'TA_MFI', 'TA_MIDPOINT', 'TA_MIDPRICE', 'TA_MIN', 'TA_MININDEX', 'TA_MINMAX', 'TA_MINMAXINDEX', 'TA_MINUS_DI', 'TA_MINUS_DM', 'TA_MOM', 'TA_MULT', 'TA_NATR', 'TA_OBV', 'TA_PLUS_DI', 'TA_PLUS_DM', 'TA_PPO', 'TA_ROC', 'TA_ROCP', 'TA_ROCR', 'TA_ROCR100', 'TA_RSI', 'TA_SAR', 'TA_SAREXT', 'TA_SIN', 'TA_SINH', 'TA_SMA', 'TA_SQRT', 'TA_STDDEV', 'TA_STOCH', 'TA_STOCHF', 'TA_STOCHRSI', 'TA_SUB', 'TA_SUM', 'TA_T3', 'TA_TAN', 'TA_TANH', 'TA_TEMA', 'TA_TRANGE', 'TA_TRIMA', 'TA_TRIX', 'TA_TSF', 'TA_TYPPRICE', 'TA_ULTOSC', 'TA_VAR', 'TA_WCLPRICE', 'TA_WILLR', 'TA_WMA', 'TC_FixedA', 'TC_FixedA2015', 'TC_FixedA2017', 'TC_TestStub', 'TC_Zero', 'TIME', 'TIMELINE', 'TIMELINEVOL', 'TR', 'TRACE', 'TURNOVER', 'TestOrderBroker', 'TimeDelta', 'TimeDelta_timedelta', 'TimeLineList', 'TimeLineRecord', 'TimeLine_to_df', 'TimeLine_to_np', 'TradeCostBase', 'TradeList_to_df', 'TradeList_to_np', 'TradeManager', 'TradeRecord', 'TradeRecordList', 'TradeRequest', 'TransList', 'TransList_to_df', 'TransList_to_np', 'TransRecord', 'UPNDAY', 'UTCOffset', 'V', 'VALUE', 'VAR', 'VARP', 'VIGOR', 'VOL', 'WARN', 'WEAVE', 'WEEK', 'WINNER', 'WITHDAY', 'WITHHALFYEAR', 'WITHHOUR', 'WITHHOUR2', 'WITHHOUR4', 'WITHKTYPE', 'WITHMIN', 'WITHMIN15', 'WITHMIN30', 'WITHMIN5', 'WITHMIN60', 'WITHMONTH', 'WITHQUARTER', 'WITHWEEK', 'WITHYEAR', 'WMA', 'YEAR', 'ZHBOND10', 'ZONGGUBEN', 'ZSCORE', 'active_device', 'add_class_logger_handler', 'add_local_hub', 'add_remote_hub', 'adjust_axes_show', 'analysis', 'analysis_sys_list', 'analysis_sys_list_multi', 'ax_draw_macd', 'ax_draw_macd2', 'ax_set_locator_formatter', 'backtest', 'batch_calculate_inds', 'blocka', 'blockbj', 'blockg', 'blocksh', 'blockstart', 'blocksz', 'blockzxb', 'broker', 'broker_easytrader', 'broker_mail', 'build_hub', 'can_upgrade', 'capture_multiprocess_all_logger', 'class_logger', 'close_ostream_to_python', 'close_spend_time', 'combinate_ind', 'combinate_ind_analysis', 'combinate_ind_analysis_multi', 'combinate_index', 'concat_to_df', 'constant', 'core', 'cpp', 'create_figure', 'crtAF', 'crtBrokerTM', 'crtCN', 'crtEV', 'crtMF', 'crtMM', 'crtOB', 'crtPG', 'crtSE', 'crtSEOptimal', 'crtSG', 'crtSP', 'crtST', 'crtTM', 'crt_pf_strategy', 'crt_sys_strategy', 'current_path', 'date', 'datetime', 'df_to_ind', 'dll_directory', 'draw', 'el', 'extend', 'fetch_trial_license', 'find_optimal_system', 'find_optimal_system_multi', 'gca', 'gcf', 'get_block', 'get_business_name', 'get_current_draw_engine', 'get_current_hub', 'get_data_from_buffer_server', 'get_date_range', 'get_global_context', 'get_hub_name_list', 'get_hub_path', 'get_kdata', 'get_last_version', 'get_log_level', 'get_part', 'get_part_info', 'get_part_list', 'get_part_module', 'get_part_name_list', 'get_stock', 'get_system_part_enum', 'get_system_part_name', 'get_version', 'get_version_git', 'get_version_with_build', 'help_part', 'hikyuu_init', 'hku_benchmark', 'hku_catch', 'hku_check', 'hku_check_ignore', 'hku_check_throw', 'hku_debug', 'hku_debug_if', 'hku_error', 'hku_error_if', 'hku_fatal', 'hku_fatal_if', 'hku_info', 'hku_info_if', 'hku_load', 'hku_logger', 'hku_save', 'hku_to_async', 'hku_trace', 'hku_trace_if', 'hku_warn', 'hku_warn_if', 'hub', 'in_interactive_session', 'in_ipython_frontend', 'indicator', 'indicator_getitem', 'indicator_iter', 'indicator_to_df', 'inner_analysis_sys_list', 'inner_combinate_ind_analysis', 'inner_combinate_ind_analysis_with_block', 'iodog', 'is_valid_license', 'isinf', 'isnan', 'kf', 'list_getitem', 'load_hikyuu', 'new_Query_init', 'new_path', 'np', 'old_Query_init', 'open_ostream_to_python', 'open_spend_time', 'os', 'part_clone', 'part_init', 'part_iter', 'pd', 'pickle', 'plugin_path', 'print_part_info', 'pyind', 'realtime_update_inner', 'realtime_update_wrap', 'remove_hub', 'remove_license', 'roundDown', 'roundEx', 'roundUp', 'run_in_strategy', 'search_part', 'select', 'select2', 'set_global_context', 'set_log_level', 'set_my_logger_file', 'set_python_in_interactive', 'set_python_in_jupyter', 'show_gcf', 'sm', 'spend_time', 'start_data_server', 'start_spot_agent', 'stop_data_server', 'stop_spot_agent', 'sys', 'talib_wrap', 'timedelta', 'timeout', 'toPriceList', 'traceback', 'trade', 'trade_manage', 'trade_sys', 'update_hub', 'use_draw_engine', 'util', 'view_license', 'vl', 'with_trace', 'zsbk_a', 'zsbk_bj', 'zsbk_cyb', 'zsbk_hs300', 'zsbk_sh', 'zsbk_sh180', 'zsbk_sh50', 'zsbk_sz', 'zsbk_zxb', 'zsbk_zz100']
class iodog:
    @staticmethod
    def close():
        ...
    @staticmethod
    def open():
        ...
def get_global_context():
    """
    
        获取当前的全局 K 线上下文
    
        :rtype: KData
        
    """
def hku_load(filename):
    """
    
        将通过 hku_save 保存的变量，读取到var中。
    
        :param str filename: 待载入的序列化文件。
        :return: 之前被序列化保存的文件    
        
    """
def hku_save(var, filename):
    """
    
        序列化，将hikyuu内建类型的变量（如Stock、TradeManager等）保存在指定的文件中，格式为XML。
    
        :param var: hikyuu内建类型的变量
        :param str filename: 指定的文件名
        
    """
def load_hikyuu(**kwargs):
    """
    
        初始化加载 hikyuu 数据库，并初始化全局变量。
    
        示例:
    
        # 仅预加载 sh000001 日线数据，关闭行情接收
        options = {
            "stock_list": ["sh000001"],
            "ktype_list": ["day"],
            "preload_num: {"day_max": 100000}
            "load_history_finance": False,
            "load_weight": False,
            "start_spot": False,
            "spot_worker_num": 1,
        }
        load_hikyuu(**options)
    
        参数:
            config_file (str): 配置文件路径，默认为 ~/.hikyuu/hikyuu.ini
    
            stock_list (list): 指定加载的股票列表，默认为全部A股('all'), 如：['sh000001', 'sz399001']
            ktype_list (list): 指定加载的K线类型列表，默认按配置文件设置加载. 如: ['day', 'week', 'month']
                        支持的K线类型有:
                        'day', 'week', 'month', 'quarter', 'halfyear', 'year', 'min', 'min5',
                        'min15', 'min30', 'min60', 'hour2'
            preload_num (dict): {'day_max': 100000, 'week_max': 100000, 'month_max': 100000, ...}
            load_history_finance (boolean): 预加载历史财务数至内存，默认为 True
            load_weight (boolean): 加载权息数据，默认为 True
    
            start_spot (boolean): 启动行情接收，默认为 True
            spot_worker_num (int): 行情接收数据处理线程数，默认为 1
        
    """
def realtime_update_inner(source = 'qq', stk_list = None):
    ...
def realtime_update_wrap():
    ...
def select(cond, start = ..., end = ..., print_out = True):
    """
    
        示例：
        #选出涨停股
        C = CLOSE()
        x = select(C / REF(C, 1) - 1 >= 0.0995)
    
        :param Indicator cond: 条件指标
        :param Datetime start: 起始日期
        :param Datetime end: 结束日期
        :param bool print_out: 打印选中的股票
        :rtype: 选中的股票列表
        
    """
def select2(inds, start = ..., end = ..., stks = None):
    """
    导出最后时刻指定证券的所有指定指标值
    
        如：
            select2([CLOSE(), VOLUME()], stks=blocka)
    
        返回一个DataFrame, 列名是指标名称, 行是证券代码和证券名称:
    
            证券代码  证券名称  CLOSE  VOLUME
            SH600000 浦发银行  14.09   1000
            SH600001 中国平安  13.09   2000
            SZ000001 平安银行  13.09   3000
            ...
    
        :param Indicator inds: 指标列表
        :param Datetime start: 起始日期
        :param Datetime end: 结束日期（不包括该日期）
        :param list stks: 指定的证券列表
        :rtype: pandas.DataFrame
        
    """
def set_global_context(stk, query):
    """
    
        设置全局的 K 线上下文
    
        :param Stock stk: 指定的全局Stock
        :param Query query: 指定的查询条件
        
    """
A: cpp.core39.Indicator  # value = Indicator{...
AMO: cpp.core39.Indicator  # value = Indicator{...
BASE_DIR: str = 'D:\\workspace\\hikyuu\\hikyuu'
C: cpp.core39.Indicator  # value = Indicator{...
CLOSE: cpp.core39.Indicator  # value = Indicator{...
DEBUG: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.DEBUG: 1>
DRAWNULL: float  # value = nan
ERROR: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.ERROR: 4>
FATAL: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.FATAL: 5>
H: cpp.core39.Indicator  # value = Indicator{...
HIGH: cpp.core39.Indicator  # value = Indicator{...
INFO: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.INFO: 2>
K = None
KDATA: cpp.core39.Indicator  # value = Indicator{...
L: cpp.core39.Indicator  # value = Indicator{...
LOW: cpp.core39.Indicator  # value = Indicator{...
O: cpp.core39.Indicator  # value = Indicator{...
OFF: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.OFF: 6>
OPEN: cpp.core39.Indicator  # value = Indicator{...
TRACE: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.TRACE: 0>
V: cpp.core39.Indicator  # value = Indicator{...
VOL: cpp.core39.Indicator  # value = Indicator{...
WARN: cpp.core39.LOG_LEVEL  # value = <LOG_LEVEL.WARN: 3>
__copyright__: str = '\nApache License Version 2.0\n\nCopyright (c) 2010-2017 fasiondog\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the "Software"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n'
__version__: str = '2.6.5'
blocka = None
blockbj = None
blockg = None
blocksh = None
blockstart = None
blocksz = None
blockzxb = None
constant: cpp.core39.Constant  # value = <hikyuu.cpp.core39.Constant object>
current_path: str = 'D:\\anaconda3\\envs\\py39;D:\\anaconda3\\envs\\py39\\Library\\mingw-w64\\bin;D:\\anaconda3\\envs\\py39\\Library\\usr\\bin;D:\\anaconda3\\envs\\py39\\Library\\bin;D:\\anaconda3\\envs\\py39\\Scripts;D:\\anaconda3\\envs\\py39\\bin;D:\\anaconda3\\condabin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\libnvvp;C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\Calibre2;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\dotnet;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Go\\bin;C:\\Program Files\\nodejs;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.1.0;C:\\Program Files\\CMake\\bin;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\xmake;d:\\anaconda3;d:\\anaconda3\\Library\\mingw-w64\\bin;d:\\anaconda3\\Library\\usr\\bin;d:\\anaconda3\\Library\\bin;d:\\anaconda3\\Scripts;C:\\Qt\\6.8.0\\msvc2022_64\\bin;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\xmake;D:\\mysql-8.0.27-winx64\\bin;C:\\Program Files\\Graphviz\\bin;D:\\mongodb-win32-x86_64-windows-5.0.6\\bin;C:\\tools\\apache-maven-3.8.6\\bin;C:\\Program Files\\7-Zip;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\tools;C:\\Users\\<USER>\\go\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\NVDIA\\CUDNN\\v9.0\\bin;.'
dll_directory: str = 'D:\\workspace\\hikyuu\\hikyuu\\cpp'
hku_logger: logging.Logger  # value = <Logger hikyuu (INFO)>
new_path: str = 'D:\\workspace\\hikyuu\\hikyuu\\cpp;D:\\anaconda3\\envs\\py39;D:\\anaconda3\\envs\\py39\\Library\\mingw-w64\\bin;D:\\anaconda3\\envs\\py39\\Library\\usr\\bin;D:\\anaconda3\\envs\\py39\\Library\\bin;D:\\anaconda3\\envs\\py39\\Scripts;D:\\anaconda3\\envs\\py39\\bin;D:\\anaconda3\\condabin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\libnvvp;C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\Calibre2;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\dotnet;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Go\\bin;C:\\Program Files\\nodejs;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.1.0;C:\\Program Files\\CMake\\bin;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\xmake;d:\\anaconda3;d:\\anaconda3\\Library\\mingw-w64\\bin;d:\\anaconda3\\Library\\usr\\bin;d:\\anaconda3\\Library\\bin;d:\\anaconda3\\Scripts;C:\\Qt\\6.8.0\\msvc2022_64\\bin;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\xmake;D:\\mysql-8.0.27-winx64\\bin;C:\\Program Files\\Graphviz\\bin;D:\\mongodb-win32-x86_64-windows-5.0.6\\bin;C:\\tools\\apache-maven-3.8.6\\bin;C:\\Program Files\\7-Zip;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\tools;C:\\Users\\<USER>\\go\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\NVDIA\\CUDNN\\v9.0\\bin;.'
plugin_path: str = 'C:\\Users\\<USER>\\.hikyuu\\plugin'
sm: cpp.core39.StockManager  # value = <hikyuu.cpp.core39.StockManager object>
zsbk_a = None
zsbk_bj = None
zsbk_cyb = None
zsbk_hs300 = None
zsbk_sh = None
zsbk_sh180 = None
zsbk_sh50 = None
zsbk_sz = None
zsbk_zxb = None
zsbk_zz100 = None
