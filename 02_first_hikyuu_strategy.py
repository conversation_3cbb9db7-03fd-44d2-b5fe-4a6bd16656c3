#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一个Hikyuu策略：双均线交叉策略
这是一个经典的技术分析策略，适合初学者理解Hikyuu的基本概念
"""

from hikyuu.interactive import *

def basic_double_ma_strategy():
    """基础双均线策略"""
    print("=" * 60)
    print("  📈 双均线交叉策略")
    print("=" * 60)
    
    # 1. 选择股票
    stock_code = 'sz000001'  # 平安银行
    stock = sm[stock_code]
    print(f"选择股票: {stock.name} ({stock.market_code}{stock.code})")
    
    # 2. 设置回测参数
    days = 250  # 回测天数
    init_cash = 100000  # 初始资金10万
    
    # 3. 获取K线数据
    query = Query(-days)
    kdata = stock.get_kdata(query)
    print(f"K线数据: {len(kdata)} 条记录")
    print(f"数据范围: {kdata[0].datetime} 到 {kdata[-1].datetime}")
    
    # 4. 创建交易账户
    my_tm = crtTM(init_cash=init_cash)
    print(f"创建交易账户: 初始资金 {init_cash}")
    
    # 5. 计算技术指标
    close_data = CLOSE(kdata)  # 收盘价序列
    ma5 = MA(close_data, n=5)   # 5日均线
    ma20 = MA(close_data, n=20) # 20日均线
    
    print(f"技术指标:")
    print(f"  MA5 (5日均线): {len(ma5)} 个值")
    print(f"  MA20 (20日均线): {len(ma20)} 个值")
    print(f"  最新MA5: {ma5[-1]:.2f}")
    print(f"  最新MA20: {ma20[-1]:.2f}")
    
    # 6. 创建信号指示器
    # SG_Cross: 当快线上穿慢线时买入，下穿时卖出
    my_sg = SG_Cross(ma5, ma20)
    print(f"信号指示器: 5日线与20日线交叉")
    
    # 7. 创建资金管理策略
    # MM_FixedCount: 每次买入固定股数
    my_mm = MM_FixedCount(1000)  # 每次买入1000股
    print(f"资金管理: 每次买入1000股")
    
    # 8. 创建交易系统
    sys = SYS_Simple(
        tm=my_tm,    # 交易管理器
        sg=my_sg,    # 信号指示器
        mm=my_mm     # 资金管理
    )
    print(f"创建交易系统: SYS_Simple")
    
    # 9. 运行回测
    print(f"\n开始回测...")
    sys.run(stock, query)
    print(f"回测完成!")
    
    # 10. 分析结果
    print(f"\n" + "=" * 40)
    print(f"📊 回测结果分析")
    print(f"=" * 40)
    
    # 基本财务指标
    initial_cash = my_tm.init_cash
    final_cash = my_tm.current_cash
    total_return = final_cash - initial_cash
    return_rate = (total_return / initial_cash) * 100
    
    print(f"💰 资金情况:")
    print(f"  初始资金: {initial_cash:,.2f}")
    print(f"  最终资金: {final_cash:,.2f}")
    print(f"  总收益: {total_return:,.2f}")
    print(f"  收益率: {return_rate:.2f}%")
    
    # 交易统计
    trades = my_tm.get_trade_list()
    print(f"\n📈 交易统计:")
    print(f"  总交易次数: {len(trades)}")
    
    if len(trades) > 0:
        # 分析盈亏交易
        profit_trades = 0
        loss_trades = 0
        
        for trade in trades:
            if trade.business != 0:  # 排除初始化记录
                # 简化的盈亏判断（实际应该配对买卖计算）
                if trade.real_price > 0:
                    profit_trades += 1
                else:
                    loss_trades += 1
        
        print(f"  盈利交易: {profit_trades}")
        print(f"  亏损交易: {loss_trades}")
        
        # 显示最近几笔交易
        print(f"\n🔄 最近交易记录:")
        recent_trades = trades[-5:] if len(trades) >= 5 else trades
        for i, trade in enumerate(recent_trades):
            if trade.business != 0:  # 排除初始化记录
                action = "买入" if trade.business == 1 else "卖出"
                print(f"  {i+1}. {trade.datetime} {action} {trade.number}股 @{trade.real_price:.2f}")
    
    # 持仓情况
    positions = my_tm.get_position_list()
    print(f"\n📋 持仓情况:")
    print(f"  持仓数量: {len(positions)}")
    
    return sys, my_tm

def analyze_signals(stock_code='sz000001', days=50):
    """分析信号产生情况"""
    print(f"\n" + "=" * 40)
    print(f"🔍 信号分析")
    print(f"=" * 40)
    
    stock = sm[stock_code]
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    ma5 = MA(close_data, n=5)
    ma20 = MA(close_data, n=20)
    
    # 创建信号指示器
    sg = SG_Cross(ma5, ma20)
    
    # 分析信号
    buy_signals = []
    sell_signals = []
    
    for i in range(len(kdata)):
        if sg.should_buy(kdata[i].datetime, stock):
            buy_signals.append((kdata[i].datetime, kdata[i].close))
        elif sg.should_sell(kdata[i].datetime, stock):
            sell_signals.append((kdata[i].datetime, kdata[i].close))
    
    print(f"分析期间: 最近{days}天")
    print(f"买入信号: {len(buy_signals)} 个")
    print(f"卖出信号: {len(sell_signals)} 个")
    
    if buy_signals:
        print(f"\n📈 买入信号:")
        for date, price in buy_signals[-3:]:  # 显示最近3个
            print(f"  {date} @{price:.2f}")
    
    if sell_signals:
        print(f"\n📉 卖出信号:")
        for date, price in sell_signals[-3:]:  # 显示最近3个
            print(f"  {date} @{price:.2f}")

def compare_different_periods():
    """比较不同均线周期的效果"""
    print(f"\n" + "=" * 40)
    print(f"📊 不同周期比较")
    print(f"=" * 40)
    
    stock = sm['sz000001']
    query = Query(-200)
    
    # 测试不同的均线组合
    combinations = [
        (5, 10, "短期"),
        (5, 20, "中期"),
        (10, 30, "长期")
    ]
    
    results = []
    
    for fast, slow, name in combinations:
        # 创建交易系统
        tm = crtTM(init_cash=100000)
        kdata = stock.get_kdata(query)
        close_data = CLOSE(kdata)
        ma_fast = MA(close_data, n=fast)
        ma_slow = MA(close_data, n=slow)
        sg = SG_Cross(ma_fast, ma_slow)
        mm = MM_FixedCount(1000)
        
        sys = SYS_Simple(tm=tm, sg=sg, mm=mm)
        sys.run(stock, query)
        
        # 计算收益率
        return_rate = ((tm.current_cash - tm.init_cash) / tm.init_cash) * 100
        trades = tm.get_trade_list()
        
        results.append({
            'name': name,
            'fast': fast,
            'slow': slow,
            'return_rate': return_rate,
            'trades': len(trades)
        })
        
        print(f"{name}策略 (MA{fast}/MA{slow}): 收益率 {return_rate:.2f}%, 交易次数 {len(trades)}")
    
    # 找出最佳策略
    best = max(results, key=lambda x: x['return_rate'])
    print(f"\n🏆 最佳策略: {best['name']} (MA{best['fast']}/MA{best['slow']})")
    print(f"   收益率: {best['return_rate']:.2f}%")

def main():
    """主函数"""
    print("🚀 开始Hikyuu学习之旅!")
    
    # 1. 运行基础策略
    sys, tm = basic_double_ma_strategy()
    
    # 2. 分析信号
    analyze_signals()
    
    # 3. 比较不同周期
    compare_different_periods()
    
    print(f"\n" + "=" * 60)
    print(f"🎉 第一个Hikyuu策略学习完成!")
    print(f"=" * 60)
    print(f"📚 学习要点:")
    print(f"  1. 理解了Hikyuu的基本组件: TM, SG, MM, SYS")
    print(f"  2. 学会了创建简单的双均线策略")
    print(f"  3. 掌握了回测结果的基本分析")
    print(f"  4. 了解了参数对策略效果的影响")
    print(f"\n🎯 下一步建议:")
    print(f"  1. 尝试修改均线周期参数")
    print(f"  2. 测试不同的股票")
    print(f"  3. 学习其他技术指标")
    print(f"  4. 添加止损止盈策略")
    print(f"=" * 60)

if __name__ == "__main__":
    main()
