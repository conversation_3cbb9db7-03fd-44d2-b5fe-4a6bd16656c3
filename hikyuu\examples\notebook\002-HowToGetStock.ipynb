{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-10-19 20:43:08,440 [INFO] hikyuu version: 2.2.1_202410020224_RELEASE_windows_x64 [<module>] (D:\\workspace\\hikyuu\\hikyuu\\__init__.py:97) [hikyuu::hku_info]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-10-19 20:43:09.048 [HKU-I] - Using SQLITE3 BaseInfoDriver (BaseInfoDriver.cpp:57)\n", "2024-10-19 20:43:09.053 [HKU-I] - Loading market information... (StockManager.cpp:454)\n", "2024-10-19 20:43:09.054 [HKU-I] - Loading stock type information... (StockManager.cpp:472)\n", "2024-10-19 20:43:09.054 [HKU-I] - Loading stock information... (StockManager.cpp:374)\n", "2024-10-19 20:43:09.268 [HKU-I] - Loading stock weight... (StockManager.cpp:490)\n", "2024-10-19 20:43:09.442 [HKU-I] - Loading block... (StockManager.cpp:113)\n", "2024-10-19 20:43:09.703 [HKU-I] - Loading KData... (StockManager.cpp:117)\n", "2024-10-19 20:43:09.703 [HKU-I] - Preloading all day kdata to buffer ! (StockManager.cpp:153)\n", "2024-10-19 20:43:09.704 [HKU-I] - 0.65s Loaded Data. (StockManager.cpp:125)\n", "CPU times: total: 734 ms\n", "Wall time: 1.85 s\n"]}], "source": ["%matplotlib inline\n", "%time from hikyuu.interactive import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1 全局获取股票对象\n", "==========\n", "\n", "1.1 获取股票对象\n", "-----------------\n", "\n", "通过全局管理对象 sm，或使用函数 get_stock。股票标识格式“市场标识+股票代码”，市场标识：沪市sh，深市sz。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stock(SH, 000001, 上证指数, 指数, 1, 1990-12-20 00:00:00, +infinity)\n"]}], "source": ["#s = getStock('sh000001')\n", "s = sm['sh000001']\n", "print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1.2 遍历所有股票\n", "-----------------"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["全部数量: 9294\n"]}, {"data": {"text/plain": ["9294"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["i = 0\n", "#遍历所有股票\n", "for s in sm:\n", "    i += 1\n", "    #print(s)\n", "print(\"全部数量:\", i)\n", "\n", "len(sm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["2 通过板块（Block）遍历股票对象\n", "================\n", "\n", "通过 sm.get_stock(\"板块分类\", \"板块名称\") 获取相应板块"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["300\n"]}], "source": ["blk = sm.get_block(\"指数板块\", \"沪深300\")\n", "print(len(blk))\n", "# for s in blk:\n", "#     if s.valid:\n", "#         print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["3 查看权息信息\n", "======="]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Weight(1990-03-01 00:00:00, 0, 1, 3.559, 0, 0, 0, 0, 0)\n", "Weight(1991-04-03 00:00:00, 0, 0, 0, 0, 0, 4850, 2650, 0)\n", "Weight(1991-05-02 00:00:00, 4, 0, 0, 3, 0, 8975, 3949, 0)\n", "Weight(1991-08-17 00:00:00, 10, 0, 0, 0, 0, 8975, 3949, 0)\n", "Weight(1992-03-23 00:00:00, 5, 0, 0, 2, 0, 8975, 3949, 0)\n", "Weight(1993-05-24 00:00:00, 8.5, 1, 16, 3, 0, 26942, 18813, 0)\n", "Weight(1993-06-30 00:00:00, 0, 0, 0, 0, 0, 26125, 18004, 0)\n", "Weight(1994-07-09 00:00:00, 0, 0, 0, 0, 0, 28738, 19805, 0)\n", "Weight(1994-07-11 00:00:00, 5, 1, 5, 5, 0, 28738, 19805, 0)\n", "Weight(1994-07-14 00:00:00, 0, 0, 0, 0, 0, 43107, 29707, 0)\n", "Weight(1995-09-25 00:00:00, 2, 0, 0, 3, 0, 43107, 29707, 0)\n", "Weight(1995-09-27 00:00:00, 0, 0, 0, 0, 0, 51728, 35649, 0)\n", "Weight(1996-05-27 00:00:00, 10, 0, 0, 0, 0, 51728, 35649, 0)\n", "Weight(1996-05-29 00:00:00, 0, 0, 0, 0, 0, 103456, 71442, 0)\n", "Weight(1997-08-25 00:00:00, 5, 0, 0, 2, 0, 103456, 71442, 0)\n", "Weight(1997-08-27 00:00:00, 0, 0, 0, 0, 0, 155185, 107163, 0)\n", "Weight(1999-10-18 00:00:00, 0, 0, 0, 6, 0, 155185, 107163, 0)\n", "Weight(2000-11-06 00:00:00, 0, 3, 8, 0, 0, 155185, 107163, 0)\n", "Weight(2000-12-08 00:00:00, 0, 0, 0, 0, 0, 194582, 139312, 0)\n", "Weight(2001-10-15 00:00:00, 0, 0, 0, 0, 0, 194582, 140936, 0)\n", "Weight(2002-07-23 00:00:00, 0, 0, 0, 1.5, 0, 194582, 140936, 0)\n", "Weight(2003-09-29 00:00:00, 0, 0, 0, 1.5, 0, 194582, 140936, 0)\n", "Weight(2007-06-18 00:00:00, 1, 0, 0, 0, 0, 194582, 140936, 0)\n", "Weight(2007-06-20 00:00:00, 0, 0, 0, 0, 0, 208676, 155019, 0)\n", "Weight(2007-12-28 00:00:00, 0, 0, 0, 0, 0, 229341, 170327, 0)\n", "Weight(2007-12-31 00:00:00, 0, 0, 0, 0, 0, 229341, 170326, 0)\n", "Weight(2008-01-21 00:00:00, 0, 0, 0, 0, 0, 229341, 175682, 0)\n", "Weight(2008-06-26 00:00:00, 0, 0, 0, 0, 0, 229341, 204652, 0)\n", "Weight(2008-06-27 00:00:00, 0, 0, 0, 0, 0, 238880, 214200, 0)\n", "Weight(2008-10-31 00:00:00, 3, 0, 0, 0.335, 0, 310543, 278461, 0)\n", "Weight(2009-06-22 00:00:00, 0, 0, 0, 0, 0, 310543, 292367, 0)\n", "Weight(2009-06-30 00:00:00, 0, 0, 0, 0, 0, 310543, 292376, 0)\n", "Weight(2009-10-15 00:00:00, 0, 0, 0, 0, 0, 310543, 292411, 0)\n", "Weight(2010-06-28 00:00:00, 0, 0, 0, 0, 0, 310543, 310537, 0)\n", "Weight(2010-06-29 00:00:00, 0, 0, 0, 0, 0, 348501, 310537, 0)\n", "Weight(2010-06-30 00:00:00, 0, 0, 0, 0, 0, 348501, 310537, 0)\n", "Weight(2010-12-31 00:00:00, 0, 0, 0, 0, 0, 348501, 310536, 0)\n", "Weight(2011-06-30 00:00:00, 0, 0, 0, 0, 0, 348501, 310536, 0)\n", "Weight(2011-08-05 00:00:00, 0, 0, 0, 0, 0, 512335, 310536, 0)\n", "Weight(2012-10-19 00:00:00, 0, 0, 0, 1, 0, 512335, 310536, 0)\n", "Weight(2013-06-20 00:00:00, 6, 0, 0, 1.7, 0, 819736, 496857, 0)\n", "Weight(2013-11-12 00:00:00, 0, 0, 0, 0, 0, 819736, 557590, 0)\n", "Weight(2014-01-09 00:00:00, 0, 0, 0, 0, 0, 952075, 557590, 0)\n", "Weight(2014-06-12 00:00:00, 2, 0, 0, 1.6, 0, 1.14249e+06, 669106, 0)\n", "Weight(2014-09-01 00:00:00, 0, 0, 0, 0, 0, 1.14249e+06, 983671, 0)\n", "Weight(2015-04-13 00:00:00, 2, 0, 0, 1.74, 0, 1.37099e+06, 1.18041e+06, 0)\n", "Weight(2015-05-21 00:00:00, 0, 0, 0, 0, 0, 1.43087e+06, 1.18041e+06, 0)\n", "Weight(2016-05-23 00:00:00, 0, 0, 0, 0, 0, 1.43087e+06, 1.21926e+06, 0)\n", "Weight(2016-06-16 00:00:00, 2, 0, 0, 1.529, 0, 1.71704e+06, 1.46312e+06, 0)\n", "Weight(2017-01-09 00:00:00, 0, 0, 0, 0, 0, 1.71704e+06, 1.6918e+06, 0)\n", "Weight(2017-07-21 00:00:00, 0, 0, 0, 1.58, 0, 1.71704e+06, 1.6918e+06, 0)\n", "Weight(2017-12-31 00:00:00, 0, 0, 0, 0, 0, 1.71704e+06, 1.6918e+06, 0)\n", "Weight(2018-05-21 00:00:00, 0, 0, 0, 0, 0, 1.71704e+06, 1.71702e+06, 0)\n", "Weight(2018-07-12 00:00:00, 0, 0, 0, 1.36, 0, 1.71704e+06, 1.71702e+06, 0)\n", "Weight(2019-06-26 00:00:00, 0, 0, 0, 1.45, 0, 1.71704e+06, 1.71702e+06, 0)\n", "Weight(2019-06-30 00:00:00, 0, 0, 0, 0, 0, 1.71704e+06, 1.71702e+06, 0)\n", "Weight(2019-09-18 00:00:00, 0, 0, 0, 0, 0, 1.94059e+06, 1.94058e+06, 0)\n", "Weight(2020-05-28 00:00:00, 0, 0, 0, 2.18, 0, 1.94059e+06, 1.94058e+06, 0)\n", "Weight(2020-12-31 00:00:00, 0, 0, 0, 0, 0, 1.94059e+06, 1.94058e+06, 0)\n", "Weight(2021-05-14 00:00:00, 0, 0, 0, 1.799, 0, 1.94059e+06, 1.94058e+06, 0)\n", "Weight(2021-12-31 00:00:00, 0, 0, 0, 0, 0, 1.94059e+06, 1.94055e+06, 0)\n", "Weight(2022-06-30 00:00:00, 0, 0, 0, 0, 0, 1.94059e+06, 1.94056e+06, 0)\n", "Weight(2022-07-22 00:00:00, 0, 0, 0, 2.279, 0, 1.94059e+06, 1.94056e+06, 0)\n", "Weight(2023-06-14 00:00:00, 0, 0, 0, 2.849, 0, 1.94059e+06, 1.94056e+06, 0)\n", "Weight(2024-06-14 00:00:00, 0, 0, 0, 7.19, 0, 1.94059e+06, 1.94056e+06, 0)\n", "Weight(2024-06-30 00:00:00, 0, 0, 0, 0, 0, 1.94059e+06, 1.94056e+06, 0)\n", "Weight(2024-10-10 00:00:00, 0, 0, 0, 2.46, 0, 1.94059e+06, 1.94056e+06, 0)\n"]}], "source": ["ws = sm['sz000001'].get_weight()\n", "for w in ws:\n", "    print(w)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}