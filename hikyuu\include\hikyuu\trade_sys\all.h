/*
 * all.h
 *
 *  Created on: 2013-5-5
 *      Author: fasiondog
 */

#pragma once
#ifndef ALL_TRADE_SYS_H_
#define ALL_TRADE_SYS_H_

#include "signal/build_in.h"
#include "condition/build_in.h"
#include "environment/build_in.h"
#include "moneymanager/build_in.h"
#include "profitgoal/build_in.h"
#include "slippage/build_in.h"
#include "stoploss/build_in.h"
#include "system/build_in.h"
#include "allocatefunds/build_in.h"
#include "selector/build_in.h"
#include "multifactor/build_in.h"

#endif /* ALL_TRADE_SYS_H_ */
