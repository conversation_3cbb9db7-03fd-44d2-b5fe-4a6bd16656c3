#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查交易记录属性
"""

import sys
from pathlib import Path

# 添加hikyuu路径
hikyuu_path = Path(__file__).parent / "hikyuu"
if hikyuu_path.exists():
    sys.path.insert(0, str(hikyuu_path))

try:
    from hikyuu.interactive import *
    print("✅ Hikyuu 导入成功")
except ImportError as e:
    print(f"❌ Hikyuu 导入失败: {e}")
    sys.exit(1)

def check_trade_attributes():
    """检查交易记录属性"""
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建双均线策略
        kdata = stock.get_kdata(Query(-100))
        close_data = CLOSE(kdata)
        ma5 = MA(close_data, n=5)
        ma20 = MA(close_data, n=20)
        
        # 创建信号指示器
        my_sg = SG_Cross(ma5, ma20)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-100))
        
        # 获取交易记录
        trade_list = my_tm.get_trade_list()
        print(f"交易记录数量: {len(trade_list)}")
        
        if len(trade_list) > 0:
            trade = trade_list[0]
            print(f"交易记录类型: {type(trade)}")
            print(f"交易记录属性: {[attr for attr in dir(trade) if not attr.startswith('_')]}")
            
            # 检查具体属性
            if hasattr(trade, 'datetime'):
                print(f"datetime属性: {trade.datetime}")
            if hasattr(trade, 'realPrice'):
                print(f"realPrice属性: {trade.realPrice}")
            if hasattr(trade, 'number'):
                print(f"number属性: {trade.number}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_trade_attributes()
