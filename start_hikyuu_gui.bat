@echo off
chcp 65001 >nul
title Hikyuu 量化交易策略开发平台

echo.
echo ========================================
echo   Hikyuu 量化交易策略开发平台
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.9+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 已安装
echo.

REM 检查必要的依赖包
echo 📦 检查依赖包...
python -c "import tkinter, matplotlib, pandas, numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要的依赖包，正在安装...
    pip install matplotlib pandas numpy
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请手动安装:
        echo pip install matplotlib pandas numpy
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.

REM 检查Hikyuu是否安装
echo 🔍 检查Hikyuu框架...
python -c "import hikyuu" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  未找到Hikyuu框架，正在安装...
    pip install hikyuu
    if errorlevel 1 (
        echo ❌ Hikyuu安装失败，请手动安装:
        echo pip install hikyuu
        pause
        exit /b 1
    )
)

echo ✅ Hikyuu框架检查完成
echo.

REM 启动GUI
echo 🚀 启动Hikyuu量化交易平台...
echo.
python hikyuu_gui.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
)
