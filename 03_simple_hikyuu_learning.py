#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Hikyuu学习示例
专注于核心功能，避免复杂的API调用
"""

from hikyuu.interactive import *

def lesson1_basic_data():
    """第1课：基础数据操作"""
    print("=" * 60)
    print("  📚 第1课：基础数据操作")
    print("=" * 60)
    
    # 1. 获取股票对象
    stock = sm['sz000001']
    print(f"股票信息: {stock.name} ({stock.market_code}{stock.code})")
    print(f"股票类型: {stock.type}")
    print(f"上市日期: {stock.start_datetime}")
    
    # 2. 获取K线数据
    kdata = stock.get_kdata(Query(-10))  # 最近10天
    print(f"\nK线数据: {len(kdata)} 条记录")
    
    # 3. 查看具体数据
    print(f"\n最近3天的K线数据:")
    for i in range(min(3, len(kdata))):
        k = kdata[-(i+1)]  # 从最新开始
        print(f"  {k.datetime}: 开{k.open:.2f} 高{k.high:.2f} 低{k.low:.2f} 收{k.close:.2f} 量{k.volume}")
    
    # 4. 获取价格序列
    close_data = CLOSE(kdata)
    print(f"\n收盘价序列: {len(close_data)} 个值")
    print(f"最新收盘价: {close_data[-1]:.2f}")
    
    return stock, kdata

def lesson2_technical_indicators():
    """第2课：技术指标计算"""
    print("\n" + "=" * 60)
    print("  📊 第2课：技术指标计算")
    print("=" * 60)
    
    # 获取数据
    stock = sm['sz000001']
    kdata = stock.get_kdata(Query(-50))  # 50天数据
    close_data = CLOSE(kdata)
    
    # 1. 移动平均线
    ma5 = MA(close_data, n=5)
    ma10 = MA(close_data, n=10)
    ma20 = MA(close_data, n=20)
    
    print(f"移动平均线:")
    print(f"  MA5:  {ma5[-1]:.2f}")
    print(f"  MA10: {ma10[-1]:.2f}")
    print(f"  MA20: {ma20[-1]:.2f}")
    
    # 2. RSI指标
    rsi = RSI(close_data, n=14)
    print(f"\nRSI指标:")
    print(f"  RSI(14): {rsi[-1]:.2f}")
    
    # 3. MACD指标
    macd = MACD(close_data)
    print(f"\nMACD指标:")
    print(f"  MACD: {macd[-1]:.4f}")
    
    # 4. 成交量指标
    vol_data = VOL(kdata)
    print(f"\n成交量:")
    print(f"  最新成交量: {vol_data[-1]:.0f}")

    # 5. 价格变化
    current_price = close_data[-1]
    prev_price = close_data[-2] if len(close_data) > 1 else current_price
    price_change = current_price - prev_price
    change_pct = (price_change / prev_price) * 100 if prev_price != 0 else 0

    print(f"\n价格变化:")
    print(f"  当前价格: {current_price:.2f}")
    print(f"  价格变化: {price_change:+.2f} ({change_pct:+.2f}%)")
    
    return ma5, ma10, ma20, rsi

def lesson3_simple_strategy():
    """第3课：简单策略构建"""
    print("\n" + "=" * 60)
    print("  🎯 第3课：简单策略构建")
    print("=" * 60)
    
    # 1. 基础设置
    stock = sm['sz000001']
    init_cash = 100000
    days = 100
    
    print(f"策略设置:")
    print(f"  股票: {stock.name}")
    print(f"  初始资金: {init_cash:,}")
    print(f"  回测天数: {days}")
    
    # 2. 创建交易账户
    my_tm = crtTM(init_cash=init_cash)
    
    # 3. 获取数据和指标
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    ma5 = MA(close_data, n=5)
    ma20 = MA(close_data, n=20)
    
    # 4. 创建信号指示器（双均线交叉）
    my_sg = SG_Cross(ma5, ma20)
    
    # 5. 创建资金管理（固定股数）
    my_mm = MM_FixedCount(1000)
    
    # 6. 创建交易系统
    sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
    
    # 7. 运行回测
    print(f"\n开始回测...")
    sys.run(stock, Query(-days))
    
    # 8. 分析结果
    final_cash = my_tm.current_cash
    total_return = final_cash - init_cash
    return_rate = (total_return / init_cash) * 100
    
    print(f"\n策略结果:")
    print(f"  初始资金: {init_cash:,}")
    print(f"  最终资金: {final_cash:,.2f}")
    print(f"  总收益: {total_return:,.2f}")
    print(f"  收益率: {return_rate:.2f}%")
    
    # 9. 交易统计
    trades = my_tm.get_trade_list()
    print(f"  交易次数: {len(trades)}")
    
    return sys, my_tm

def lesson4_different_strategies():
    """第4课：不同策略比较"""
    print("\n" + "=" * 60)
    print("  🔄 第4课：不同策略比较")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 150
    
    strategies = []
    
    # 策略1: 双均线(5,20)
    tm1 = crtTM(init_cash=init_cash)
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg1 = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    mm1 = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg1, mm=mm1)
    sys1.run(stock, Query(-days))
    
    return1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    strategies.append(("双均线(5,20)", return1, len(tm1.get_trade_list())))
    
    # 策略2: 双均线(10,30)
    tm2 = crtTM(init_cash=init_cash)
    sg2 = SG_Cross(MA(close_data, 10), MA(close_data, 30))
    mm2 = MM_FixedCount(1000)
    sys2 = SYS_Simple(tm=tm2, sg=sg2, mm=mm2)
    sys2.run(stock, Query(-days))
    
    return2 = ((tm2.current_cash - init_cash) / init_cash) * 100
    strategies.append(("双均线(10,30)", return2, len(tm2.get_trade_list())))
    
    # 策略3: RSI策略
    tm3 = crtTM(init_cash=init_cash)
    rsi = RSI(close_data, 14)
    sg3 = SG_Band(rsi, 30, 70)  # RSI<30买入，RSI>70卖出
    mm3 = MM_FixedCount(1000)
    sys3 = SYS_Simple(tm=tm3, sg=sg3, mm=mm3)
    sys3.run(stock, Query(-days))
    
    return3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    strategies.append(("RSI策略", return3, len(tm3.get_trade_list())))
    
    # 显示结果
    print(f"策略比较结果:")
    for name, ret, trades in strategies:
        print(f"  {name:12}: 收益率 {ret:6.2f}%, 交易次数 {trades:2d}")
    
    # 找出最佳策略
    best = max(strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳策略: {best[0]} (收益率: {best[1]:.2f}%)")
    
    return strategies

def lesson5_money_management():
    """第5课：资金管理策略"""
    print("\n" + "=" * 60)
    print("  💰 第5课：资金管理策略")
    print("=" * 60)
    
    stock = sm['sz000001']
    init_cash = 100000
    days = 100
    
    # 获取基础数据
    kdata = stock.get_kdata(Query(-days))
    close_data = CLOSE(kdata)
    sg = SG_Cross(MA(close_data, 5), MA(close_data, 20))
    
    mm_strategies = []
    
    # 1. 固定股数
    tm1 = crtTM(init_cash=init_cash)
    mm1 = MM_FixedCount(1000)
    sys1 = SYS_Simple(tm=tm1, sg=sg, mm=mm1)
    sys1.run(stock, Query(-days))
    ret1 = ((tm1.current_cash - init_cash) / init_cash) * 100
    mm_strategies.append(("固定股数(1000)", ret1))
    
    # 2. 固定股数(更多)
    tm2 = crtTM(init_cash=init_cash)
    mm2 = MM_FixedCount(2000)  # 每次买入2000股
    sys2 = SYS_Simple(tm=tm2, sg=sg, mm=mm2)
    sys2.run(stock, Query(-days))
    ret2 = ((tm2.current_cash - init_cash) / init_cash) * 100
    mm_strategies.append(("固定股数(2000)", ret2))
    
    # 3. 固定股数(更少)
    tm3 = crtTM(init_cash=init_cash)
    mm3 = MM_FixedCount(500)  # 每次买入500股
    sys3 = SYS_Simple(tm=tm3, sg=sg, mm=mm3)
    sys3.run(stock, Query(-days))
    ret3 = ((tm3.current_cash - init_cash) / init_cash) * 100
    mm_strategies.append(("固定股数(500)", ret3))
    
    print(f"资金管理策略比较:")
    for name, ret in mm_strategies:
        print(f"  {name:15}: 收益率 {ret:6.2f}%")
    
    best_mm = max(mm_strategies, key=lambda x: x[1])
    print(f"\n🏆 最佳资金管理: {best_mm[0]} (收益率: {best_mm[1]:.2f}%)")
    
    return mm_strategies

def main():
    """主学习流程"""
    print("🚀 Hikyuu官方学习教程")
    print("=" * 60)
    
    # 第1课：基础数据
    stock, kdata = lesson1_basic_data()
    
    # 第2课：技术指标
    ma5, ma10, ma20, rsi = lesson2_technical_indicators()
    
    # 第3课：简单策略
    sys, tm = lesson3_simple_strategy()
    
    # 第4课：策略比较
    strategies = lesson4_different_strategies()
    
    # 第5课：资金管理
    mm_strategies = lesson5_money_management()
    
    # 学习总结
    print("\n" + "=" * 60)
    print("  🎓 学习总结")
    print("=" * 60)
    print("✅ 已完成的学习内容:")
    print("  1. 基础数据操作 - 获取股票和K线数据")
    print("  2. 技术指标计算 - MA、RSI、MACD、BOLL")
    print("  3. 简单策略构建 - 双均线交叉策略")
    print("  4. 不同策略比较 - 多种策略效果对比")
    print("  5. 资金管理策略 - 不同资金管理方法")
    
    print("\n📚 核心概念理解:")
    print("  • TM (TradeManager): 交易账户管理")
    print("  • SG (Signal): 信号指示器")
    print("  • MM (MoneyManager): 资金管理")
    print("  • SYS (System): 交易系统")
    
    print("\n🎯 下一步学习建议:")
    print("  1. 学习止损止盈策略 (ST/TP)")
    print("  2. 学习市场环境判断 (EV)")
    print("  3. 学习多因子选股 (MF)")
    print("  4. 学习投资组合管理 (PF)")
    print("  5. 尝试实盘模拟交易")
    
    print("\n💡 实践建议:")
    print("  • 修改参数观察结果变化")
    print("  • 测试不同股票的策略效果")
    print("  • 组合多个技术指标")
    print("  • 添加风险控制措施")
    
    print("=" * 60)
    print("🎉 恭喜完成Hikyuu基础学习！")

if __name__ == "__main__":
    main()
