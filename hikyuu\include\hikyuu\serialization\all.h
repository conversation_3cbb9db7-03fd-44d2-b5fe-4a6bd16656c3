/*
 * all.h
 *
 *  Created on: 2013-5-1
 *      Author: fasiondog
 */

#pragma once
#ifndef ALL_SERIALIZATION_H_
#define ALL_SERIALIZATION_H_

#include "PriceList_serialization.h"
#include "Datetime_serialization.h"
#include "TimeDelta_serialization.h"
#include "KData_serialization.h"
#include "KQuery_serialization.h"
#include "KRecord_serialization.h"
#include "TimeLineRecord_serialization.h"
#include "TransRecord_serialization.h"
#include "MarketInfo_serialization.h"
#include "Stock_serialization.h"
#include "Block_serialization.h"
#include "StockTypeInfo_serialization.h"
#include "StockWeight_serialization.h"

#endif /* ALL_SERIALIZATION_H_ */
