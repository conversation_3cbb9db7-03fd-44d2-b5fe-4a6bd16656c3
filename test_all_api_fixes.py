#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有API修复
验证GUI中的所有API调用都正确
"""

import sys
from pathlib import Path

# 添加hikyuu路径
hikyuu_path = Path(__file__).parent / "hikyuu"
if hikyuu_path.exists():
    sys.path.insert(0, str(hikyuu_path))

try:
    from hikyuu.interactive import *
    print("✅ Hikyuu 导入成功")
except ImportError as e:
    print(f"❌ Hikyuu 导入失败: {e}")
    sys.exit(1)

def test_double_ma_strategy_complete():
    """完整测试双均线策略（模拟GUI中的所有操作）"""
    print("\n📈 完整测试双均线策略...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        print(f"✅ 股票获取: {stock.name} ({stock.market_code}{stock.code})")
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建双均线策略
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        ma5 = MA(close_data, n=5)   # 5日均线
        ma20 = MA(close_data, n=20) # 20日均线
        
        # 创建信号指示器
        my_sg = SG_Cross(ma5, ma20)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        print("✅ 回测运行成功")
        
        # 测试所有TradeManager属性（模拟GUI中的使用）
        print("\n🔧 测试TradeManager属性...")
        
        # 测试资金属性
        try:
            init_cash = my_tm.init_cash
            current_cash = my_tm.current_cash
            profit = current_cash - init_cash
            profit_rate = (profit / init_cash * 100)
            print(f"✅ 资金属性: 初始{init_cash}, 当前{current_cash}, 收益{profit:.2f}, 收益率{profit_rate:.2f}%")
        except Exception as e:
            print(f"❌ 资金属性失败: {e}")
        
        # 测试交易列表
        try:
            trade_list = my_tm.get_trade_list()
            print(f"✅ 交易列表: {len(trade_list)} 笔交易")
            
            if len(trade_list) > 0:
                # 测试交易记录属性
                trade = trade_list[0]
                datetime_attr = trade.datetime
                real_price_attr = trade.real_price
                number_attr = trade.number
                business_attr = trade.business
                print(f"✅ 交易记录属性: datetime={datetime_attr}, real_price={real_price_attr}, number={number_attr}, business={business_attr}")
                
                # 测试盈亏统计
                profit_trades = len([t for t in trade_list if t.real_price * t.number > 0])
                loss_trades = len([t for t in trade_list if t.real_price * t.number < 0])
                print(f"✅ 盈亏统计: 盈利{profit_trades}笔, 亏损{loss_trades}笔")
                
        except Exception as e:
            print(f"❌ 交易列表失败: {e}")
        
        # 测试持仓列表
        try:
            position_list = my_tm.get_position_list()
            print(f"✅ 持仓列表: {len(position_list)} 个持仓")
        except Exception as e:
            print(f"❌ 持仓列表失败: {e}")
        
        # 测试资金曲线
        try:
            if len(trade_list) > 0:
                # 创建日期列表
                dates = DatetimeList()
                for trade in trade_list:
                    dates.append(trade.datetime)
                
                # 获取资金曲线
                funds_curve = my_tm.get_funds_curve(dates)
                print(f"✅ 资金曲线: {len(funds_curve)} 个数据点")
            else:
                print("⚠️ 无交易记录，跳过资金曲线测试")
        except Exception as e:
            print(f"❌ 资金曲线失败: {e}")
        
        print(f"\n📊 最终结果:")
        print(f"   初始资金: {init_cash}")
        print(f"   最终资金: {current_cash}")
        print(f"   收益: {profit:.2f}")
        print(f"   收益率: {profit_rate:.2f}%")
        print(f"   交易次数: {len(trade_list)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 双均线策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rsi_strategy_complete():
    """完整测试RSI策略"""
    print("\n📉 完整测试RSI策略...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建RSI指标
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        rsi = RSI(close_data, n=14)
        
        # 创建信号指示器
        my_sg = SG_Band(rsi, 30, 70)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        
        # 测试所有API
        trade_list = my_tm.get_trade_list()
        position_list = my_tm.get_position_list()
        init_cash = my_tm.init_cash
        current_cash = my_tm.current_cash
        
        print(f"✅ RSI策略测试成功")
        print(f"   交易次数: {len(trade_list)}")
        print(f"   持仓数: {len(position_list)}")
        print(f"   收益: {current_cash - init_cash:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ RSI策略测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("  🧪 完整API修复验证测试")
    print("=" * 60)
    
    tests = [
        ("双均线策略完整测试", test_double_ma_strategy_complete),
        ("RSI策略完整测试", test_rsi_strategy_complete),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API修复验证通过！GUI可以完美运行！")
        print("✅ 用户现在可以正常使用所有策略模板")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
