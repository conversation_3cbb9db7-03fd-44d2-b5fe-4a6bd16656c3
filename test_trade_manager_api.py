#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TradeManager API修复
"""

import sys
from pathlib import Path

# 添加hikyuu路径
hikyuu_path = Path(__file__).parent / "hikyuu"
if hikyuu_path.exists():
    sys.path.insert(0, str(hikyuu_path))

try:
    from hikyuu.interactive import *
    print("✅ Hikyuu 导入成功")
except ImportError as e:
    print(f"❌ Hikyuu 导入失败: {e}")
    sys.exit(1)

def test_trade_manager_api():
    """测试TradeManager API"""
    print("\n🔍 测试TradeManager API...")
    
    try:
        # 获取股票和数据
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        
        # 获取K线数据并创建RSI指标
        kdata = stock.get_kdata(Query(-250))
        close_data = CLOSE(kdata)
        rsi = RSI(close_data, n=14)
        
        # 创建信号指示器
        my_sg = SG_Band(rsi, 30, 70)
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        
        # 运行回测
        sys.run(stock, Query(-250))
        
        print("✅ 回测运行成功")
        
        # 测试新的API方法
        print("\n🔧 测试TradeManager API方法...")
        
        # 测试交易列表
        try:
            trade_list = my_tm.get_trade_list()
            print(f"✅ get_trade_list(): {len(trade_list)} 笔交易")
        except Exception as e:
            print(f"❌ get_trade_list() 失败: {e}")
        
        # 测试资金曲线
        try:
            funds_curve = my_tm.get_funds_curve()
            print(f"✅ get_funds_curve(): {len(funds_curve)} 个数据点")
        except Exception as e:
            print(f"❌ get_funds_curve() 失败: {e}")
        
        # 测试持仓列表
        try:
            position_list = my_tm.get_position_list()
            print(f"✅ get_position_list(): {len(position_list)} 个持仓")
        except Exception as e:
            print(f"❌ get_position_list() 失败: {e}")
        
        # 测试资金信息
        try:
            funds = my_tm.get_funds()
            print(f"✅ get_funds(): 当前资金 {funds.total_assets}")
        except Exception as e:
            print(f"❌ get_funds() 失败: {e}")
        
        # 获取结果
        initial_cash = my_tm.init_cash
        final_cash = my_tm.current_cash
        profit = final_cash - initial_cash
        
        print(f"\n📊 回测结果:")
        print(f"   初始资金: {initial_cash}")
        print(f"   最终资金: {final_cash}")
        print(f"   收益: {profit:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ TradeManager API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("  🧪 TradeManager API 修复测试")
    print("=" * 60)
    
    if test_trade_manager_api():
        print("\n🎉 TradeManager API测试通过！")
        print("✅ 所有API方法都已正确修复")
    else:
        print("\n❌ TradeManager API测试失败")
        print("⚠️  需要进一步检查API问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
