// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SPOT_HIKYUU_FLAT_H_
#define FLATBUFFERS_GENERATED_SPOT_HIKYUU_FLAT_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

namespace hikyuu {
namespace flat {

struct Spot;
struct SpotBuilder;

struct SpotList;
struct SpotListBuilder;

struct Spot FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SpotBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MARKET = 4,
    VT_CODE = 6,
    VT_NAME = 8,
    VT_DATETIME = 10,
    VT_YESTERDAY_CLOSE = 12,
    VT_OPEN = 14,
    VT_HIGH = 16,
    VT_LOW = 18,
    VT_CLOSE = 20,
    VT_AMOUNT = 22,
    VT_VOLUME = 24,
    VT_BID1 = 26,
    VT_BID1_AMOUNT = 28,
    VT_BID2 = 30,
    VT_BID2_AMOUNT = 32,
    VT_BID3 = 34,
    VT_BID3_AMOUNT = 36,
    VT_BID4 = 38,
    VT_BID4_AMOUNT = 40,
    VT_BID5 = 42,
    VT_BID5_AMOUNT = 44,
    VT_ASK1 = 46,
    VT_ASK1_AMOUNT = 48,
    VT_ASK2 = 50,
    VT_ASK2_AMOUNT = 52,
    VT_ASK3 = 54,
    VT_ASK3_AMOUNT = 56,
    VT_ASK4 = 58,
    VT_ASK4_AMOUNT = 60,
    VT_ASK5 = 62,
    VT_ASK5_AMOUNT = 64
  };
  const ::flatbuffers::String *market() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MARKET);
  }
  const ::flatbuffers::String *code() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CODE);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *datetime() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DATETIME);
  }
  double yesterday_close() const {
    return GetField<double>(VT_YESTERDAY_CLOSE, 0.0);
  }
  double open() const {
    return GetField<double>(VT_OPEN, 0.0);
  }
  double high() const {
    return GetField<double>(VT_HIGH, 0.0);
  }
  double low() const {
    return GetField<double>(VT_LOW, 0.0);
  }
  double close() const {
    return GetField<double>(VT_CLOSE, 0.0);
  }
  double amount() const {
    return GetField<double>(VT_AMOUNT, 0.0);
  }
  double volume() const {
    return GetField<double>(VT_VOLUME, 0.0);
  }
  double bid1() const {
    return GetField<double>(VT_BID1, 0.0);
  }
  double bid1_amount() const {
    return GetField<double>(VT_BID1_AMOUNT, 0.0);
  }
  double bid2() const {
    return GetField<double>(VT_BID2, 0.0);
  }
  double bid2_amount() const {
    return GetField<double>(VT_BID2_AMOUNT, 0.0);
  }
  double bid3() const {
    return GetField<double>(VT_BID3, 0.0);
  }
  double bid3_amount() const {
    return GetField<double>(VT_BID3_AMOUNT, 0.0);
  }
  double bid4() const {
    return GetField<double>(VT_BID4, 0.0);
  }
  double bid4_amount() const {
    return GetField<double>(VT_BID4_AMOUNT, 0.0);
  }
  double bid5() const {
    return GetField<double>(VT_BID5, 0.0);
  }
  double bid5_amount() const {
    return GetField<double>(VT_BID5_AMOUNT, 0.0);
  }
  double ask1() const {
    return GetField<double>(VT_ASK1, 0.0);
  }
  double ask1_amount() const {
    return GetField<double>(VT_ASK1_AMOUNT, 0.0);
  }
  double ask2() const {
    return GetField<double>(VT_ASK2, 0.0);
  }
  double ask2_amount() const {
    return GetField<double>(VT_ASK2_AMOUNT, 0.0);
  }
  double ask3() const {
    return GetField<double>(VT_ASK3, 0.0);
  }
  double ask3_amount() const {
    return GetField<double>(VT_ASK3_AMOUNT, 0.0);
  }
  double ask4() const {
    return GetField<double>(VT_ASK4, 0.0);
  }
  double ask4_amount() const {
    return GetField<double>(VT_ASK4_AMOUNT, 0.0);
  }
  double ask5() const {
    return GetField<double>(VT_ASK5, 0.0);
  }
  double ask5_amount() const {
    return GetField<double>(VT_ASK5_AMOUNT, 0.0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MARKET) &&
           verifier.VerifyString(market()) &&
           VerifyOffset(verifier, VT_CODE) &&
           verifier.VerifyString(code()) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATETIME) &&
           verifier.VerifyString(datetime()) &&
           VerifyField<double>(verifier, VT_YESTERDAY_CLOSE, 8) &&
           VerifyField<double>(verifier, VT_OPEN, 8) &&
           VerifyField<double>(verifier, VT_HIGH, 8) &&
           VerifyField<double>(verifier, VT_LOW, 8) &&
           VerifyField<double>(verifier, VT_CLOSE, 8) &&
           VerifyField<double>(verifier, VT_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_VOLUME, 8) &&
           VerifyField<double>(verifier, VT_BID1, 8) &&
           VerifyField<double>(verifier, VT_BID1_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_BID2, 8) &&
           VerifyField<double>(verifier, VT_BID2_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_BID3, 8) &&
           VerifyField<double>(verifier, VT_BID3_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_BID4, 8) &&
           VerifyField<double>(verifier, VT_BID4_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_BID5, 8) &&
           VerifyField<double>(verifier, VT_BID5_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_ASK1, 8) &&
           VerifyField<double>(verifier, VT_ASK1_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_ASK2, 8) &&
           VerifyField<double>(verifier, VT_ASK2_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_ASK3, 8) &&
           VerifyField<double>(verifier, VT_ASK3_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_ASK4, 8) &&
           VerifyField<double>(verifier, VT_ASK4_AMOUNT, 8) &&
           VerifyField<double>(verifier, VT_ASK5, 8) &&
           VerifyField<double>(verifier, VT_ASK5_AMOUNT, 8) &&
           verifier.EndTable();
  }
};

struct SpotBuilder {
  typedef Spot Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_market(::flatbuffers::Offset<::flatbuffers::String> market) {
    fbb_.AddOffset(Spot::VT_MARKET, market);
  }
  void add_code(::flatbuffers::Offset<::flatbuffers::String> code) {
    fbb_.AddOffset(Spot::VT_CODE, code);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Spot::VT_NAME, name);
  }
  void add_datetime(::flatbuffers::Offset<::flatbuffers::String> datetime) {
    fbb_.AddOffset(Spot::VT_DATETIME, datetime);
  }
  void add_yesterday_close(double yesterday_close) {
    fbb_.AddElement<double>(Spot::VT_YESTERDAY_CLOSE, yesterday_close, 0.0);
  }
  void add_open(double open) {
    fbb_.AddElement<double>(Spot::VT_OPEN, open, 0.0);
  }
  void add_high(double high) {
    fbb_.AddElement<double>(Spot::VT_HIGH, high, 0.0);
  }
  void add_low(double low) {
    fbb_.AddElement<double>(Spot::VT_LOW, low, 0.0);
  }
  void add_close(double close) {
    fbb_.AddElement<double>(Spot::VT_CLOSE, close, 0.0);
  }
  void add_amount(double amount) {
    fbb_.AddElement<double>(Spot::VT_AMOUNT, amount, 0.0);
  }
  void add_volume(double volume) {
    fbb_.AddElement<double>(Spot::VT_VOLUME, volume, 0.0);
  }
  void add_bid1(double bid1) {
    fbb_.AddElement<double>(Spot::VT_BID1, bid1, 0.0);
  }
  void add_bid1_amount(double bid1_amount) {
    fbb_.AddElement<double>(Spot::VT_BID1_AMOUNT, bid1_amount, 0.0);
  }
  void add_bid2(double bid2) {
    fbb_.AddElement<double>(Spot::VT_BID2, bid2, 0.0);
  }
  void add_bid2_amount(double bid2_amount) {
    fbb_.AddElement<double>(Spot::VT_BID2_AMOUNT, bid2_amount, 0.0);
  }
  void add_bid3(double bid3) {
    fbb_.AddElement<double>(Spot::VT_BID3, bid3, 0.0);
  }
  void add_bid3_amount(double bid3_amount) {
    fbb_.AddElement<double>(Spot::VT_BID3_AMOUNT, bid3_amount, 0.0);
  }
  void add_bid4(double bid4) {
    fbb_.AddElement<double>(Spot::VT_BID4, bid4, 0.0);
  }
  void add_bid4_amount(double bid4_amount) {
    fbb_.AddElement<double>(Spot::VT_BID4_AMOUNT, bid4_amount, 0.0);
  }
  void add_bid5(double bid5) {
    fbb_.AddElement<double>(Spot::VT_BID5, bid5, 0.0);
  }
  void add_bid5_amount(double bid5_amount) {
    fbb_.AddElement<double>(Spot::VT_BID5_AMOUNT, bid5_amount, 0.0);
  }
  void add_ask1(double ask1) {
    fbb_.AddElement<double>(Spot::VT_ASK1, ask1, 0.0);
  }
  void add_ask1_amount(double ask1_amount) {
    fbb_.AddElement<double>(Spot::VT_ASK1_AMOUNT, ask1_amount, 0.0);
  }
  void add_ask2(double ask2) {
    fbb_.AddElement<double>(Spot::VT_ASK2, ask2, 0.0);
  }
  void add_ask2_amount(double ask2_amount) {
    fbb_.AddElement<double>(Spot::VT_ASK2_AMOUNT, ask2_amount, 0.0);
  }
  void add_ask3(double ask3) {
    fbb_.AddElement<double>(Spot::VT_ASK3, ask3, 0.0);
  }
  void add_ask3_amount(double ask3_amount) {
    fbb_.AddElement<double>(Spot::VT_ASK3_AMOUNT, ask3_amount, 0.0);
  }
  void add_ask4(double ask4) {
    fbb_.AddElement<double>(Spot::VT_ASK4, ask4, 0.0);
  }
  void add_ask4_amount(double ask4_amount) {
    fbb_.AddElement<double>(Spot::VT_ASK4_AMOUNT, ask4_amount, 0.0);
  }
  void add_ask5(double ask5) {
    fbb_.AddElement<double>(Spot::VT_ASK5, ask5, 0.0);
  }
  void add_ask5_amount(double ask5_amount) {
    fbb_.AddElement<double>(Spot::VT_ASK5_AMOUNT, ask5_amount, 0.0);
  }
  explicit SpotBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Spot> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Spot>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Spot> CreateSpot(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> market = 0,
    ::flatbuffers::Offset<::flatbuffers::String> code = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> datetime = 0,
    double yesterday_close = 0.0,
    double open = 0.0,
    double high = 0.0,
    double low = 0.0,
    double close = 0.0,
    double amount = 0.0,
    double volume = 0.0,
    double bid1 = 0.0,
    double bid1_amount = 0.0,
    double bid2 = 0.0,
    double bid2_amount = 0.0,
    double bid3 = 0.0,
    double bid3_amount = 0.0,
    double bid4 = 0.0,
    double bid4_amount = 0.0,
    double bid5 = 0.0,
    double bid5_amount = 0.0,
    double ask1 = 0.0,
    double ask1_amount = 0.0,
    double ask2 = 0.0,
    double ask2_amount = 0.0,
    double ask3 = 0.0,
    double ask3_amount = 0.0,
    double ask4 = 0.0,
    double ask4_amount = 0.0,
    double ask5 = 0.0,
    double ask5_amount = 0.0) {
  SpotBuilder builder_(_fbb);
  builder_.add_ask5_amount(ask5_amount);
  builder_.add_ask5(ask5);
  builder_.add_ask4_amount(ask4_amount);
  builder_.add_ask4(ask4);
  builder_.add_ask3_amount(ask3_amount);
  builder_.add_ask3(ask3);
  builder_.add_ask2_amount(ask2_amount);
  builder_.add_ask2(ask2);
  builder_.add_ask1_amount(ask1_amount);
  builder_.add_ask1(ask1);
  builder_.add_bid5_amount(bid5_amount);
  builder_.add_bid5(bid5);
  builder_.add_bid4_amount(bid4_amount);
  builder_.add_bid4(bid4);
  builder_.add_bid3_amount(bid3_amount);
  builder_.add_bid3(bid3);
  builder_.add_bid2_amount(bid2_amount);
  builder_.add_bid2(bid2);
  builder_.add_bid1_amount(bid1_amount);
  builder_.add_bid1(bid1);
  builder_.add_volume(volume);
  builder_.add_amount(amount);
  builder_.add_close(close);
  builder_.add_low(low);
  builder_.add_high(high);
  builder_.add_open(open);
  builder_.add_yesterday_close(yesterday_close);
  builder_.add_datetime(datetime);
  builder_.add_name(name);
  builder_.add_code(code);
  builder_.add_market(market);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Spot> CreateSpotDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *market = nullptr,
    const char *code = nullptr,
    const char *name = nullptr,
    const char *datetime = nullptr,
    double yesterday_close = 0.0,
    double open = 0.0,
    double high = 0.0,
    double low = 0.0,
    double close = 0.0,
    double amount = 0.0,
    double volume = 0.0,
    double bid1 = 0.0,
    double bid1_amount = 0.0,
    double bid2 = 0.0,
    double bid2_amount = 0.0,
    double bid3 = 0.0,
    double bid3_amount = 0.0,
    double bid4 = 0.0,
    double bid4_amount = 0.0,
    double bid5 = 0.0,
    double bid5_amount = 0.0,
    double ask1 = 0.0,
    double ask1_amount = 0.0,
    double ask2 = 0.0,
    double ask2_amount = 0.0,
    double ask3 = 0.0,
    double ask3_amount = 0.0,
    double ask4 = 0.0,
    double ask4_amount = 0.0,
    double ask5 = 0.0,
    double ask5_amount = 0.0) {
  auto market__ = market ? _fbb.CreateString(market) : 0;
  auto code__ = code ? _fbb.CreateString(code) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto datetime__ = datetime ? _fbb.CreateString(datetime) : 0;
  return hikyuu::flat::CreateSpot(
      _fbb,
      market__,
      code__,
      name__,
      datetime__,
      yesterday_close,
      open,
      high,
      low,
      close,
      amount,
      volume,
      bid1,
      bid1_amount,
      bid2,
      bid2_amount,
      bid3,
      bid3_amount,
      bid4,
      bid4_amount,
      bid5,
      bid5_amount,
      ask1,
      ask1_amount,
      ask2,
      ask2_amount,
      ask3,
      ask3_amount,
      ask4,
      ask4_amount,
      ask5,
      ask5_amount);
}

struct SpotList FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SpotListBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SPOT = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<hikyuu::flat::Spot>> *spot() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<hikyuu::flat::Spot>> *>(VT_SPOT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SPOT) &&
           verifier.VerifyVector(spot()) &&
           verifier.VerifyVectorOfTables(spot()) &&
           verifier.EndTable();
  }
};

struct SpotListBuilder {
  typedef SpotList Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_spot(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<hikyuu::flat::Spot>>> spot) {
    fbb_.AddOffset(SpotList::VT_SPOT, spot);
  }
  explicit SpotListBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SpotList> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SpotList>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SpotList> CreateSpotList(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<hikyuu::flat::Spot>>> spot = 0) {
  SpotListBuilder builder_(_fbb);
  builder_.add_spot(spot);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<SpotList> CreateSpotListDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<hikyuu::flat::Spot>> *spot = nullptr) {
  auto spot__ = spot ? _fbb.CreateVector<::flatbuffers::Offset<hikyuu::flat::Spot>>(*spot) : 0;
  return hikyuu::flat::CreateSpotList(
      _fbb,
      spot__);
}

inline const hikyuu::flat::SpotList *GetSpotList(const void *buf) {
  return ::flatbuffers::GetRoot<hikyuu::flat::SpotList>(buf);
}

inline const hikyuu::flat::SpotList *GetSizePrefixedSpotList(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<hikyuu::flat::SpotList>(buf);
}

inline bool VerifySpotListBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<hikyuu::flat::SpotList>(nullptr);
}

inline bool VerifySizePrefixedSpotListBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<hikyuu::flat::SpotList>(nullptr);
}

inline void FinishSpotListBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<hikyuu::flat::SpotList> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedSpotListBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<hikyuu::flat::SpotList> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace flat
}  // namespace hikyuu

#endif  // FLATBUFFERS_GENERATED_SPOT_HIKYUU_FLAT_H_
