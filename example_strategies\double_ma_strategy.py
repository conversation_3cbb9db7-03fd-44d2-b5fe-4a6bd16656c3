# 双均线策略示例
# 这是一个经典的双均线交叉策略，适合趋势跟踪

from hikyuu.interactive import *

# 策略参数
INIT_CASH = 100000      # 初始资金
FAST_PERIOD = 5         # 快线周期
SLOW_PERIOD = 20        # 慢线周期
TRADE_COUNT = 1000      # 每次交易股数

# 创建交易账户
my_tm = crtTM(init_cash=INIT_CASH)

# 创建移动平均线指标
fast_ma = MA(n=FAST_PERIOD)   # 快线：5日均线
slow_ma = MA(n=SLOW_PERIOD)   # 慢线：20日均线

# 创建信号指示器
# 当快线上穿慢线时买入，下穿时卖出
my_sg = SG_Cross(fast_ma, slow_ma)

# 资金管理策略：固定股数
my_mm = MM_FixedCount(TRADE_COUNT)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测（最近250个交易日）
sys.run(stock, Query(-250))

# 打印基本统计信息
print(f"初始资金: {my_tm.initCash}")
print(f"最终资金: {my_tm.currentCash}")
print(f"总收益: {my_tm.currentCash - my_tm.initCash:.2f}")
print(f"收益率: {((my_tm.currentCash - my_tm.initCash) / my_tm.initCash * 100):.2f}%")
