#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Hikyuu API的正确用法
"""

from hikyuu.interactive import *

def test_basic_api():
    """测试基本API"""
    print("🔍 测试基本API...")
    
    # 获取股票
    stock = sm['sz000001']
    print(f"✅ 股票: {stock.name} ({stock.market_code})")
    
    # 获取K线数据
    query = Query(-10)  # 最近10个交易日
    kdata = stock.get_kdata(query)
    print(f"✅ K线数据: {len(kdata)} 条记录")
    
    if len(kdata) > 0:
        k = kdata[0]
        print(f"   第一条: {k.datetime} 开:{k.open} 高:{k.high} 低:{k.low} 收:{k.close}")
    
    return True

def test_strategy():
    """测试策略"""
    print("\n🔍 测试策略...")
    
    try:
        # 获取股票
        stock = sm['sz000001']
        
        # 创建交易账户
        my_tm = crtTM(init_cash=100000)
        print("✅ 交易账户创建成功")
        
        # 创建信号指示器
        my_sg = SG_Cross(MA(n=5), MA(n=20))
        print("✅ 信号指示器创建成功")
        
        # 资金管理
        my_mm = MM_FixedCount(1000)
        print("✅ 资金管理创建成功")
        
        # 创建交易系统
        sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
        print("✅ 交易系统创建成功")
        
        # 运行回测
        sys.run(stock, Query(-50))
        print("✅ 回测运行成功")
        
        # 显示结果
        print(f"   初始资金: {my_tm.init_cash}")
        print(f"   最终资金: {my_tm.current_cash}")
        print(f"   收益: {my_tm.current_cash - my_tm.init_cash:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("  Hikyuu API 测试")
    print("=" * 50)
    
    # 测试基本API
    api_ok = test_basic_api()
    
    # 测试策略
    strategy_ok = test_strategy()
    
    print("\n" + "=" * 50)
    if api_ok and strategy_ok:
        print("🎉 所有API测试通过！")
    else:
        print("❌ 部分测试失败")
    print("=" * 50)

if __name__ == "__main__":
    main()
