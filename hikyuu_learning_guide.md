# 🚀 Hikyuu量化交易策略学习助手

欢迎来到Hikyuu量化交易框架的学习之旅！这是一个循序渐进的学习计划，帮助你从零开始掌握Hikyuu进行量化交易策略开发。

## 📚 关于Hikyuu

Hikyuu是一个基于C++/Python的高性能开源量化交易研究框架，专门用于策略分析及回测（目前主要用于国内A股市场）。

### 🎯 核心特点
- **高性能**: 百万级别K线回测，2-3秒完成计算
- **模块化设计**: 全链条交易组件化架构
- **多范式支持**: 面向对象和命令行编程
- **数据存储灵活**: 支持HDF5、MySQL、SQLite等多种存储方式
- **云平台友好**: 可结合Jupyter搭建云量化平台
- **版本活跃**: 当前最新版本2.6.3，更新频繁

### 🏗️ 全链条交易组件架构 (2.0+版本)
从投资组合到具体交易的完整链条：
1. **投资组合** (PF - Portfolio)
2. **多因子评分** (MF - MultiFactor)
3. **选股算法** (SE - Selector)
4. **交易系统** (SYS - System)
5. **信号指示器** (SG - Signal)
6. **资金管理** (MM - MoneyManager)
7. **止损策略** (ST - Stoploss) / **止盈策略** (TP - TakeProfit)
8. **盈利目标** (PG - ProfitGoal)

### 🧩 传统七大核心组件
1. **市场环境判断策略** (Environment)
2. **系统有效条件** (Condition)
3. **信号指示器** (Signal)
4. **止损/止盈策略** (Stoploss/Takeprofit)
5. **资金管理策略** (MoneyManager)
6. **盈利目标策略** (ProfitGoal)
7. **移滑价差算法** (Slippage)

## 🗺️ 学习路径

### 第一阶段：环境准备与基础概念 (1-2天)
**目标**: 搭建开发环境，理解框架基本概念

#### 🔧 环境要求：
- **操作系统**: Windows 7+, Ubuntu, macOS (64位)
- **Python版本**: >= 3.9 (macOS >= 3.10)，**推荐 3.11 或 3.12**
- **推荐发行版**: [Anaconda](https://www.anaconda.com/) (包含常用数据科学包)

#### 学习内容：
1. **安装配置**
   ```bash
   # 安装Hikyuu
   python -m pip install hikyuu

   # 升级到最新版本
   python -m pip install hikyuu -U
   ```

2. **数据下载配置**
   ```bash
   # 启动数据下载工具（GUI界面）
   hikyuutdx

   # 或使用命令行工具
   importdata
   ```

   **注意**: 如果命令无法执行，请检查Python Scripts目录是否在PATH中

3. **基础概念理解**
   - 量化交易基本概念
   - Hikyuu 2.0+ 新架构：PF→MF→SE→SYS→SG→MM→ST/TP→PG
   - 系统化交易方法论
   - 组件化设计思想

4. **第一个示例**
   ```python
   # 导入交互式工具
   from hikyuu.interactive import *

   # 创建模拟交易账户
   my_tm = crtTM(init_cash=300000)

   # 创建信号指示器（5日EMA与其10日EMA交叉）
   my_sg = SG_Flex(EMA(CLOSE, n=5), slow_n=10)

   # 固定每次买入1000股
   my_mm = MM_FixedCount(1000)

   # 创建交易系统并运行
   sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
   sys.run(sm['sz000001'], Query(-150))
   ```

#### 🚨 常见问题解决：
1. **安装失败**: 尝试更换pip源
   ```bash
   pip install hikyuu -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **hikyuutdx命令无法执行**:
   - 检查Python Scripts目录是否在PATH中
   - 或直接运行: `python -m hikyuu.gui.HikyuuTdx`

3. **中文字体乱码**: 参考官方文档配置matplotlib中文字体

4. **PyCharm IDE无法提示**:
   ```bash
   pip install pybind11-stubgen
   pybind11-stubgen hikyuu
   ```

#### 实践任务：
- [ ] 成功安装Hikyuu最新版本(2.6.3+)
- [ ] 完成数据下载配置
- [ ] 运行第一个示例并理解输出结果
- [ ] 搭建Jupyter开发环境
- [ ] 理解新架构的组件关系

---

### 第二阶段：数据获取与基础操作 (2-3天)
**目标**: 掌握股票数据获取、K线数据处理和基本可视化

#### 学习内容：
1. **股票对象管理**
   - 股票代码规范
   - 获取股票对象: `sm['sz000001']`
   - 股票基本信息查询
   - 市场数据管理

2. **K线数据操作**
   - K线数据获取: `stock.getKData(Query(-100))`
   - 不同周期数据: 日线、周线、月线、分钟线
   - 数据查询条件设置
   - 数据格式转换 (与pandas互转)

3. **数据可视化**
   - K线图绘制
   - 成交量图表
   - 多图组合显示
   - 交互式图表

#### 实践任务：
- [ ] 获取并显示任意股票的K线数据
- [ ] 绘制包含成交量的K线图
- [ ] 实现数据的pandas转换和基本分析

---

### 第三阶段：技术指标学习 (3-4天)
**目标**: 掌握各种技术指标的计算、应用和自定义

#### 学习内容：
1. **内置指标使用**
   - 趋势指标: MA、EMA、MACD
   - 震荡指标: RSI、KDJ、CCI
   - 成交量指标: VOL、OBV
   - 其他指标: BOLL、ATR等

2. **指标计算与绘制**
   ```python
   # 计算指标
   ma5 = hku.MA(hku.CLOSE, 5)
   ma20 = hku.MA(hku.CLOSE, 20)
   
   # 绘制指标
   hku.ax1 = hku.gca()  # 获取当前坐标轴
   ma5.plot(axes=hku.ax1, color='blue', legend_on=True)
   ```

3. **自定义指标开发**
   - 指标公式编写
   - 参数设置和优化
   - 指标组合使用

#### 实践任务：
- [ ] 计算并绘制常用技术指标
- [ ] 开发一个自定义指标
- [ ] 分析指标的有效性和适用场景

---

### 第四阶段：交易系统组件 (4-5天)
**目标**: 深入学习全链条组件架构，构建完整交易策略

#### 学习内容：
1. **多因子组件 (MF - MultiFactor)** ⭐ 2.0+新增
   ```python
   # 创建多因子评分器
   mf = MF_MultiFactor()
   mf.add_factor('ROE', FINANCE('roe'))  # 添加ROE因子
   mf.add_factor('PE', 1/FINANCE('pe'))   # 添加PE倒数因子

   # 获取评分结果
   scores = mf.get_scores(Datetime(20240101))
   ```

2. **信号指示器 (Signal)**
   - 单指标信号: `SG_Single`
   - 双指标交叉: `SG_Cross`
   - 灵活信号: `SG_Flex`
   - 布尔信号: `SG_Bool`
   - 信号运算: 支持 +、-、*、/、&、| 操作

3. **选股算法 (SE - Selector)**
   ```python
   # 多因子选股
   se = SE_MultiFactor(mf, topn=10)

   # 固定选股
   se = SE_Fixed([stock_list], sys)
   ```

4. **资金管理策略 (MM)**
   - 固定金额: `MM_FixedCash`
   - 固定股数: `MM_FixedCount`
   - 百分比资金: `MM_FixedPercent`
   - 凯利公式: `MM_Kelly`
   - 固定资本: `MM_FixedCapitalFunds` ⭐ 新增

5. **止损止盈策略**
   - 固定止损: `ST_FixedPercent`
   - 技术止损: `ST_Indicator`
   - 追踪止损: `ST_Saftyloss`

6. **投资组合 (PF - Portfolio)**
   ```python
   # 创建投资组合
   pf = PF_Simple(tm=my_tm, af=my_af, se=my_se)
   pf.run(Query(-500))
   ```

7. **其他组件**
   - 市场环境判断 (EV): 支持逻辑运算
   - 系统有效条件 (CN): 支持逻辑运算
   - 盈利目标策略 (PG)
   - 移滑价差算法 (SP)

#### 🆕 最新功能 (2.6+版本)：
- **事件驱动回测**: `backtest` 组件
- **滚动优化系统**: `WalkForwardSystem`
- **新增指标**: RANK、WITHKTYPE系列、KALMAN等

#### 实践任务：
- [ ] 构建一个多因子选股系统
- [ ] 实现完整的投资组合策略
- [ ] 测试不同组件组合的效果
- [ ] 尝试使用最新的滚动优化功能

---

### 第五阶段：策略回测与优化 (3-4天)
**目标**: 学习完整的策略开发流程，包括回测、评估和优化

#### 学习内容：
1. **交易系统构建**
   ```python
   # 完整交易系统示例
   sys = hku.SYS_Simple(
       tm=my_tm,           # 交易管理
       sg=my_sg,           # 信号指示器
       mm=my_mm,           # 资金管理
       ev=my_ev,           # 市场环境
       cn=my_cn,           # 系统条件
       sl=my_sl,           # 止损策略
       tp=my_tp,           # 止盈策略
       pg=my_pg,           # 盈利目标
       sp=my_sp            # 移滑价差
   )
   ```

2. **回测执行与分析**
   - 单股票回测
   - 多股票回测
   - 不同时间段测试
   - 绩效指标分析

3. **策略优化**
   - 参数优化方法
   - 过拟合避免
   - 稳健性测试
   - 样本外验证

#### 实践任务：
- [ ] 构建一个完整的交易策略
- [ ] 进行全面的回测分析
- [ ] 实现参数优化和策略改进

---

### 第六阶段：高级应用 (5-7天)
**目标**: 掌握最新高级功能和实盘交易技术

#### 学习内容：
1. **事件驱动回测** ⭐ VIP功能
   ```python
   # 使用事件驱动回测
   from hikyuu.backtest import backtest

   def strategy_func(context):
       # 策略逻辑
       pass

   result = backtest(strategy_func, start_date, end_date)
   ```

2. **滚动优化系统**
   ```python
   # 滚动寻优系统
   walk_sys = SYS_WalkForward(
       sys_list=[sys1, sys2, sys3],  # 候选系统
       optimizer=OptimalSelector(),   # 优化算法
       train_len=252,                # 训练长度
       test_len=63                   # 测试长度
   )
   ```

3. **高级多因子模型**
   - IC/ICIR信息系数分析
   - 因子有效性检验
   - 因子组合优化
   - 风险模型构建

4. **投资组合管理**
   - 多股票组合构建
   - 动态资金分配
   - 风险控制和监控
   - 组合绩效归因

5. **实盘交易系统**
   ```python
   # 策略实盘运行
   from hikyuu.strategy import Strategy

   strategy = Strategy()
   strategy.add_task('09:30', task_func)  # 添加定时任务
   strategy.run()  # 启动实盘策略
   ```

6. **数据服务和监控**
   - 实时数据缓存服务
   - 行情数据采集
   - 策略监控和报警
   - 风险控制系统

#### 🔥 最新特性 (2.6+版本)：
- **插件系统**: 支持C++插件扩展
- **数据服务器**: 实时数据缓存服务
- **新增指标**: RANK排名、WITHKTYPE跨周期等
- **性能优化**: 内存使用优化，计算速度提升

#### 实践任务：
- [ ] 构建一个完整的多因子选股系统
- [ ] 实现滚动优化交易策略
- [ ] 搭建实盘交易监控系统
- [ ] 尝试使用最新的插件功能

## 📖 学习资源

### 官方资源
- [官方网站](https://hikyuu.org/)
- [官方文档](https://hikyuu.readthedocs.io/zh-cn/latest/)
- [GitHub仓库](https://github.com/fasiondog/hikyuu)
- [示例Notebook](https://nbviewer.org/github/fasiondog/hikyuu/blob/master/hikyuu/examples/notebook/000-Index.ipynb)

### 学习建议
1. **循序渐进**: 按阶段学习，不要跳跃
2. **动手实践**: 每个概念都要亲自编码实现
3. **记录总结**: 建立自己的策略库和笔记
4. **社区交流**: 加入官方社群讨论问题
5. **持续学习**: 关注框架更新和新功能

### 常见问题与解决方案

#### 🔧 安装相关
**Q: pip install hikyuu 失败**
```bash
# 解决方案1: 更换pip源
pip install hikyuu -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 解决方案2: 升级pip
python -m pip install --upgrade pip

# 解决方案3: 使用conda
conda install hikyuu -c conda-forge
```

**Q: hikyuutdx命令无法执行**
```bash
# 检查Python Scripts目录
where hikyuutdx  # Windows
which hikyuutdx  # Linux/Mac

# 直接运行
python -m hikyuu.gui.HikyuuTdx
```

#### 📊 数据相关
**Q: 数据下载失败或缓慢**
- 检查网络连接
- 尝试更换数据源服务器
- 使用代理或VPN

**Q: 数据配置错误**
- 确保数据目录路径正确
- 检查权限设置
- 重新运行配置工具

#### ⚡ 性能相关
**Q: 回测速度慢**
- 合理设置预加载参数
- 使用HDF5而非MySQL存储
- 启用并行计算选项

**Q: 内存占用过大**
- 调整预加载数量
- 使用save_all_factors=False参数
- 及时释放不用的对象

#### 🐛 代码相关
**Q: 中文字体乱码**
- 配置matplotlib中文字体
- 参考官方文档字体设置

**Q: PyCharm无法代码提示**
```bash
pip install pybind11-stubgen
pybind11-stubgen hikyuu
```

#### 📈 策略相关
- **过拟合问题**: 使用样本外验证，避免参数过度优化
- **未来函数**: 注意使用前复权数据的风险
- **数据泄露**: 确保策略逻辑的时间一致性

## 🎯 学习目标检查清单

### 基础掌握 ✅
- [ ] 能够独立安装和配置Hikyuu环境
- [ ] 理解量化交易和系统化交易的基本概念
- [ ] 掌握股票数据获取和基本操作
- [ ] 能够计算和绘制常用技术指标

### 进阶应用 ✅
- [ ] 能够构建完整的交易系统
- [ ] 掌握各种交易组件的使用方法
- [ ] 能够进行策略回测和性能评估
- [ ] 具备基本的策略优化能力

### 高级开发 ✅
- [ ] 能够开发自定义指标和策略组件
- [ ] 掌握投资组合管理技术
- [ ] 能够构建多策略组合系统
- [ ] 具备实盘交易的技术准备

## 📋 学习计划验证报告

### ✅ 验证结果
经过全面的资料搜集和官方文档验证，本学习计划具有以下优势：

1. **架构更新**: 已更新到Hikyuu 2.6.3最新版本架构
2. **内容全面**: 涵盖从基础安装到高级应用的完整学习路径
3. **实践导向**: 每个阶段都有具体的代码示例和实践任务
4. **问题解决**: 包含详细的常见问题解决方案

### 🔄 持续更新
- Hikyuu项目更新频繁，建议定期查看[官方文档](https://hikyuu.readthedocs.io/)
- 关注[GitHub仓库](https://github.com/fasiondog/hikyuu)获取最新功能
- 加入官方社群获取实时帮助和经验分享

### 🎯 学习建议
1. **循序渐进**: 严格按照阶段顺序学习，不要跳跃
2. **动手实践**: 每个示例都要亲自运行和修改
3. **记录总结**: 建立自己的策略库和学习笔记
4. **社区交流**: 遇到问题及时在社群中求助
5. **版本意识**: 注意API变化，使用最新版本功能

---

**祝你在Hikyuu量化交易的学习之旅中收获满满！** 🎉

> 记住：量化交易不仅是技术，更是艺术。保持学习的热情，持续改进你的策略！

---

*最后更新: 2025年7月3日 | 基于Hikyuu 2.6.3版本*
