/*
 * build_in.h
 *
 *  Created on: 2012-10-15
 *      Author: fasiondog
 */

#pragma once
#ifndef INDICATOR_BUILD_IN_H_
#define INDICATOR_BUILD_IN_H_

#include "Indicator.h"
#include "crt/KDATA.h"
#include "crt/ABS.h"
#include "crt/ACOS.h"
#include "crt/AD.h"
#include "crt/ADVANCE.h"
#include "crt/ALIGN.h"
#include "crt/AMA.h"
#include "crt/ASIN.h"
#include "crt/ATAN.h"
#include "crt/ATR.h"
#include "crt/AVEDEV.h"
#include "crt/BACKSET.h"
#include "crt/BARSCOUNT.h"
#include "crt/BARSLAST.h"
#include "crt/BARSLASTCOUNT.h"
#include "crt/BARSSINCE.h"
#include "crt/BETWEEN.h"
#include "crt/BLOCKSETNUM.h"
#include "crt/CEILING.h"
#include "crt/CYCLE.h"
#include "crt/CONTEXT.h"
#include "crt/CORR.h"
#include "crt/COS.h"
#include "crt/COST.h"
#include "crt/COUNT.h"
#include "crt/CROSS.h"
#include "crt/CVAL.h"
#include "crt/DECLINE.h"
#include "crt/DEVSQ.h"
#include "crt/DIFF.h"
#include "crt/DISCARD.h"
#include "crt/DMA.h"
#include "crt/DOWNNDAY.h"
#include "crt/DROPNA.h"
#include "crt/EMA.h"
#include "crt/EXP.h"
#include "crt/EXIST.h"
#include "crt/EVERY.h"
#include "crt/FILTER.h"
#include "crt/FINANCE.h"
#include "crt/FLOOR.h"
#include "crt/HHV.h"
#include "crt/HHVBARS.h"
#include "crt/HSL.h"
#include "crt/IC.h"
#include "crt/ICIR.h"
#include "crt/INBLOCK.h"
#include "crt/INDEX.h"
#include "crt/INSUM.h"
#include "crt/IR.h"
#include "crt/INTPART.h"
#include "crt/ISINF.h"
#include "crt/ISINFA.h"
#include "crt/ISLASTBAR.h"
#include "crt/ISNA.h"
#include "crt/JUMPUP.h"
#include "crt/JUMPDOWN.h"
#include "crt/KALMAN.h"
#include "crt/LAST.h"
#include "crt/LASTVALUE.h"
#include "crt/LIUTONGPAN.h"
#include "crt/LLV.h"
#include "crt/LLVBARS.h"
#include "crt/LN.h"
#include "crt/LOG.h"
#include "crt/LONGCROSS.h"
#include "crt/MA.h"
#include "crt/MACD.h"
#include "crt/MAX.h"
#include "crt/MDD.h"
#include "crt/MIN.h"
#include "crt/MOD.h"
#include "crt/MRR.h"
#include "crt/NDAY.h"
#include "crt/NOT.h"
#include "crt/POS.h"
#include "crt/POW.h"
#include "crt/PRICELIST.h"
#include "crt/RECOVER.h"
#include "crt/REF.h"
#include "crt/REPLACE.h"
#include "crt/RESULT.h"
#include "crt/REVERSE.h"
#include "crt/ROC.h"
#include "crt/ROCP.h"
#include "crt/ROCR.h"
#include "crt/ROCR100.h"
#include "crt/ROUND.h"
#include "crt/ROUNDDOWN.h"
#include "crt/ROUNDUP.h"
#include "crt/RSI.h"
#include "crt/SAFTYLOSS.h"
#include "crt/SIN.h"
#include "crt/SLICE.h"
#include "crt/SGN.h"
#include "crt/SLOPE.h"
#include "crt/SMA.h"
#include "crt/SPEARMAN.h"
#include "crt/SQRT.h"
#include "crt/STDEV.h"
#include "crt/STDP.h"
#include "crt/SUM.h"
#include "crt/SUMBARS.h"
#include "crt/TAN.h"
#include "crt/TIME.h"
#include "crt/TIMELINE.h"
#include "crt/TIMELINEVOL.h"
#include "crt/TR.h"
#include "crt/TURNOVER.h"
#include "crt/UPNDAY.h"
#include "crt/VAR.h"
#include "crt/VARP.h"
#include "crt/VIGOR.h"
#include "crt/WINNER.h"
#include "crt/WMA.h"
#include "crt/ZHBOND10.h"
#include "crt/ZONGGUBEN.h"
#include "crt/ZSCORE.h"

#endif /* INDICATOR_BUILD_IN_H_ */
