from __future__ import annotations
import numpy
import typing
__all__ = ['ABS', 'ACOS', 'AD', 'ADVANCE', 'AF_EqualWeight', 'AF_FixedWeight', 'AF_FixedWeightList', 'AF_MultiFactor', 'ALIGN', 'AMA', 'ASIN', 'ATAN', 'ATR', 'AVEDEV', 'AllocateFundsBase', 'BACKSET', 'BARSCOUNT', 'BARSLAST', 'BARSLASTCOUNT', 'BARSSINCE', 'BARSSINCEN', 'BETWEEN', 'BLOCKSETNUM', 'BUSINESS', 'Block', 'BlockInfoDriver', 'BorrowRecord', 'BrokerPositionRecord', 'CEILING', 'CN_Bool', 'CN_OPLine', 'CONTEXT', 'CONTEXT_K', 'CORR', 'COS', 'COST', 'COUNT', 'CROSS', 'CVAL', 'CYCLE', 'C_AMO', 'C_CLOSE', 'C_HIGH', 'C_KDA<PERSON>', 'C_LOW', 'C_OPEN', 'C_VOL', 'ConditionBase', 'Constant', 'CostRecord', 'DATE', 'DAY', 'DEBUG', 'DECLINE', 'DEVSQ', 'DIFF', 'DIRECT', 'DISCARD', 'DMA', 'DOWNNDAY', 'DROPNA', 'DataDriverFactory', 'Datetime', 'DatetimeList', 'Days', 'EMA', 'ERROR', 'EVERY', 'EV_Bool', 'EV_TwoLine', 'EXIST', 'EXP', 'EnvironmentBase', 'FATAL', 'FILTER', 'FINANCE', 'FLOOR', 'FundsRecord', 'HHV', 'HHVBARS', 'HKUException', 'HOUR', 'HSL', 'Hours', 'IC', 'ICIR', 'IF', 'INBLOCK', 'INDEXA', 'INDEXADV', 'INDEXC', 'INDEXDEC', 'INDEXH', 'INDEXL', 'INDEXO', 'INDEXV', 'INFO', 'INSUM', 'INTPART', 'IR', 'ISINF', 'ISINFA', 'ISLASTBAR', 'ISNA', 'IndParam', 'Indicator', 'IndicatorImp', 'JUMPDOWN', 'JUMPUP', 'KALMAN', 'KDATA_PART', 'KData', 'KDataDriver', 'KDataToHdf5Importer', 'KRecord', 'KRecordList', 'LAST', 'LASTVALUE', 'LIUTONGPAN', 'LLV', 'LLVBARS', 'LN', 'LOG', 'LOG_LEVEL', 'LONGCROSS', 'LoanRecord', 'MA', 'MACD', 'MAX', 'MDD', 'MF_EqualWeight', 'MF_ICIRWeight', 'MF_ICWeight', 'MF_Weight', 'MIN', 'MINUTE', 'MM_FixedCapital', 'MM_FixedCapitalFunds', 'MM_FixedCount', 'MM_FixedCountTps', 'MM_FixedPercent', 'MM_FixedRisk', 'MM_FixedUnits', 'MM_Nothing', 'MM_WilliamsFixedRisk', 'MOD', 'MONTH', 'MRR', 'MarketInfo', 'Microseconds', 'Milliseconds', 'Minutes', 'MoneyManagerBase', 'MultiFactorBase', 'NDAY', 'NOT', 'OFF', 'OrderBrokerBase', 'PF_Simple', 'PF_WithoutAF', 'PG_FixedHoldDays', 'PG_FixedPercent', 'PG_NoGoal', 'POS', 'POW', 'PRICELIST', 'Parameter', 'Performance', 'Portfolio', 'PositionRecord', 'PositionRecordList', 'ProfitGoalBase', 'Query', 'RANK', 'RECOVER_BACKWARD', 'RECOVER_EQUAL_BACKWARD', 'RECOVER_EQUAL_FORWARD', 'RECOVER_FORWARD', 'REF', 'REPLACE', 'RESULT', 'REVERSE', 'ROC', 'ROCP', 'ROCR', 'ROCR100', 'ROUND', 'ROUNDDOWN', 'ROUNDUP', 'RSI', 'SAFTYLOSS', 'SE_EvaluateOptimal', 'SE_Fixed', 'SE_MaxFundsOptimal', 'SE_MultiFactor', 'SE_PerformanceOptimal', 'SE_Signal', 'SGN', 'SG_Add', 'SG_AllwaysBuy', 'SG_And', 'SG_Band', 'SG_Bool', 'SG_Buy', 'SG_Cross', 'SG_CrossGold', 'SG_Cycle', 'SG_Div', 'SG_Flex', 'SG_Mul', 'SG_OneSide', 'SG_Or', 'SG_Sell', 'SG_Single', 'SG_Single2', 'SG_Sub', 'SIN', 'SLICE', 'SLOPE', 'SMA', 'SPEARMAN', 'SP_FixedPercent', 'SP_FixedValue', 'SQRT', 'STDEV', 'STDP', 'ST_FixedPercent', 'ST_Indicator', 'ST_Saftyloss', 'SUM', 'SUMBARS', 'SYS_Simple', 'SYS_WalkForward', 'ScoreRecord', 'ScoreRecordList', 'Seconds', 'SelectorBase', 'SignalBase', 'SlippageBase', 'SpotRecord', 'Stock', 'StockManager', 'StockTypeInfo', 'StockWeight', 'StockWeightList', 'StoplossBase', 'Strategy', 'StrategyContext', 'System', 'SystemPart', 'SystemWeight', 'SystemWeightList', 'TAN', 'TA_ACCBANDS', 'TA_ACOS', 'TA_AD', 'TA_ADD', 'TA_ADOSC', 'TA_ADX', 'TA_ADXR', 'TA_APO', 'TA_AROON', 'TA_AROONOSC', 'TA_ASIN', 'TA_ATAN', 'TA_ATR', 'TA_AVGDEV', 'TA_AVGPRICE', 'TA_BBANDS', 'TA_BETA', 'TA_BOP', 'TA_CCI', 'TA_CDL2CROWS', 'TA_CDL3BLACKCROWS', 'TA_CDL3INSIDE', 'TA_CDL3LINESTRIKE', 'TA_CDL3OUTSIDE', 'TA_CDL3STARSINSOUTH', 'TA_CDL3WHITESOLDIERS', 'TA_CDLABANDONEDBABY', 'TA_CDLADVANCEBLOCK', 'TA_CDLBELTHOLD', 'TA_CDLBREAKAWAY', 'TA_CDLCLOSINGMARUBOZU', 'TA_CDLCONCEALBABYSWALL', 'TA_CDLCOUNTERATTACK', 'TA_CDLDARKCLOUDCOVER', 'TA_CDLDOJI', 'TA_CDLDOJISTAR', 'TA_CDLDRAGONFLYDOJI', 'TA_CDLENGULFING', 'TA_CDLEVENINGDOJISTAR', 'TA_CDLEVENINGSTAR', 'TA_CDLGAPSIDESIDEWHITE', 'TA_CDLGRAVESTONEDOJI', 'TA_CDLHAMMER', 'TA_CDLHANGINGMAN', 'TA_CDLHARAMI', 'TA_CDLHARAMICROSS', 'TA_CDLHIGHWAVE', 'TA_CDLHIKKAKE', 'TA_CDLHIKKAKEMOD', 'TA_CDLHOMINGPIGEON', 'TA_CDLIDENTICAL3CROWS', 'TA_CDLINNECK', 'TA_CDLINVERTEDHAMMER', 'TA_CDLKICKING', 'TA_CDLKICKINGBYLENGTH', 'TA_CDLLADDERBOTTOM', 'TA_CDLLONGLEGGEDDOJI', 'TA_CDLLONGLINE', 'TA_CDLMARUBOZU', 'TA_CDLMATCHINGLOW', 'TA_CDLMATHOLD', 'TA_CDLMORNINGDOJISTAR', 'TA_CDLMORNINGSTAR', 'TA_CDLONNECK', 'TA_CDLPIERCING', 'TA_CDLRICKSHAWMAN', 'TA_CDLRISEFALL3METHODS', 'TA_CDLSEPARATINGLINES', 'TA_CDLSHOOTINGSTAR', 'TA_CDLSHORTLINE', 'TA_CDLSPINNINGTOP', 'TA_CDLSTALLEDPATTERN', 'TA_CDLSTICKSANDWICH', 'TA_CDLTAKURI', 'TA_CDLTASUKIGAP', 'TA_CDLTHRUSTING', 'TA_CDLTRISTAR', 'TA_CDLUNIQUE3RIVER', 'TA_CDLUPSIDEGAP2CROWS', 'TA_CDLXSIDEGAP3METHODS', 'TA_CEIL', 'TA_CMO', 'TA_CORREL', 'TA_COS', 'TA_COSH', 'TA_DEMA', 'TA_DIV', 'TA_DX', 'TA_EMA', 'TA_EXP', 'TA_FLOOR', 'TA_HT_DCPERIOD', 'TA_HT_DCPHASE', 'TA_HT_PHASOR', 'TA_HT_SINE', 'TA_HT_TRENDLINE', 'TA_HT_TRENDMODE', 'TA_IMI', 'TA_KAMA', 'TA_LINEARREG', 'TA_LINEARREG_ANGLE', 'TA_LINEARREG_INTERCEPT', 'TA_LINEARREG_SLOPE', 'TA_LN', 'TA_LOG10', 'TA_MA', 'TA_MACD', 'TA_MACDEXT', 'TA_MACDFIX', 'TA_MAMA', 'TA_MAVP', 'TA_MAX', 'TA_MAXINDEX', 'TA_MEDPRICE', 'TA_MFI', 'TA_MIDPOINT', 'TA_MIDPRICE', 'TA_MIN', 'TA_MININDEX', 'TA_MINMAX', 'TA_MINMAXINDEX', 'TA_MINUS_DI', 'TA_MINUS_DM', 'TA_MOM', 'TA_MULT', 'TA_NATR', 'TA_OBV', 'TA_PLUS_DI', 'TA_PLUS_DM', 'TA_PPO', 'TA_ROC', 'TA_ROCP', 'TA_ROCR', 'TA_ROCR100', 'TA_RSI', 'TA_SAR', 'TA_SAREXT', 'TA_SIN', 'TA_SINH', 'TA_SMA', 'TA_SQRT', 'TA_STDDEV', 'TA_STOCH', 'TA_STOCHF', 'TA_STOCHRSI', 'TA_SUB', 'TA_SUM', 'TA_T3', 'TA_TAN', 'TA_TANH', 'TA_TEMA', 'TA_TRANGE', 'TA_TRIMA', 'TA_TRIX', 'TA_TSF', 'TA_TYPPRICE', 'TA_ULTOSC', 'TA_VAR', 'TA_WCLPRICE', 'TA_WILLR', 'TA_WMA', 'TC_FixedA', 'TC_FixedA2015', 'TC_FixedA2017', 'TC_TestStub', 'TC_Zero', 'TIME', 'TIMELINE', 'TIMELINEVOL', 'TR', 'TRACE', 'TURNOVER', 'TimeDelta', 'TimeLineList', 'TimeLineRecord', 'TradeCostBase', 'TradeManager', 'TradeRecord', 'TradeRecordList', 'TradeRequest', 'TransList', 'TransRecord', 'UPNDAY', 'UTCOffset', 'VAR', 'VARP', 'VIGOR', 'WARN', 'WEAVE', 'WEEK', 'WINNER', 'WITHDAY', 'WITHHALFYEAR', 'WITHHOUR', 'WITHHOUR2', 'WITHHOUR4', 'WITHKTYPE', 'WITHMIN', 'WITHMIN15', 'WITHMIN30', 'WITHMIN5', 'WITHMIN60', 'WITHMONTH', 'WITHQUARTER', 'WITHWEEK', 'WITHYEAR', 'WMA', 'YEAR', 'ZHBOND10', 'ZONGGUBEN', 'ZSCORE', 'active_device', 'backtest', 'batch_calculate_inds', 'can_upgrade', 'close_ostream_to_python', 'close_spend_time', 'combinate_ind', 'combinate_index', 'constant', 'crtBrokerTM', 'crtSEOptimal', 'crtTM', 'crt_pf_strategy', 'crt_sys_strategy', 'fetch_trial_license', 'find_optimal_system', 'find_optimal_system_multi', 'get_block', 'get_business_name', 'get_data_from_buffer_server', 'get_date_range', 'get_kdata', 'get_last_version', 'get_log_level', 'get_stock', 'get_system_part_enum', 'get_system_part_name', 'get_version', 'get_version_git', 'get_version_with_build', 'hikyuu_init', 'inner_analysis_sys_list', 'inner_combinate_ind_analysis', 'inner_combinate_ind_analysis_with_block', 'is_valid_license', 'isinf', 'isnan', 'open_ostream_to_python', 'open_spend_time', 'remove_license', 'roundDown', 'roundEx', 'roundUp', 'run_in_strategy', 'set_log_level', 'set_python_in_interactive', 'set_python_in_jupyter', 'start_data_server', 'start_spot_agent', 'stop_data_server', 'stop_spot_agent', 'toPriceList', 'view_license']
class AllocateFundsBase:
    """
    资产分配算法基类, 子类接口：
    
    公共参数:
        
        - adjust_running_sys (bool|True): 是否调整之前已经持仓策略的持仓。不调整时，仅使用总账户当前剩余资金进行分配，否则将使用总市值进行分配
            注意: 无论是否调整已持仓策略，权重比例都是相对于总资产，不是针对剩余现金余额
                  仅针对剩余现金比例调整没有意义，即使分配由于交易成本原因可能也无法完成实际交易
            adjust_running_sys: True - 主动根据资产分配对已持仓策略进行增减仓
            adjust_running_sys: False - 不会根据当前分配权重对已持仓策略进行强制加减仓
        
        - auto_adjust_weight (bool|True): 自动调整权重，此时认为传入的权重为各证券的相互比例（详见ignore_zero_weight说明）。否则，以传入的权重为指定权重不做调整（此时传入的各个权重需要小于1）。
      
        - ignore_zero_weight (bool|False): 该参数在 auto_adjust_weight 时生效。是否过滤子类返回的比例权重列表中的 0 值（包含小于0）和 nan 值。
    
            如：子类返回权重比例列表 [6, 2, 0, 0, 0], 则
                - 过滤 0 值，则实际调整后的权重为 Xi / sum(Xi)：[6/8, 2/8]
                - 不过滤，m 设为非零元素个数，n为总元素个数，(Xi / Sum(Xi)) * (m / n)：
                     [(6/8)*(2/5), (2/8)*(2/5), 0, 0, 0]
                    即，保留分为5份后，仅在2份中保持相对比例
    
        - ignore_se_score_is_null (bool|False): 忽略选中系统列表中的系统得分为 null 的系统. 注意：某些SE（如SE_MultiFactor）本身可能也存在类似控制
        - ignore_se_score_lt_zero (bool|False): 忽略选中系统列表中的系统得分小于等于 0 的系统
        - reserve_percent (float|0.0): 保留不参与重分配的资产比例
        - trace (bool|False): 打印跟踪信息
    
    子类接口:
    
        - _allocateWeight : 【必须】子类资产分配调整实现
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: AllocateFundsBase) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _allocate_weight(self, date: Datetime, se_list: SystemWeightList) -> SystemWeightList:
        """
        _allocate_weight(self, date, se_list)
        
                【重载接口】子类分配权重接口，获取实际分配资产的系统实例及其权重
        
                :param Datetime date: 当前时间
                :param SystemList se_list: 当前选中的系统列表
                :return: 系统权重分配信息列表
                :rtype: SystemWeightList
        """
    def _reset(self) -> None:
        """
        子类复位操作实现
        """
    def clone(self) -> AllocateFundsBase:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        算法组件名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        设置或获取查询条件
        """
    @query.setter
    def query(self, arg1: Query) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        ...
class BUSINESS:
    """
    Members:
    
      INIT
    
      BUY
    
      SELL
    
      BUY_SHORT
    
      SELL_SHORT
    
      GIFT
    
      BONUS
    
      CHECKIN
    
      CHECKOUT
    
      CHECKIN_STOCK
    
      CHECKOUT_STOCK
    
      BORROW_CASH
    
      RETURN_CASH
    
      BORROW_STOCK
    
      RETURN_STOCK
    
      INVALID
    """
    BONUS: typing.ClassVar[BUSINESS]  # value = <BUSINESS.BONUS: 4>
    BORROW_CASH: typing.ClassVar[BUSINESS]  # value = <BUSINESS.BORROW_CASH: 9>
    BORROW_STOCK: typing.ClassVar[BUSINESS]  # value = <BUSINESS.BORROW_STOCK: 11>
    BUY: typing.ClassVar[BUSINESS]  # value = <BUSINESS.BUY: 1>
    BUY_SHORT: typing.ClassVar[BUSINESS]  # value = <BUSINESS.BUY_SHORT: 14>
    CHECKIN: typing.ClassVar[BUSINESS]  # value = <BUSINESS.CHECKIN: 5>
    CHECKIN_STOCK: typing.ClassVar[BUSINESS]  # value = <BUSINESS.CHECKIN_STOCK: 7>
    CHECKOUT: typing.ClassVar[BUSINESS]  # value = <BUSINESS.CHECKOUT: 6>
    CHECKOUT_STOCK: typing.ClassVar[BUSINESS]  # value = <BUSINESS.CHECKOUT_STOCK: 8>
    GIFT: typing.ClassVar[BUSINESS]  # value = <BUSINESS.GIFT: 3>
    INIT: typing.ClassVar[BUSINESS]  # value = <BUSINESS.INIT: 0>
    INVALID: typing.ClassVar[BUSINESS]  # value = <BUSINESS.INVALID: 15>
    RETURN_CASH: typing.ClassVar[BUSINESS]  # value = <BUSINESS.RETURN_CASH: 10>
    RETURN_STOCK: typing.ClassVar[BUSINESS]  # value = <BUSINESS.RETURN_STOCK: 12>
    SELL: typing.ClassVar[BUSINESS]  # value = <BUSINESS.SELL: 2>
    SELL_SHORT: typing.ClassVar[BUSINESS]  # value = <BUSINESS.SELL_SHORT: 13>
    __members__: typing.ClassVar[dict[str, BUSINESS]]  # value = {'INIT': <BUSINESS.INIT: 0>, 'BUY': <BUSINESS.BUY: 1>, 'SELL': <BUSINESS.SELL: 2>, 'BUY_SHORT': <BUSINESS.BUY_SHORT: 14>, 'SELL_SHORT': <BUSINESS.SELL_SHORT: 13>, 'GIFT': <BUSINESS.GIFT: 3>, 'BONUS': <BUSINESS.BONUS: 4>, 'CHECKIN': <BUSINESS.CHECKIN: 5>, 'CHECKOUT': <BUSINESS.CHECKOUT: 6>, 'CHECKIN_STOCK': <BUSINESS.CHECKIN_STOCK: 7>, 'CHECKOUT_STOCK': <BUSINESS.CHECKOUT_STOCK: 8>, 'BORROW_CASH': <BUSINESS.BORROW_CASH: 9>, 'RETURN_CASH': <BUSINESS.RETURN_CASH: 10>, 'BORROW_STOCK': <BUSINESS.BORROW_STOCK: 11>, 'RETURN_STOCK': <BUSINESS.RETURN_STOCK: 12>, 'INVALID': <BUSINESS.INVALID: 15>}
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class Block:
    """
    板块类，可视为证券的容器
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: Block) -> bool:
        ...
    def __getitem__(self, arg0: str) -> Stock:
        """
        __getitem__(self, market_code)
        
            :param str market_code: 证券代码
            :return: Stock 实例
        """
    def __getstate__(self) -> tuple:
        ...
    def __hash__(self) -> int:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str, arg1: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Block) -> None:
        ...
    def __iter__(self) -> typing.Iterator[Stock]:
        ...
    def __len__(self) -> int:
        """
        包含的证券数量
        """
    def __ne__(self, arg0: Block) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @typing.overload
    def add(self, arg0: Stock) -> bool:
        """
        add(self, stock)
        
            加入指定的证券
        
            :param Stock stock: 待加入的证券
            :return: 是否成功加入
            :rtype: bool
        """
    @typing.overload
    def add(self, arg0: str) -> bool:
        """
        add(self, market_code)
        
            根据"市场简称证券代码"加入指定的证券
        
            :param str market_code: 市场简称证券代码
            :return: 是否成功加入
            :rtype: bool
        """
    @typing.overload
    def add(self, arg0: typing.Sequence) -> bool:
        """
        add(self, sequence)
        
            加入定的证券列表
        
            :param sequence stks: 全部由 Stock 组成的序列或全部由字符串市场简称证券代码组成的序列
            :return: True 全部成功 | False 存在失败
        """
    def clear(self) -> None:
        """
        移除包含的所有证券
        """
    def empty(self) -> bool:
        """
        empty(self)
            
            是否为空
        """
    def get_stock_list(self, filter: typing.Any = None) -> list[Stock]:
        """
        get_stock_list(self[, filter=None])
                
            获取证券列表
        
            :param func filter: 输入参数为 stock, 返回 True | False 的过滤函数
        """
    def is_null(self) -> bool:
        """
        is_null(self)
                
            是否为null值
        """
    @typing.overload
    def remove(self, arg0: Stock) -> bool:
        """
        remove(self, stock)
        
            移除指定证券
        
            :param Stock stock: 指定的证券
            :return: 是否成功
            :rtype: bool
        """
    @typing.overload
    def remove(self, arg0: str) -> bool:
        """
        remove(market_code)
        
            移除指定证券
        
            :param str market_code: 市场简称证券代码
            :return: True 成功 | False 失败
            :rtype: bool
        """
    @property
    def category(self) -> str:
        """
        板块所属分类
        """
    @category.setter
    def category(self, arg1: str) -> None:
        ...
    @property
    def index_stock(self) -> Stock:
        """
        对应指数
        """
    @index_stock.setter
    def index_stock(self, arg1: Stock) -> None:
        ...
    @property
    def name(self) -> str:
        """
        板块名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
class BlockInfoDriver:
    """
    板块数据驱动基类
        
        子类接口：
            - _init(self) (必须)
            _ getBlock(self, category, name) （必须）
            _ _getBlockList(self, category=None) （必须）
        
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __init__(self, arg0: str) -> None:
        """
        初始化
        
            :param str name: 驱动名称
        """
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    def _init(self) -> bool:
        """
        【子类接口（必须）】驱动初始化
        """
    def getBlock(self, category: str, name: str) -> Block:
        """
        【子类接口（必须）】获取指定板块
        
            :param str category: 指定的板块分类
            :param str name: 板块名称
        """
    def get_param(self, arg0: str) -> any:
        """
        获取指定参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        指定参数是否存在
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        设置指定参数
        """
    @property
    def name(self) -> str:
        """
        驱动名称
        """
class BorrowRecord:
    """
    记录当前借入的股票信息
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock, arg1: float, arg2: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def number(self) -> float:
        """
        借入总数量
        """
    @number.setter
    def number(self, arg0: float) -> None:
        ...
    @property
    def stock(self) -> Stock:
        """
        借入的证券
        """
    @stock.setter
    def stock(self, arg0: Stock) -> None:
        ...
    @property
    def value(self) -> float:
        """
        借入总价值
        """
    @value.setter
    def value(self, arg0: float) -> None:
        ...
class BrokerPositionRecord:
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock, arg1: float, arg2: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    @property
    def money(self) -> float:
        """
        买入花费总资金
        """
    @money.setter
    def money(self, arg0: float) -> None:
        ...
    @property
    def number(self) -> float:
        """
        持仓数量
        """
    @number.setter
    def number(self, arg0: float) -> None:
        ...
    @property
    def stock(self) -> Stock:
        """
        持仓对象
        """
    @stock.setter
    def stock(self, arg0: Stock) -> None:
        ...
class ConditionBase:
    """
    系统有效条件基类自定义系统有效条件接口：
    
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def plot(cn, new = True, axes = None, kdata = None, upcolor = 'red', downcolor = 'blue', alpha = 0.2):
        """
        绘制系统有效条件
        
        :param ConditionBase cn: 系统有效条件
        :param new: 仅在未指定axes的情况下生效，当为True时，创建新的窗口对象并在其中进行绘制
        :param axes: 指定在那个轴对象中进行绘制
        :param KData kdata: 指定的KData，如该值为None，则认为该系统有效条件已经
                            指定了交易对象，否则，使用该参数作为交易对象
        :param upcolor: 有效数时的颜色
        :param downcolor: 无效时的颜色
        :param alpha: 透明度
        """
    def __add__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def __and__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def __getitem__(self, arg0: int) -> float:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: ConditionBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    def __iter__(self):
        ...
    def __len__(self) -> int:
        ...
    def __mul__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def __or__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def __sub__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def __truediv__(self, arg0: ConditionBase) -> ConditionBase:
        ...
    def _add_valid(self, datetime: Datetime, value: float = 1.0) -> None:
        """
        _add_valid(self, datetime)
        
            加入有效时间，在_calculate中调用
        
            :param Datetime datetime: 有效时间
        """
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def clone(self) -> ConditionBase:
        """
        克隆操作
        """
    def get_datetime_list(self) -> DatetimeList:
        """
        get_datetime_list(self)
            
            获取系统有效的日期。注意仅返回系统有效的日期列表，和交易对象不等长
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_values(self) -> Indicator:
        """
        get_values(self)
                   
            以指标的形式获取实际值，与交易对象等长，0表示无效，1表示系统有效
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def is_valid(self, arg0: Datetime) -> bool:
        """
        is_valid(self, datetime)
        
            指定时间系统是否有效
        
            :param Datetime datetime: 指定时间
            :return: True 有效 | False 无效
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def sg(self) -> ...:
        """
        设置或获取交易信号指示器
        """
    @sg.setter
    def sg(self, arg1: ...) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        设置或获取交易管理账户
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        设置或获取交易对象
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
class Constant:
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @property
    def STOCKTYPE_A(self) -> int:
        """
        A股
        """
    @property
    def STOCKTYPE_A_BJ(self) -> int:
        """
        A股北交所
        """
    @property
    def STOCKTYPE_B(self) -> int:
        """
        B股
        """
    @property
    def STOCKTYPE_BLOCK(self) -> int:
        """
        板块
        """
    @property
    def STOCKTYPE_BOND(self) -> int:
        """
        债券
        """
    @property
    def STOCKTYPE_CRYPTO(self) -> int:
        """
        数字币
        """
    @property
    def STOCKTYPE_ETF(self) -> int:
        """
        ETF
        """
    @property
    def STOCKTYPE_FUND(self) -> int:
        """
        基金
        """
    @property
    def STOCKTYPE_GEM(self) -> int:
        """
        创业板
        """
    @property
    def STOCKTYPE_INDEX(self) -> int:
        """
        指数
        """
    @property
    def STOCKTYPE_ND(self) -> int:
        """
        国债
        """
    @property
    def STOCKTYPE_START(self) -> int:
        """
        科创板
        """
    @property
    def STOCKTYPE_TMP(self) -> int:
        """
        临时Stock
        """
    @property
    def inf(self) -> float:
        """
        无穷大
        """
    @property
    def infa(self) -> float:
        """
        负无穷大
        """
    @property
    def max_double(self) -> float:
        """
        最大double值
        """
    @property
    def nan(self) -> float:
        """
        非数字
        """
    @property
    def null_datetime(self) -> ...:
        """
        无效Datetime
        """
    @property
    def null_double(self) -> float:
        """
        同 nan
        """
    @property
    def null_int(self) -> int:
        """
        无效int
        """
    @property
    def null_int64(self) -> int:
        """
        无效int64_t
        """
    @property
    def null_price(self) -> float:
        """
        同 nan
        """
    @property
    def null_size(self) -> int:
        """
        无效size
        """
    @property
    def pickle_support(self) -> bool:
        """
        是否支持 pickle
        """
class CostRecord:
    """
    成本记录
        
        总成本 = 佣金 + 印花税 + 过户费 + 其他费用
        
        该结构主要用于存放成本记录结果，一般当做struct直接使用，
        该类本身不对总成本进行计算，也不保证上面的公式成立
    """
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: CostRecord) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, commission: float, stamptax: float, transferfee: float, others: float, total: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def commission(self) -> float:
        """
        佣金
        """
    @commission.setter
    def commission(self, arg0: float) -> None:
        ...
    @property
    def others(self) -> float:
        """
        其他费用
        """
    @others.setter
    def others(self, arg0: float) -> None:
        ...
    @property
    def stamptax(self) -> float:
        """
        印花税
        """
    @stamptax.setter
    def stamptax(self, arg0: float) -> None:
        ...
    @property
    def total(self) -> float:
        """
        总成本(float)，= 佣金 + 印花税 + 过户费 + 其它费用
        """
    @total.setter
    def total(self, arg0: float) -> None:
        ...
    @property
    def transferfee(self) -> float:
        """
        过户费
        """
    @transferfee.setter
    def transferfee(self, arg0: float) -> None:
        ...
class DIRECT:
    """
    Members:
    
      BUY
    
      SELL
    
      AUCTION
    """
    AUCTION: typing.ClassVar[DIRECT]  # value = <DIRECT.AUCTION: 2>
    BUY: typing.ClassVar[DIRECT]  # value = <DIRECT.BUY: 0>
    SELL: typing.ClassVar[DIRECT]  # value = <DIRECT.SELL: 1>
    __members__: typing.ClassVar[dict[str, DIRECT]]  # value = {'BUY': <DIRECT.BUY: 0>, 'SELL': <DIRECT.SELL: 1>, 'AUCTION': <DIRECT.AUCTION: 2>}
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class DataDriverFactory:
    """
    数据驱动工厂类
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def getBaseInfoDriver(arg0: Parameter) -> ...:
        ...
    @staticmethod
    def getBlockDriver(arg0: Parameter) -> BlockInfoDriver:
        ...
    @staticmethod
    def getKDataDriverPool(arg0: Parameter) -> ...:
        ...
    @staticmethod
    def regBlockDriver(arg0: typing.Any) -> None:
        ...
    @staticmethod
    def removeBaseInfoDriver(arg0: str) -> None:
        ...
    @staticmethod
    def removeBlockDriver(arg0: str) -> None:
        ...
    @staticmethod
    def removeKDataDriver(arg0: str) -> None:
        ...
class Datetime:
    """
    日期时间类（精确到微秒），通过以下方式构建：
        
        - 通过字符串：Datetime("2010-1-1 10:00:00")、Datetime("2001-1-1")、
                     Datetime("20010101")、Datetime("20010101T232359)
        - 通过 Python 的date：Datetime(date(2010,1,1))
        - 通过 Python 的datetime：Datetime(datetime(2010,1,1,10)
        - 通过 YYYYMMDDHHMMss 或 YYYYMMDDHHMM 或 YYYYMMDD 形式的整数：Datetime(201001011000)、Datetime(20010101)
        - Datetime(year, month, day, hour=0, minute=0, second=0, millisecond=0, microsecond=0)
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def from_hex(arg0: int) -> Datetime:
        """
        兼容oracle用后7个字节表示的datetime
        """
    @staticmethod
    def from_timestamp(arg0: int) -> Datetime:
        """
        从时间戳(微秒)创建Datetime对象
        """
    @staticmethod
    def from_timestamp_utc(arg0: int) -> Datetime:
        """
        从时间戳（微秒）创建Datetime对象，并加上本地UTC时间偏差
        """
    @staticmethod
    def max() -> Datetime:
        """
        获取支持的最大日期, Datetime(9999, 12, 31)
        """
    @staticmethod
    def min() -> Datetime:
        """
        获取支持的最小日期, Datetime(1400, 1, 1)
        """
    @staticmethod
    def now() -> Datetime:
        """
        获取系统当前日期时间
        """
    @staticmethod
    def today() -> Datetime:
        """
        获取当前的日期
        """
    def __add__(self, td):
        """
        加上指定时长，时长对象可为 TimeDelta 或 datetime.timedelta 类型
        
        :param TimeDelta td: 时长
        :rtype: Datetime
        """
    def __eq__(self, arg0: Datetime) -> bool:
        ...
    def __ge__(self, arg0: Datetime) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __gt__(self, arg0: Datetime) -> bool:
        ...
    def __hash__(self):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: int) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime) -> None:
        ...
    @typing.overload
    def __init__(self, year: int, month: int, day: int, hour: int = 0, minute: int = 0, second: int = 0, millisecond: int = 0, microsecond: int = 0) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: typing.Any) -> None:
        ...
    def __le__(self, arg0: Datetime) -> bool:
        ...
    def __lt__(self, arg0: Datetime) -> bool:
        ...
    def __ne__(self, arg0: Datetime) -> bool:
        ...
    def __radd__(self, td):
        """
        加上指定时长，时长对象可为 TimeDelta 或 datetime.timedelta 类型
        
        :param TimeDelta td: 时长
        :rtype: Datetime
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def __sub__(self, td):
        """
        减去指定的时长, 时长对象可为 TimeDelta 或 datetime.timedelta 类型
        
        :param TimeDelta td: 指定时长
        :rtype: Datetime
        """
    def date(self):
        """
        转化生成 python 的 date
        """
    def date_of_week(self, arg0: int) -> Datetime:
        """
            返回指定的本周中第几天的日期，周日为0天，周六为第6天
        
            :param int day: 指明本周的第几天，如小于则认为为第0天，如大于6则认为为第6天
        """
    def datetime(self):
        """
        转化生成 python 的 datetime
        """
    def day_of_week(self) -> int:
        """
        返回是一周中的第几天，周日为0，周一为1
        """
    def day_of_year(self) -> int:
        """
        返回一年中的第几天，1月1日为一年中的第1天
        """
    def endOfYear(self) -> Datetime:
        """
        返回年度结束日期
        """
    def end_of_day(self) -> Datetime:
        """
        返回当日 23点59分59秒
        """
    def end_of_halfyear(self) -> Datetime:
        """
        返回半年度结束日期
        """
    def end_of_month(self) -> Datetime:
        """
        返回月末最后一天日期
        """
    def end_of_quarter(self) -> Datetime:
        """
        返回季度结束日期
        """
    def end_of_week(self) -> Datetime:
        """
        返回周结束日期（周日）
        """
    def is_null(self) -> bool:
        """
        是否是Null值，等于 Datetime() 直接创建的对象
        """
    def next_day(self) -> Datetime:
        """
        返回下一自然日
        """
    def next_halfyear(self) -> Datetime:
        """
        返回下一半年度首日日期
        """
    def next_month(self) -> Datetime:
        """
        返回下月首日日期
        """
    def next_quarter(self) -> Datetime:
        """
        返回下一季度首日日期
        """
    def next_week(self) -> Datetime:
        """
        返回下周周一日期
        """
    def next_year(self) -> Datetime:
        """
        返回下一年度首日日期
        """
    def pre_day(self) -> Datetime:
        """
        返回前一自然日日期
        """
    def pre_halfyear(self) -> Datetime:
        """
        返回上一半年度首日日期
        """
    def pre_month(self) -> Datetime:
        """
        返回上月首日日期
        """
    def pre_quarter(self) -> Datetime:
        """
        返回上一季度首日日期
        """
    def pre_week(self) -> Datetime:
        """
        返回上周周一日期
        """
    def pre_year(self) -> Datetime:
        """
        返回上一年度首日日期
        """
    def start_of_day(self) -> Datetime:
        """
        返回当天 0点0分0秒
        """
    def start_of_halfyear(self) -> Datetime:
        """
        返回半年度起始日期
        """
    def start_of_month(self) -> Datetime:
        """
        返回月度起始日期
        """
    def start_of_quarter(self) -> Datetime:
        """
        返回季度起始日期
        """
    def start_of_week(self) -> Datetime:
        """
        返回周起始日期（周一）
        """
    def start_of_year(self) -> Datetime:
        """
        返回年度起始日期
        """
    def timestamp(self) -> int:
        """
        返回时间戳(微妙级别)
        """
    def timestamp_utc(self) -> int:
        """
        返回时间戳(微妙级别), 并扣除本地 UTC 偏差时间
        """
    @property
    def day(self) -> int:
        """
        日
        """
    @property
    def hex(self) -> int:
        """
        返回用后7个字节表示世纪、世纪年、月、日、时、分、秒的64位整数
        """
    @property
    def hour(self) -> int:
        """
        时
        """
    @property
    def microsecond(self) -> int:
        """
        微秒
        """
    @property
    def millisecond(self) -> int:
        """
        毫秒
        """
    @property
    def minute(self) -> int:
        """
        分
        """
    @property
    def month(self) -> int:
        """
        月
        """
    @property
    def number(self) -> int:
        """
        返回显示如 YYYYMMDDhhmm 的数字
        """
    @property
    def second(self) -> int:
        """
        秒
        """
    @property
    def ticks(self) -> int:
        """
        返回距离最小日期过去的微秒数
        """
    @property
    def year(self) -> int:
        """
        年
        """
    @property
    def ym(self) -> int:
        """
        返回显示如 YYYYMM 的数字
        """
    @property
    def ymd(self) -> int:
        """
        返回显示如 YYYYMMDD 的数字
        """
    @property
    def ymdh(self) -> int:
        """
        返回显示如 YYYYMMDDhh 的数字
        """
    @property
    def ymdhm(self) -> int:
        """
        返回显示如 YYYYMMDDhhmm 的数字
        """
    @property
    def ymdhms(self) -> int:
        """
        返回显示如 YYYYMMDDhhmms 的数字
        """
class DatetimeList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(data):
        """
        仅在安装了pandas模块时生效，转换为pandas.DataFrame
        """
    @staticmethod
    def to_np(data):
        """
        仅在安装了numpy模块时生效，转换为numpy.array
        """
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: DatetimeList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> DatetimeList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: DatetimeList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: DatetimeList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: DatetimeList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: DatetimeList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class EnvironmentBase:
    """
    市场环境判定策略基类
    
    自定义市场环境判定策略接口：
    
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def plot(ev, ref_kdata, new = True, axes = None, upcolor = 'red', downcolor = 'blue', alpha = 0.2):
        """
        绘制市场有效判断
        
        :param EnvironmentBase cn: 系统有效条件
        :param KData ref_kdata: 用于日期参考
        :param new: 仅在未指定axes的情况下生效，当为True时，创建新的窗口对象并在其中进行绘制
        :param axes: 指定在那个轴对象中进行绘制
        :param upcolor: 有效时的颜色
        :param downcolor: 无效时的颜色
        :param alpha: 透明度
        """
    def __add__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def __and__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: EnvironmentBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    def __mul__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def __or__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def __sub__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def __truediv__(self, arg0: EnvironmentBase) -> EnvironmentBase:
        ...
    def _add_valid(self, datetime: Datetime, value: float = 1.0) -> None:
        """
        _add_valid(self, datetime)
        
            加入有效时间，在_calculate中调用
        
            :param Datetime datetime: 有效时间
            :param float value: 默认值为1.0, 大于0表示有效, 小于等于0表示无效
        """
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，用于复位内部私有变量
        """
    def clone(self) -> EnvironmentBase:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def is_valid(self, arg0: Datetime) -> bool:
        """
        is_valid(self, datetime)
        
            指定时间系统是否有效
        
            :param Datetime datetime: 指定时间
            :return: True 有效 | False 无效
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        设置或获取查询条件
        """
    @query.setter
    def query(self, arg1: Query) -> None:
        ...
class FundsRecord:
    """
    当前资产情况记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __add__(self, arg0: FundsRecord) -> FundsRecord:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __iadd__(self, arg0: FundsRecord) -> FundsRecord:
        ...
    def __init__(self) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def base_asset(self) -> float:
        """
        当前投入的资产价值（float）
        """
    @base_asset.setter
    def base_asset(self, arg0: float) -> None:
        ...
    @property
    def base_cash(self) -> float:
        """
        当前投入本金（float）
        """
    @base_cash.setter
    def base_cash(self, arg0: float) -> None:
        ...
    @property
    def borrow_asset(self) -> float:
        """
        当前借入证券资产价值（float）
        """
    @borrow_asset.setter
    def borrow_asset(self, arg0: float) -> None:
        ...
    @property
    def borrow_cash(self) -> float:
        """
        当前借入的资金（float），即负债
        """
    @borrow_cash.setter
    def borrow_cash(self, arg0: float) -> None:
        ...
    @property
    def cash(self) -> float:
        """
        当前现金（float）
        """
    @cash.setter
    def cash(self, arg0: float) -> None:
        ...
    @property
    def market_value(self) -> float:
        """
        当前多头市值（float）
        """
    @market_value.setter
    def market_value(self, arg0: float) -> None:
        ...
    @property
    def net_assets(self) -> float:
        """
        净资产
        """
    @property
    def profit(self) -> float:
        """
        当前收益
        """
    @property
    def short_market_value(self) -> float:
        """
        当前空头仓位市值（float）
        """
    @short_market_value.setter
    def short_market_value(self, arg0: float) -> None:
        ...
    @property
    def total_assets(self) -> float:
        """
        总资产
        """
    @property
    def total_base(self) -> float:
        """
        投入本值资产
        """
    @property
    def total_borrow(self) -> float:
        """
        总负债
        """
class HKUException(Exception):
    pass
class IndParam:
    """
    技术指标
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: IndicatorImp) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Indicator) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    def get(self) -> Indicator:
        ...
    def get_imp(self) -> IndicatorImp:
        ...
class Indicator:
    """
    技术指标
    """
    @staticmethod
    def __getitem__(data, i):
        """
        
        :param i: int | Datetime | slice | str 类型
        """
    @staticmethod
    def __iter__(indicator):
        ...
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def bar(indicator, new = True, axes = None, kref = None, legend_on = False, text_on = False, text_color = 'k', label = None, width = 0.4, color = 'r', edgecolor = 'r', zero_on = False, *args, **kwargs):
        """
        绘制indicator柱状图
        
        :param Indicator indicator: Indicator实例
        :param axes:       指定的坐标轴
        :param new:        是否在新窗口中显示，只在没有指定axes时生效
        :param kref:       参考的K线数据，以便绘制日期X坐标
        :param legend_on:  是否打开图例
        :param text_on:    是否在左上角显示指标名称及其参数
        :param text_color: 指标名称解释文字的颜色，默认为黑色
        :param str label:  label显示文字信息，text_on 及 legend_on 为 True 时生效
        :param zero_on:    是否需要在y=0轴上绘制一条直线
        :param width:      Bar的宽度
        :param color:      Bar的颜色
        :param edgecolor:  Bar边缘颜色
        :param args:       pylab plot参数
        :param kwargs:     pylab plot参数
        """
    @staticmethod
    def exist_nan(*args, **kwargs) -> bool:
        """
        exist_nan(self, result_index)
        
            判断是否存在NaN值
        
            :param int result_index: 指定的结果集
            :rtype: bool
        """
    @staticmethod
    def heatmap(ind, axes = None):
        """
        
        绘制指标收益年-月收益热力图
        
        指标收益率 = (当前月末值 - 上月末值) / 上月末值 * 100
        
        指标应已计算（即有值），且为时间序列
        
        :param ind: 指定指标
        :param axes: 绘制的轴对象，默认为None，表示创建新的轴对象
        :return: None
        """
    @staticmethod
    def plot(indicator, new = True, axes = None, kref = None, legend_on = False, text_on = False, text_color = 'k', zero_on = False, label = None, linestyle = '-', *args, **kwargs):
        """
        绘制indicator曲线
        
        :param Indicator indicator: indicator实例
        :param axes:            指定的坐标轴
        :param new:             是否在新窗口中显示，只在没有指定axes时生效
        :param kref:            参考的K线数据，以便绘制日期X坐标
        :param legend_on:       是否打开图例
        :param text_on:         是否在左上角显示指标名称及其参数
        :param text_color:      指标名称解释文字的颜色，默认为黑色
        :param zero_on:         是否需要在y=0轴上绘制一条直线
        :param str label:       label显示文字信息，text_on 及 legend_on 为 True 时生效
        :param args:            pylab plot参数
        :param kwargs:          pylab plot参数，如：marker（标记类型）、
                                 markerfacecolor（标记颜色）、
                                 markeredgecolor（标记的边缘颜色）
        """
    @staticmethod
    def to_df(indicator):
        """
        转化为pandas.DataFrame
        """
    @typing.overload
    def __add__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __add__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __and__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __and__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __call__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __call__(self, arg0: KData) -> Indicator:
        ...
    @typing.overload
    def __call__(self) -> Indicator:
        ...
    @typing.overload
    def __eq__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __eq__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __eq__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __ge__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __ge__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __ge__(self, arg0: float) -> Indicator:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __gt__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __gt__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __gt__(self, arg0: float) -> Indicator:
        ...
    def __hash__(self) -> int:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: ...) -> None:
        ...
    @typing.overload
    def __le__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __le__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __le__(self, arg0: float) -> Indicator:
        ...
    def __len__(self) -> int:
        ...
    @typing.overload
    def __lt__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __lt__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __lt__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __mod__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __mod__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __mul__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __mul__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __ne__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __ne__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __ne__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __or__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __or__(self, arg0: float) -> Indicator:
        ...
    def __radd__(self, arg0: float) -> Indicator:
        ...
    def __rand__(self, arg0: float) -> Indicator:
        ...
    def __repr__(self) -> str:
        ...
    def __rmod__(self, arg0: float) -> Indicator:
        ...
    def __rmul__(self, arg0: float) -> Indicator:
        ...
    def __ror__(self, arg0: float) -> Indicator:
        ...
    def __rsub__(self, arg0: float) -> Indicator:
        ...
    def __rtruediv__(self, arg0: float) -> Indicator:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @typing.overload
    def __sub__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __sub__(self, arg0: float) -> Indicator:
        ...
    @typing.overload
    def __truediv__(self, arg0: Indicator) -> Indicator:
        ...
    @typing.overload
    def __truediv__(self, arg0: float) -> Indicator:
        ...
    def clone(self) -> Indicator:
        """
        克隆操作
        """
    def contains(self, arg0: str) -> bool:
        """
        contains(self, name)
                
            获取指标公式中是否包含指定名称的指标
            
            :param str name: 指定的指标名称
            :rtype: bool
        """
    def empty(self) -> bool:
        """
        是否为空
        """
    def equal(self, arg0: Indicator) -> bool:
        ...
    def formula(self) -> str:
        """
        formula(self)
        
            打印指标公式
        
            :rtype: str
        """
    def get(self, pos: int, result_index: int = 0) -> float:
        """
        get(self, pos[, result_index=0])
        
            获取指定位置的值
        
            :param int pos: 指定的位置索引
            :param int result_index: 指定的结果集
            :rtype: float
        """
    def get_by_datetime(self, datetime: Datetime, result_index: int = 0) -> float:
        """
        get_by_datetime(self, date[, result_index=0])
        
            获取指定日期数值。如果对应日期无结果，返回 constant.null_price
        
            :param Datetime datetime: 指定日期
            :param int result_index: 指定的结果集
            :rtype: float
        """
    def get_context(self) -> KData:
        """
        get_context(self)
        
            获取上下文
        
            :rtype: KData
        """
    def get_datetime(self, arg0: int) -> Datetime:
        """
        get_datetime(self, pos)
        
            获取指定位置的日期
        
            :param int pos: 指定的位置索引
            :rtype: float
        """
    def get_datetime_list(self) -> DatetimeList:
        """
        get_datetime_list(self)
        
            返回对应的日期列表
        
            :rtype: DatetimeList
        """
    def get_imp(self) -> ...:
        ...
    def get_ind_param(self, arg0: str) -> ...:
        """
        get_ind_param(self, name)
            
            获取指定的动态指标参数
            
            :param str name: 参数名称
            :return: 动态指标参数
            :rtype: IndParam
            :raises out_of_range: 无此参数
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_pos(self, arg0: Datetime) -> typing.Any:
        """
        get_pos(self, date):
        
            获取指定日期相应的索引位置, 如果没有对应位置返回 None
        
            :param Datetime date: 指定日期
            :rtype: int
        """
    def get_result(self, arg0: int) -> Indicator:
        """
        get_result(self, result_index)
        
            获取指定结果集
        
            :param int result_index: 指定的结果集
            :rtype: Indicator
        """
    def get_result_as_price_list(self, arg0: int) -> list[float]:
        """
        get_result_as_price_list(self, result_index)
        
            获取指定结果集
        
            :param int result_index: 指定的结果集
            :rtype: PriceList
        """
    def get_result_num(self) -> int:
        """
        get_result_num(self)
        
            获取结果集数量
        
            :rtype: int
        """
    def have_ind_param(self, arg0: str) -> bool:
        """
        是否存在指定的动态指标参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def is_same(self, arg0: Indicator) -> bool:
        ...
    @typing.overload
    def set_context(self, arg0: Stock, arg1: Query) -> None:
        ...
    @typing.overload
    def set_context(self, arg0: KData) -> None:
        """
        set_context(self, kdata)
        
            设置上下文
        
            :param KData kdata: 关联的上下文K线)
              
        set_context(self, stock, query)
        
            设置上下文
        
            :param Stock stock: 指定的 Stock
            :param Query query: 指定的查询条件
        """
    def set_discard(self, arg0: int) -> None:
        """
        set_discard(self, discard)
            
            设置抛弃的个数，如果小于原有的discard则无效
            :param int discard: 需抛弃的点数，大于0
        """
    @typing.overload
    def set_ind_param(self, arg0: str, arg1: Indicator) -> None:
        ...
    @typing.overload
    def set_ind_param(self, arg0: str, arg1: ...) -> None:
        """
        set_param(self, name, ind)
        
            设置动态指标参数
        
            :param str name: 参数名称
            :param Indicator|IndParam: 参数值（可为 Indicator 或 IndParam 实例）
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :type value: int | bool | float | string | Query | KData | Stock | DatetimeList
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    def support_ind_param(self) -> bool:
        """
        是否支持动态指标参数
        """
    def to_np(self) -> numpy.ndarray[numpy.float64]:
        """
        转化为np.array，如果indicator存在多个值，只返回第一个
        """
    @property
    def discard(self) -> int:
        """
        结果中需抛弃的个数
        """
    @property
    def long_name(self) -> str:
        """
        返回形如：Name(param1_val,param2_val,...)
        """
    @property
    def name(self) -> str:
        """
        指标名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
class IndicatorImp:
    """
    指标实现类，定义新指标时，应从此类继承
        
        子类需实现以下接口：
    
            - _clone() -> IndicatorImp
            - _calculate(ind) ：指标计算
            - isNeedContext(bool) ：是否依赖上下文
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
            :param str name: 指标名称
        """
    @typing.overload
    def __init__(self, arg0: str, arg1: int) -> None:
        """
            :param str name: 指标名称
            :param int result_num: 指标结果集数量
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _calculate(self, arg0: Indicator) -> None:
        ...
    def _dyn_calculate(self, arg0: Indicator) -> None:
        ...
    def _dyn_run_one_step(self, arg0: Indicator, arg1: int, arg2: int) -> None:
        ...
    def _ready_buffer(self, arg0: int, arg1: int) -> None:
        ...
    def _set(self, val: float, pos: int, num: int = 0) -> None:
        ...
    def calculate(self) -> Indicator:
        ...
    def clone(self) -> IndicatorImp:
        ...
    def contains(self, arg0: str) -> bool:
        ...
    def get_ind_param(self, arg0: str) -> ...:
        ...
    def get_param(self, arg0: str) -> any:
        ...
    def get_parameter(self) -> Parameter:
        """
        获取内部参数类对象
        """
    def get_result_as_price_list(self, arg0: int) -> list[float]:
        ...
    def get_result_num(self) -> int:
        ...
    def have_ind_param(self, arg0: str) -> bool:
        ...
    def have_param(self, arg0: str) -> bool:
        ...
    def is_leaf(self) -> bool:
        ...
    def is_need_context(self) -> bool:
        ...
    def is_serial(self) -> bool:
        ...
    def set_discard(self, arg0: int) -> None:
        ...
    @typing.overload
    def set_ind_param(self, arg0: str, arg1: Indicator) -> None:
        ...
    @typing.overload
    def set_ind_param(self, arg0: str, arg1: ...) -> None:
        ...
    def set_param(self, arg0: str, arg1: any) -> None:
        ...
    def support_ind_param(self) -> bool:
        ...
    @property
    def discard(self) -> int:
        """
        结果中需抛弃的个数
        """
    @property
    def name(self) -> str:
        """
        指标名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
class KData:
    """
    通过 Stock.getKData 获取的K线数据，由 KRecord 组成的数组，可象 list 一样进行遍历
    """
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def __getitem__(kdata, i):
        """
        
        :param i: int | Datetime | slice | str 类型
        """
    @staticmethod
    def __iter__(kdata):
        ...
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def kplot(kdata, new = True, axes = None, colorup = 'r', colordown = 'g'):
        """
        绘制K线图
        
        :param KData kdata: K线数据
        :param bool new:    是否在新窗口中显示，只在没有指定axes时生效
        :param axes:        指定的坐标轴
        :param colorup:     the color of the rectangle where close >= open
        :param colordown:   the color of the rectangle where close < open
        """
    @staticmethod
    def mkplot(kdata, new = True, axes = None, colorup = 'r', colordown = 'g', ticksize = 3):
        """
        绘制美式K线图
        
        :param KData kdata: K线数据
        :param bool new:    是否在新窗口中显示，只在没有指定axes时生效
        :param axes:        指定的坐标轴
        :param colorup:     the color of the lines where close >= open
        :param colordown:   the color of the lines where close < open
        :param ticksize:    open/close tick marker in points
        """
    @staticmethod
    def plot(kdata, new = True, axes = None, colorup = 'r', colordown = 'g'):
        """
        绘制K线图
        
        :param KData kdata: K线数据
        :param bool new:    是否在新窗口中显示，只在没有指定axes时生效
        :param axes:        指定的坐标轴
        :param colorup:     the color of the rectangle where close >= open
        :param colordown:   the color of the rectangle where close < open
        """
    @staticmethod
    def to_df(kdata):
        """
        转化为pandas的DataFrame
        """
    @staticmethod
    def to_np(kdata):
        """
        转化为numpy结构数组
        """
    def __eq__(self, arg0: KData) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __init__(self) -> None:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: KData) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def empty(self) -> bool:
        """
        empty(self)
        
                判断是否为空
        
                :rtype: bool
        """
    def get(self, arg0: int) -> KRecord:
        """
        get(self, pos)
        
                获取指定索引位置的K线记录
        
                :param int pos: 位置索引
                :rtype: KRecord
        """
    def get_by_datetime(self, arg0: Datetime) -> KRecord:
        """
        get_by_datetime(self, datetime)
        
                获取指定时间的K线记录。
        
                :param Datetime datetime: 指定的日期
                :rtype: KRecord
        """
    def get_datetime_list(self) -> DatetimeList:
        """
        get_datetime_list(self)
        
                返回交易日期列表
        
                :rtype: DatetimeList
        """
    def get_kdata(self, arg0: Datetime, arg1: Datetime) -> KData:
        """
        get_kdata(self, start_date, end_date)
              
                通过当前 KData 获取一个保持数据类型、复权类型不变的新的 KData（注意，不是原 KData 的子集）
        
                :param Datetime start: 新的起始日期
                :param Datetime end: 新的结束日期
                :rtype: KData
        """
    def get_pos(self, arg0: Datetime) -> typing.Any:
        """
        get_pos(self, datetime)
        
                获取指定时间的K线记录的索引位置, 如果不在数据范围内，则返回 None
                
                :param Datetime datetime: 指定的日期
                :rtype: int
        """
    def get_pos_in_stock(self, arg0: Datetime) -> typing.Any:
        """
        get_pos_in_stock(self, datetime) 
                
                获取指定时间对应的原始K线中的索引位置
        
                :param Datetime datetime: 指定的时间
                :return: 对应的索引位置，如果不在数据范围内，则返回 None
        """
    def get_query(self) -> Query:
        """
        get_query(self)
        
                获取关联的查询条件
        
                :rtype: KQuery
        """
    def get_stock(self) -> ...:
        """
        get_stock(self)
        
                获取关联的Stock
        
                :rtype: Stock
        """
    def tocsv(self, arg0: str) -> None:
        """
        tocsv(self, filename)
        
                将数据保存至CSV文件
        
                :param str filename: 指定保存的文件名称
        """
    @property
    def amo(self) -> ...:
        """
        返回包含成交金额的 Indicator 实例，相当于 AMO(k)
        """
    @property
    def close(self) -> ...:
        """
        返回包含收盘价的 Indicator 实例，相当于 CLOSE(k)
        """
    @property
    def end_pos(self) -> int:
        """
        获取在原始K线记录中对应范围的下一条记录的位置，如果为空返回0,其他等于lastPos + 1
        """
    @property
    def high(self) -> ...:
        """
        返回包含最高价的 Indicator 实例，相当于 HIGH(k)
        """
    @property
    def last_pos(self) -> int:
        """
        获取在原始K线记录中对应的最后一条记录的位置，如果为空返回0,其他等于endPos - 1
        """
    @property
    def low(self) -> ...:
        """
        返回包含最低价的 Indicator 实例，相当于 LOW(k)
        """
    @property
    def open(self) -> ...:
        """
        返回包含开盘价的 Indicator 实例，相当于 OPEN(k)
        """
    @property
    def start_pos(self) -> int:
        """
        获取在原始K线记录中对应的起始位置，如果KData为空返回0
        """
    @property
    def vol(self) -> ...:
        """
        返回包含成交量的 Indicator 实例，相当于 VOL(k)
        """
class KDataDriver:
    """
    K线数据驱动基类
        
      子类接口:
        - _init(self)
        - getCount(self, market, code, ktype)
        - getKRecord(self, market, code, pos, ktype)
        - _loadKDate(self, market, code, ktype, startix, endix)
        - _getIndexRangeByDate(self, market, code, query)
        - _getTimeLineList(self, market, code, query)
        - _getTransList(self, market, code, query)
      
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    def _init(self) -> bool:
        """
        【子类接口】初始化驱动
        """
    def canParallelLoad(self) -> bool:
        ...
    def clone(self) -> KDataDriver:
        ...
    def getCount(self, market: str, code: str, ktype: str) -> int:
        """
        【子类接口】获取K线记录数量
            
            :param str markt: 市场简称
            :param str code: 证券代码
            :param Query.KType ktype: K线类型
            :rtype int
        """
    def get_param(self, arg0: str) -> any:
        """
        获取指定参数的值
        """
    def have_param(self, arg0: str) -> bool:
        """
        指定参数是否存在
        """
    def isIndexFirst(self) -> bool:
        ...
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        设置参数
        """
    @property
    def name(self) -> str:
        """
        驱动名称
        """
class KDataToHdf5Importer:
    """
    K线数据导入器
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __init__(self) -> None:
        ...
    def add_krecord_list(self, arg0: str, arg1: str, arg2: KRecordList, arg3: str) -> None:
        """
        添加K线数据
        """
    def get_last_datetime(self, arg0: str, arg1: str, arg2: str) -> Datetime:
        """
        获取指定市场指定证券最后K线时间
        """
    def set_config(self, arg0: str, arg1: list[str]) -> bool:
        """
        设置数据保存路径和数据源列表
        """
    def update_index(self, arg0: str, arg1: str, arg2: str) -> None:
        """
        更新索引
        """
class KRecord:
    """
    K线记录，组成K线数据，属性可读写
    """
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: KRecord) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime, arg1: float, arg2: float, arg3: float, arg4: float, arg5: float, arg6: float) -> None:
        ...
    def __ne__(self, arg0: KRecord) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def amount(self) -> float:
        """
        成交金额
        """
    @amount.setter
    def amount(self, arg0: float) -> None:
        ...
    @property
    def close(self) -> float:
        """
        收盘价
        """
    @close.setter
    def close(self, arg0: float) -> None:
        ...
    @property
    def datetime(self) -> Datetime:
        """
        时间
        """
    @datetime.setter
    def datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def high(self) -> float:
        """
        最高价
        """
    @high.setter
    def high(self, arg0: float) -> None:
        ...
    @property
    def low(self) -> float:
        """
        最低价
        """
    @low.setter
    def low(self, arg0: float) -> None:
        ...
    @property
    def open(self) -> float:
        """
        开盘价
        """
    @open.setter
    def open(self, arg0: float) -> None:
        ...
    @property
    def volume(self) -> float:
        """
        成交量
        """
    @volume.setter
    def volume(self, arg0: float) -> None:
        ...
class KRecordList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: KRecordList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> KRecordList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: KRecordList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: KRecordList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: KRecordList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: KRecordList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class LOG_LEVEL:
    """
    Members:
    
      DEBUG
    
      TRACE
    
      INFO
    
      WARN
    
      ERROR
    
      FATAL
    
      OFF
    """
    DEBUG: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.DEBUG: 1>
    ERROR: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.ERROR: 4>
    FATAL: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.FATAL: 5>
    INFO: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.INFO: 2>
    OFF: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.OFF: 6>
    TRACE: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.TRACE: 0>
    WARN: typing.ClassVar[LOG_LEVEL]  # value = <LOG_LEVEL.WARN: 3>
    __members__: typing.ClassVar[dict[str, LOG_LEVEL]]  # value = {'DEBUG': <LOG_LEVEL.DEBUG: 1>, 'TRACE': <LOG_LEVEL.TRACE: 0>, 'INFO': <LOG_LEVEL.INFO: 2>, 'WARN': <LOG_LEVEL.WARN: 3>, 'ERROR': <LOG_LEVEL.ERROR: 4>, 'FATAL': <LOG_LEVEL.FATAL: 5>, 'OFF': <LOG_LEVEL.OFF: 6>}
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class LoanRecord:
    """
    借款记录（融资记录）
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime, arg1: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def datetime(self) -> Datetime:
        """
        借款时间
        """
    @datetime.setter
    def datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def value(self) -> float:
        """
        借款金额
        """
    @value.setter
    def value(self, arg0: float) -> None:
        ...
class MarketInfo:
    """
    市场信息记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str, arg1: str, arg2: str, arg3: str, arg4: Datetime, arg5: TimeDelta, arg6: TimeDelta, arg7: TimeDelta, arg8: TimeDelta) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def close_time1(self) -> TimeDelta:
        """
        闭市时间1
        """
    @property
    def close_time2(self) -> TimeDelta:
        """
        闭市时间2
        """
    @property
    def code(self) -> str:
        """
        该市场对应的主要指数代码，用于获取交易日历
        """
    @property
    def description(self) -> str:
        """
        描述说明
        """
    @property
    def last_datetime(self) -> Datetime:
        """
        该市场K线数据最后交易日期
        """
    @property
    def market(self) -> str:
        """
        市场标识（如：沪市“SH”, 深市“SZ”）
        """
    @property
    def name(self) -> str:
        """
        市场全称
        """
    @property
    def open_time1(self) -> TimeDelta:
        """
        开市时间1
        """
    @property
    def open_time2(self) -> TimeDelta:
        """
        开市时间2
        """
class MoneyManagerBase:
    """
    资金管理策略基类
    
    公共参数：
    
        - auto-checkin=False (bool) : 当账户现金不足以买入资金管理策略指示的买入数量时，自动向账户中补充存入（checkin）足够的现金。
        - max-stock=20000 (int) : 最大持有的证券种类数量（即持有几只股票，而非各个股票的持仓数）
        - disable_ev_force_clean_position=False (bool) : 禁用市场环境失效时强制清仓
        - disable_cn_force_clean_position=False (bool) : 禁用系统有效条件失效时强制清仓
    
    自定义资金管理策略接口：
    
        - _buyNotify : 【可选】接收实际买入通知，预留用于多次增减仓处理
        - _sellNotify : 【可选】接收实际卖出通知，预留用于多次增减仓处理
        - _getBuyNumber : 【必须】获取指定交易对象可买入的数量
        - _getSellNumber : 【可选】获取指定交易对象可卖出的数量，如未重载，默认为卖出全部已持仓数量
        - _reset : 【可选】重置私有属性
        - _clone : 【必须】克隆接口
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: MoneyManagerBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _buy_notify(self, arg0: TradeRecord) -> None:
        """
        _buy_notify(self, trade_record)
        
            【重载接口】交易系统发生实际买入操作时，通知交易变化情况，一般存在多次增减仓的情况才需要重载
        
            :param TradeRecord trade_record: 发生实际买入时的实际买入交易记录
        """
    def _get_buy_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        """
        _get_buy_num(self, datetime, stock, price, risk, part_from)
        
            【重载接口】获取指定交易对象可买入的数量
        
            :param Datetime datetime: 交易时间
            :param Stock stock: 交易对象
            :param float price: 交易价格
            :param float risk: 交易承担的风险，如果为0，表示全部损失，即市值跌至0元
            :param System.Part part_from: 来源系统组件
            :return: 可买入数量
            :rtype: float
        """
    def _get_buy_short_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        ...
    def _get_sell_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        """
        _get_sell_num(self, datetime, stock, price, risk, part_from)
        
            【重载接口】获取指定交易对象可卖出的数量。如未重载，默认为卖出全部已持仓数量。
        
            :param Datetime datetime: 交易时间
            :param Stock stock: 交易对象
            :param float price: 交易价格
            :param float risk: 新的交易承担的风险，如果为0，表示全部损失，即市值跌至0元
            :param System.Part part_from: 来源系统组件
            :return: 可卖出数量
            :rtype: float
        """
    def _get_sell_short_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        ...
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def _sell_notify(self, arg0: TradeRecord) -> None:
        """
        _sell_notify(self, trade_record)
        
            【重载接口】交易系统发生实际卖出操作时，通知实际交易变化情况，一般存在多次增减仓的情况才需要重载
        
            :param TradeRecord trade_record: 发生实际卖出时的实际卖出交易记录
        """
    def clone(self) -> MoneyManagerBase:
        """
        克隆操作
        """
    def current_buy_count(self, arg0: Stock) -> int:
        """
        当前连续买入计数
        """
    def current_sell_count(self, arg0: Stock) -> int:
        """
        当前连续卖出计数
        """
    def get_buy_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        """
        get_buy_num(self, datetime, stock, price, risk, part_from)
        
            获取指定交易对象可买入的数量
        
            :param Datetime datetime: 交易时间
            :param Stock stock: 交易对象
            :param float price: 交易价格
            :param float risk: 交易承担的风险，如果为0，表示全部损失，即市值跌至0元
            :param System.Part part_from: 来源系统组件
            :return: 可买入数量
            :rtype: float
        """
    def get_buy_short_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        ...
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_sell_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        """
        get_sell_num(self, datetime, stock, price, risk, part_from)
        
            获取指定交易对象可卖出的数量
            
            :param Datetime datetime: 交易时间
            :param Stock stock: 交易对象
            :param float price: 交易价格
            :param float risk: 新的交易承担的风险，如果为0，表示全部损失，即市值跌至0元
            :param System.Part part_from: 来源系统组件
            :return: 可卖出数量
            :rtype: float
        """
    def get_sell_short_num(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: SystemPart) -> float:
        ...
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        设置或获取查询条件
        """
    @query.setter
    def query(self, arg1: Query) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        设置或获取交易管理对象
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
class MultiFactorBase:
    """
    市场环境判定策略基类
    
    自定义市场环境判定策略接口：
    
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: MultiFactorBase) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def clone(self) -> MultiFactorBase:
        """
        克隆操作
        """
    def get_all_factors(self) -> list[Indicator]:
        """
        get_all_factors(self)
        
            获取所有证券合成后的因子列表
        
            :return: [factor1, factor2, ...] 顺序与参考证券顺序相同
        """
    def get_all_scores(self) -> list[ScoreRecordList]:
        """
        get_all_scores(self)
        
            获取所有日期的所有评分，长度与参考日期相同
        
            :return: ScoreRecordList
        """
    def get_all_src_factors(self) -> list[list[Indicator]]:
        ...
    def get_datetime_list(self) -> DatetimeList:
        """
        获取参考日期列表（由参考证券通过查询条件获得）
        """
    def get_factor(self, stock: Stock) -> Indicator:
        """
        get_factor(self, stock)
        
            获取指定证券合成后的新因子
        
            :param Stock stock: 指定证券
        """
    def get_ic(self, ndays: int = 0) -> Indicator:
        """
        get_ic(self[, ndays=0])
        
            获取合成因子的IC, 长度与参考日期同
        
            ndays 对于使用 IC/ICIR 加权的新因子，最好保持好 ic_n 一致，
            但对于等权计算的新因子，不一定非要使用 ic_n 计算。
            所以，ndays 增加了一个特殊值 0, 表示直接使用 ic_n 参数计算 IC
             
            :rtype: Indicator
        """
    def get_icir(self, ir_n: int, ic_n: int = 0) -> Indicator:
        """
        get_icir(self, ir_n[, ic_n=0])
        
            获取合成因子的 ICIR
        
            :param int ir_n: 计算 IR 的 n 窗口
            :param int ic_n: 计算 IC 的 n 窗口 (同 get_ic 中的 ndays)
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_ref_indicators(self) -> list[Indicator]:
        """
        获取创建时输入的原始因子列表
        """
    def get_ref_stock(self) -> Stock:
        """
        获取参考证券
        """
    def get_scores(self, date: Datetime, start: int = 0, end: typing.Any = None, filter: typing.Any = None) -> ScoreRecordList:
        """
        get_score(self, date[, start=0, end=Null])
        
            获取指定日期截面的所有因子值，已经降序排列，相当于各证券日期截面评分。
        
            :param Datetime date: 指定日期
            :param int start: 取当日排名开始
            :param int end: 取当日排名结束(不包含本身)
            :param function func: (ScoreRecord)->bool 或 (Datetime, ScoreRecord)->bool 为原型的可调用对象
            :rtype: ScoreRecordList
        """
    def get_stock_list(self) -> list[Stock]:
        """
        获取创建时指定的证券列表
        """
    def get_stock_list_num(self) -> int:
        """
        获取创建时指定的证券列表中证券数量
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    def set_ref_indicators(self, arg0: list[Indicator]) -> None:
        """
        set_ref_indicators(self, inds)
              
            设置原始因子列表
            
            :param list inds: 新的原始因子列表
        """
    def set_ref_stock(self, arg0: Stock) -> None:
        """
        set_ref_stock(self, stk)
              
            设置参考证券
            
            :param Stock stk: 参考证券
        """
    def set_stock_list(self, arg0: list[Stock]) -> None:
        """
        set_stock_list(self, stks)
              
            设置计算范围指定的证券列表
            
            :param list stks: 新的待计算证券列表
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        查询条件
        """
    @query.setter
    def query(self, arg1: Query) -> None:
        ...
class OrderBrokerBase:
    """
    订单代理包装基类，用户可以参考自定义自己的订单代理，加入额外的处理
          
        :param bool real: 下单前是否重新实时获取实时分笔数据
        :param float slip: 如果当前的卖一价格和指示买入的价格绝对差值不超过slip则下单，否则忽略; 对卖出操作无效，立即以当前价卖出
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
            :param str name: 代理名称
        """
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    def _buy(self, arg0: Datetime, arg1: str, arg2: str, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> None:
        """
        _buy(self, datetime, market, code, price, num, stoploss, goal_price, part_from, remark)
        
            【子类接口】执行买入操作
        
            :param Datetime datetime: 策略指示时间
            :param str market: 市场标识
            :param str code: 证券代码
            :param float price: 买入价格
            :param float num: 买入数量
            :param float stoploss: 计划止损价
            :param float goal_price: 计划盈利目标价
            :param SystemPart part_from: 信号来源,
            :param str remark: 订单备注
        """
    def _get_asset_info(self) -> str:
        """
        _get_asset_info(self)
        
            【子类接口】获取当前资产信息，子类需返回符合如下规范的 json 字符串:
        
            {
                "datetime": "2001-01-01 18:00:00.12345",
                "cash": 0.0,
                "positions": [
                    {"market": "SZ", "code": "000001", "number": 100.0, "stoploss": 0.0, "goal_price": 0.0,
                     "cost_price": 0.0},
                    {"market": "SH", "code": "600001", "number": 100.0, "stoploss": 0.0, "goal_price": 0.0,
                     "cost_price": 0.0},
                 ]
            }    
        
            :return: 以字符串（json格式）方式返回当前资产信息
            :rtype: str
        """
    def _sell(self, arg0: Datetime, arg1: str, arg2: str, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> None:
        """
        _sell(self, datetime, market, code, price, num, stoploss, goal_price, part_from, remark)
        
            【子类接口】执行卖出操作
        
            :param Datetime datetime: 策略指示时间
            :param str market: 市场标识
            :param str code: 证券代码
            :param float price: 卖出价格
            :param float num: 卖出数量
            :param float stoploss: 计划止损价
            :param float goal_price: 计划盈利目标价
            :param SystemPart part_from: 信号来源
            :param str remark: 订单备注
        """
    def buy(self, arg0: Datetime, arg1: str, arg2: str, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> None:
        """
        详情见子类实现接口: _buy
        """
    def get_asset_info(self) -> str:
        """
        详情见子类实现接口: _get_asset_info
        """
    def sell(self, arg0: Datetime, arg1: str, arg2: str, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> None:
        """
        详情见子类实现接口: _sell
        """
    @property
    def name(self) -> str:
        """
        名称（可读写）
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
class Parameter:
    """
    参数类，供需要命名参数设定的类使用，类似于 dict
    """
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __contains__(self, arg0: str) -> bool:
        ...
    def __eq__(self, arg0: Parameter) -> bool:
        ...
    def __getitem__(self, arg0: str) -> any:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __init__(self) -> None:
        ...
    def __iter__(self):
        ...
    def __lt__(self, arg0: Parameter) -> bool:
        ...
    def __ne__(self, arg0: Parameter) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setitem__(self, arg0: str, arg1: any) -> None:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def get(self, arg0: str) -> any:
        ...
    def get_name_list(self) -> list[str]:
        """
        Get all the parameter names list
        """
    def get_name_value_list(self) -> str:
        """
        Return a string, like 'name1=val1,name2=val2,...'
        """
    def have(self, arg0: str) -> bool:
        """
        Return True if there is a parameter for the specified name.
        """
    def items(self):
        ...
    def keys(self):
        ...
    def set(self, arg0: str, arg1: any) -> None:
        ...
    def to_dict(self):
        """
        转化为 Python dict 对象
        """
    def type(self, arg0: str) -> str:
        """
        Get the type name of the specified parameter, return 'string' | 'int' | 'double' | 'bool' | 'Stock' | 'KQuery' | 'KData' | 'PriceList' | 'DatetimeList'
        """
class Performance:
    """
    简单绩效统计
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(per):
        """
        将 Performance 统计结果转换为 DataFrame 格式
        """
    def __getitem__(self, arg0: str) -> float:
        """
        按指标名称获取指标值，必须在运行 statistics 或 report 之后生效
                
                :param str name: 指标名称
                :rtype: float)
        """
    def __init__(self) -> None:
        ...
    def exist(self, arg0: str) -> bool:
        ...
    def names(self) -> list[str]:
        """
        names(self)
              
              获取所有统计项名称
        """
    def report(self) -> str:
        """
        report(self)
        
                简单的文本统计报告，用于直接输出打印
                只有运行 statistics 后或 Performance 本身为从 TM 获取的结果时才生效
        
                :rtype: str
        """
    def reset(self) -> None:
        """
        reset(self)
        
                复位，清除已计算的结果
        """
    def statistics(self, tm: TradeManager, datetime: Datetime = ...) -> None:
        """
        statistics(self, tm[, datetime=Datetime.now()])
        
                根据交易记录，统计截至某一时刻的系统绩效, datetime必须大于等于lastDatetime
        
                :param TradeManager tm: 指定的交易管理实例
                :param Datetime datetime: 统计截止时刻
        """
    def to_dict(self) -> dict:
        ...
    def values(self) -> list[float]:
        """
        values(self)
              
              获取所有统计项值，顺序与 names 相同
        """
class Portfolio:
    """
    实现多标的、多策略的投资组合
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def heatmap(sys, axes = None):
        """
        
        绘制系统收益年-月收益热力图
        """
    @staticmethod
    def performance(sys, ref_stk = None):
        """
        
        绘制系统绩效，即账户累积收益率曲线
        
        :param SystemBase | PortfolioBase sys: SYS或PF实例
        :param Stock ref_stk: 参考股票, 默认为沪深300: sh000300, 绘制参考标的的收益曲线
        :return: None
        """
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str, arg1: TradeManager, arg2: SelectorBase, arg3: AllocateFundsBase) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def clone(self) -> Portfolio:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def run(self, query: Query, force: bool = False) -> None:
        """
        run(self, query[, force=false])
            
            运行投资组合策略。在查询条件及各组件没有变化时，PF在第二次执行时，默认不会实际进行计算。
            但由于各个组件的参数可能改变，此种情况无法自动判断是否需要重计算，可以手工指定进行强制计算。
                
            :param Query query: 查询条件
            :param bool force: 强制重新计算
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def af(self) -> AllocateFundsBase:
        """
        设置或获取资产分配算法
        """
    @af.setter
    def af(self, arg1: AllocateFundsBase) -> None:
        ...
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        查询条件
        """
    @query.setter
    def query(self, arg1: Query) -> None:
        ...
    @property
    def real_sys_list(self) -> list[...]:
        """
        由 PF 运行时设定的实际运行系统列表
        """
    @property
    def se(self) -> SelectorBase:
        """
        设置或获取交易对象选择算法
        """
    @se.setter
    def se(self, arg1: SelectorBase) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        设置或获取交易管理对象
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
class PositionRecord:
    """
    持仓记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock, arg1: Datetime, arg2: Datetime, arg3: float, arg4: float, arg5: float, arg6: float, arg7: float, arg8: float, arg9: float, arg10: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def buy_money(self) -> float:
        """
        累计买入资金（float）
        """
    @buy_money.setter
    def buy_money(self, arg0: float) -> None:
        ...
    @property
    def clean_datetime(self) -> Datetime:
        """
        平仓日期，当前持仓记录中为 constant.null_datetime
        """
    @clean_datetime.setter
    def clean_datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def goal_price(self) -> float:
        """
        当前的目标价格（float）
        """
    @goal_price.setter
    def goal_price(self, arg0: float) -> None:
        ...
    @property
    def number(self) -> float:
        """
        当前持仓数量（float）
        """
    @number.setter
    def number(self, arg0: float) -> None:
        ...
    @property
    def sell_money(self) -> float:
        """
        累计卖出资金（float）
        """
    @sell_money.setter
    def sell_money(self, arg0: float) -> None:
        ...
    @property
    def stock(self) -> Stock:
        """
        交易对象（Stock）
        """
    @stock.setter
    def stock(self, arg0: Stock) -> None:
        ...
    @property
    def stoploss(self) -> float:
        """
        当前止损价（float）
        """
    @stoploss.setter
    def stoploss(self, arg0: float) -> None:
        ...
    @property
    def take_datetime(self) -> Datetime:
        """
        初次建仓时刻（Datetime）
        """
    @take_datetime.setter
    def take_datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def total_cost(self) -> float:
        """
        累计交易总成本（float）
        """
    @total_cost.setter
    def total_cost(self, arg0: float) -> None:
        ...
    @property
    def total_number(self) -> float:
        """
        累计持仓数量（float）
        """
    @total_number.setter
    def total_number(self, arg0: float) -> None:
        ...
    @property
    def total_profit(self) -> float:
        """
        total_profit(self):
        
            累计盈利 = 累计卖出资金 - 累计买入资金 - 累计交易成本
            注意: 只对已清仓的记录有效, 未清仓的记录返回0  
        """
    @property
    def total_risk(self) -> float:
        """
        累计交易风险 = 各次 （买入价格-止损)*买入数量, 不包含交易成本
        """
    @total_risk.setter
    def total_risk(self, arg0: float) -> None:
        ...
class PositionRecordList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(pos_list):
        """
        转化为pandas的DataFrame
        """
    @staticmethod
    def to_np(pos_list):
        """
        转化为numpy结构数组
        """
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: PositionRecordList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> PositionRecordList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: PositionRecordList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: PositionRecordList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: PositionRecordList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: PositionRecordList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class ProfitGoalBase:
    """
    盈利目标策略基类
        
    自定义盈利目标策略接口：
    
    - getGoal : 【必须】获取目标价格
    - _calculate : 【必须】子类计算接口
    - _clone : 【必须】克隆接口
    - _reset : 【可选】重载私有变量
    - buyNotify : 【可选】接收实际买入通知，预留用于多次增减仓处理
    - sellNotify : 【可选】接收实际卖出通知，预留用于多次增减仓处理
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: ProfitGoalBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def buy_notify(self, arg0: TradeRecord) -> None:
        """
        buy_notify(self, trade_record)
            
            【重载接口】交易系统发生实际买入操作时，通知交易变化情况，一般存在多次增减仓的情况才需要重载
        
            :param TradeRecord trade_record: 发生实际买入时的实际买入交易记录
        """
    def clone(self) -> ProfitGoalBase:
        """
        克隆操作
        """
    def get_goal(self, arg0: Datetime, arg1: float) -> float:
        """
        get_goal(self, datetime, price)
        
            【重载接口】获取盈利目标价格，返回constant.null_price时，表示未限定目标；返回0意味着需要卖出
        
            :param Datetime datetime: 买入时间
            :param float price: 买入价格
            :return: 目标价格
            :rtype: float
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def sell_notify(self, arg0: TradeRecord) -> None:
        """
        sell_notify(self, trade_record)
            
            【重载接口】交易系统发生实际卖出操作时，通知实际交易变化情况，一般存在多次增减仓的情况才需要重载
                
            :param TradeRecord trade_record: 发生实际卖出时的实际卖出交易记录
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        设置或获取交易管理账户
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        设置或获取交易对象
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
class Query:
    """
    K线数据查询条件
    """
    class QueryType:
        """
        Members:
        
          INDEX : 按索引方式查询
        
          DATE : 按日期方式查询
        
          INVALID : 无效类型
        """
        DATE: typing.ClassVar[Query.QueryType]  # value = <QueryType.DATE: 1>
        INDEX: typing.ClassVar[Query.QueryType]  # value = <QueryType.INDEX: 0>
        INVALID: typing.ClassVar[Query.QueryType]  # value = <QueryType.INVALID: 2>
        __members__: typing.ClassVar[dict[str, Query.QueryType]]  # value = {'INDEX': <QueryType.INDEX: 0>, 'DATE': <QueryType.DATE: 1>, 'INVALID': <QueryType.INVALID: 2>}
        @staticmethod
        def _pybind11_conduit_v1_(*args, **kwargs):
            ...
        def __eq__(self, other: typing.Any) -> bool:
            ...
        def __getstate__(self) -> int:
            ...
        def __hash__(self) -> int:
            ...
        def __index__(self) -> int:
            ...
        def __init__(self, value: int) -> None:
            ...
        def __int__(self) -> int:
            ...
        def __ne__(self, other: typing.Any) -> bool:
            ...
        def __repr__(self) -> str:
            ...
        def __setstate__(self, state: int) -> None:
            ...
        def __str__(self) -> str:
            ...
        @property
        def name(self) -> str:
            ...
        @property
        def value(self) -> int:
            ...
    class RecoverType:
        """
        Members:
        
          NO_RECOVER : 不复权
        
          FORWARD : 前向复权
        
          BACKWARD : 后向复权
        
          EQUAL_FORWARD : 等比前向复权
        
          EQUAL_BACKWARD : 等比后向复权
        
          INVALID : 无效类型
        """
        BACKWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.BACKWARD: 2>
        EQUAL_BACKWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.EQUAL_BACKWARD: 4>
        EQUAL_FORWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.EQUAL_FORWARD: 3>
        FORWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.FORWARD: 1>
        INVALID: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.INVALID: 5>
        NO_RECOVER: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.NO_RECOVER: 0>
        __members__: typing.ClassVar[dict[str, Query.RecoverType]]  # value = {'NO_RECOVER': <RecoverType.NO_RECOVER: 0>, 'FORWARD': <RecoverType.FORWARD: 1>, 'BACKWARD': <RecoverType.BACKWARD: 2>, 'EQUAL_FORWARD': <RecoverType.EQUAL_FORWARD: 3>, 'EQUAL_BACKWARD': <RecoverType.EQUAL_BACKWARD: 4>, 'INVALID': <RecoverType.INVALID: 5>}
        @staticmethod
        def _pybind11_conduit_v1_(*args, **kwargs):
            ...
        def __eq__(self, other: typing.Any) -> bool:
            ...
        def __getstate__(self) -> int:
            ...
        def __hash__(self) -> int:
            ...
        def __index__(self) -> int:
            ...
        def __init__(self, value: int) -> None:
            ...
        def __int__(self) -> int:
            ...
        def __ne__(self, other: typing.Any) -> bool:
            ...
        def __repr__(self) -> str:
            ...
        def __setstate__(self, state: int) -> None:
            ...
        def __str__(self) -> str:
            ...
        @property
        def name(self) -> str:
            ...
        @property
        def value(self) -> int:
            ...
    BACKWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.BACKWARD: 2>
    DATE: typing.ClassVar[Query.QueryType]  # value = <QueryType.DATE: 1>
    DAY: typing.ClassVar[str] = 'DAY'
    EQUAL_BACKWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.EQUAL_BACKWARD: 4>
    EQUAL_FORWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.EQUAL_FORWARD: 3>
    FORWARD: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.FORWARD: 1>
    HALFYEAR: typing.ClassVar[str] = 'HALFYEAR'
    HOUR12: typing.ClassVar[str] = 'HOUR12'
    HOUR2: typing.ClassVar[str] = 'HOUR2'
    HOUR4: typing.ClassVar[str] = 'HOUR4'
    HOUR6: typing.ClassVar[str] = 'HOUR6'
    INDEX: typing.ClassVar[Query.QueryType]  # value = <QueryType.INDEX: 0>
    INVALID: typing.ClassVar[Query.QueryType]  # value = <QueryType.INVALID: 2>
    MIN: typing.ClassVar[str] = 'MIN'
    MIN15: typing.ClassVar[str] = 'MIN15'
    MIN3: typing.ClassVar[str] = 'MIN3'
    MIN30: typing.ClassVar[str] = 'MIN30'
    MIN5: typing.ClassVar[str] = 'MIN5'
    MIN60: typing.ClassVar[str] = 'MIN60'
    MONTH: typing.ClassVar[str] = 'MONTH'
    NO_RECOVER: typing.ClassVar[Query.RecoverType]  # value = <RecoverType.NO_RECOVER: 0>
    QUARTER: typing.ClassVar[str] = 'QUARTER'
    WEEK: typing.ClassVar[str] = 'WEEK'
    YEAR: typing.ClassVar[str] = 'YEAR'
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def get_all_ktype() -> list[str]:
        """
        获取所有KType
        """
    def __getstate__(self) -> tuple:
        ...
    def __init__(self, start = 0, end = None, ktype = 'DAY', recover_type = ...):
        """
        
        构建按索引 [start, end) 方式获取K线数据条件。start，end应同为 int 或 同为 Datetime 类型。
        
        :param int|Datetime start: 起始索引位置或起始日期
        :param int|Datetime end: 结束索引位置或结束日期
        :param Query.KType ktype: K线数据类型（如日线、分钟线等）
        :param Query.RecoverType recover_type: 复权类型
        :return: 查询条件
        :rtype: KQuery
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def get_ktype_in_min(self: str) -> int:
        """
        获取ktype对应的分钟数
        """
    @property
    def end(self) -> int:
        """
        结束索引，当按日期查询方式创建时无效
        """
    @property
    def end_datetime(self) -> Datetime:
        """
        结束日期，当按索引查询方式创建时无效
        """
    @property
    def ktype(self) -> str:
        """
        查询的K线类型
        """
    @property
    def query_type(self) -> ...:
        """
        查询方式
        """
    @property
    def recover_type(self) -> ...:
        """
        复权类别
        """
    @property
    def start(self) -> int:
        """
        起始索引，当按日期查询方式创建时无效
        """
    @property
    def start_datetime(self) -> Datetime:
        """
        起始日期，当按索引查询方式创建时无效
        """
class ScoreRecord:
    """
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock, arg1: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    @property
    def stock(self) -> Stock:
        """
        证券
        """
    @stock.setter
    def stock(self, arg0: Stock) -> None:
        ...
    @property
    def value(self) -> float:
        """
        分值
        """
    @value.setter
    def value(self, arg0: float) -> None:
        ...
class ScoreRecordList:
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, s: slice) -> ScoreRecordList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: ScoreRecordList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: ScoreRecordList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    @typing.overload
    def extend(self, L: ScoreRecordList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
class SelectorBase:
    """
    选择器策略基类，实现标的、系统策略的评估和选取算法，自定义选择器策略子类接口：
    
        - get_selected - 【必须】获取指定时刻选择的系统实例列表
        - _calculate - 【必须】计算接口
        - _reset - 【可选】重置私有属性
        - _clone - 【必须】克隆接口
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __add__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    @typing.overload
    def __add__(self, arg0: float) -> SelectorBase:
        ...
    def __and__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: SelectorBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    @typing.overload
    def __mul__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    @typing.overload
    def __mul__(self, arg0: float) -> SelectorBase:
        ...
    def __or__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    def __radd__(self, arg0: float) -> SelectorBase:
        ...
    def __repr__(self) -> str:
        ...
    def __rmul__(self, arg0: float) -> SelectorBase:
        ...
    def __rsub__(self, arg0: float) -> SelectorBase:
        ...
    def __rtruediv__(self, arg0: float) -> SelectorBase:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @typing.overload
    def __sub__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    @typing.overload
    def __sub__(self, arg0: float) -> SelectorBase:
        ...
    @typing.overload
    def __truediv__(self, arg0: SelectorBase) -> SelectorBase:
        ...
    @typing.overload
    def __truediv__(self, arg0: float) -> SelectorBase:
        ...
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        子类复位操作实现
        """
    def add_stock(self, stock: Stock, sys: ...) -> None:
        """
        add_stock(self, stock, sys)
        
            加入初始标的及其对应的系统策略原型
        
            :param Stock stock: 加入的初始标的
            :param System sys: 系统策略原型
        """
    def add_stock_list(self, stk_list: typing.Sequence, sys: ...) -> None:
        """
        add_stock_list(self, stk_list, sys)
        
            加入初始标的列表及其系统策略原型
        
            :param StockList stk_list: 加入的初始标的列表
            :param System sys: 系统策略原型
        """
    def add_sys(self, arg0: ...) -> None:
        ...
    def add_sys_list(self, arg0: list[...]) -> None:
        ...
    def calculate(self, arg0: list[...], arg1: Query) -> None:
        ...
    def clone(self) -> SelectorBase:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_proto_sys_list(self) -> list[...]:
        ...
    def get_real_sys_list(self) -> list[...]:
        ...
    def get_selected(self, arg0: Datetime) -> SystemWeightList:
        """
        get_selected(self, datetime)
        
            【重载接口】获取指定时刻选取的系统实例
        
            :param Datetime datetime: 指定时刻
            :return: 选取的系统实例列表
            :rtype: SystemList
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def is_match_af(self, arg0: ...) -> bool:
        """
        is_match_af(self)
        
            【重载接口】判断是否和 AF 匹配
        
            :param AllocateFundsBase af: 资产分配算法
        """
    def remove_all(self) -> None:
        """
        清除所有已加入的原型系统
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        算法名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def proto_sys_list(self) -> list[...]:
        """
        原型系统列表
        """
    @property
    def real_sys_list(self) -> list[...]:
        """
        由 PF 运行时设定的实际运行系统列表
        """
class SignalBase:
    """
    信号指示器基类
        信号指示器负责产生买入、卖出信号。
    
    公共参数：
    
        - alternate (bool|True) ：买入和卖出信号是否交替出现。单线型的信号通常通过拐点、斜率等判断信号的产生，此种情况下可能出现连续出现买入信号或连续出现卖出信号的情况，此时可通过该参数控制买入、卖出信号是否交替出现。而双线交叉型的信号通常本身买入和卖出已经是交替出现，此时该参数无效。
    
    自定义的信号指示器接口：
    
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def plot(sg, new = True, axes = None, style = 1, kdata = None):
        """
        绘制买入/卖出信号
        
        :param SignalBase sg: 信号指示器
        :param new: 仅在未指定axes的情况下生效，当为True时，创建新的窗口对象并在其中进行绘制
        :param axes: 指定在那个轴对象中进行绘制
        :param style: 1 | 2 信号箭头绘制样式
        :param KData kdata: 指定的KData（即信号发生器的交易对象），
                           如该值为None，则认为该信号发生器已经指定了交易对象，
                           否则，使用该参数作为交易对象
        """
    @typing.overload
    def __add__(self, arg0: SignalBase) -> SignalBase:
        ...
    @typing.overload
    def __add__(self, arg0: float) -> SignalBase:
        ...
    def __and__(self, arg0: SignalBase) -> SignalBase:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: SignalBase) -> None:
        ...
    @typing.overload
    def __mul__(self, arg0: SignalBase) -> SignalBase:
        ...
    @typing.overload
    def __mul__(self, arg0: float) -> SignalBase:
        ...
    def __or__(self, arg0: SignalBase) -> SignalBase:
        ...
    def __radd__(self, arg0: float) -> SignalBase:
        ...
    def __repr__(self) -> str:
        ...
    def __rmul__(self, arg0: float) -> SignalBase:
        ...
    def __rsub__(self, arg0: float) -> SignalBase:
        ...
    def __rtruediv__(self, arg0: float) -> SignalBase:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @typing.overload
    def __sub__(self, arg0: SignalBase) -> SignalBase:
        ...
    @typing.overload
    def __sub__(self, arg0: float) -> SignalBase:
        ...
    @typing.overload
    def __truediv__(self, arg0: SignalBase) -> SignalBase:
        ...
    @typing.overload
    def __truediv__(self, arg0: float) -> SignalBase:
        ...
    def _add_buy_signal(self, datetime: Datetime, value: float = 1.0) -> None:
        """
        _add_buy_signal(self, datetime)
        
            加入买入信号，在_calculate中调用
        
            :param Datetime datetime: 指示买入的日期
        """
    def _add_sell_signal(self, datetime: Datetime, value: float = -1.0) -> None:
        """
        _add_sell_signal(self, datetime)
        
            加入卖出信号，在_calculate中调用
        
            :param Datetime datetime: 指示卖出的日期
        """
    def _add_signal(self, datetime: Datetime, value: float) -> None:
        ...
    def _calculate(self, arg0: KData) -> None:
        """
        _calculate(self, kdata)
              
            【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def clone(self) -> SignalBase:
        """
        克隆操作
        """
    def get_buy_signal(self) -> DatetimeList:
        """
        get_buy_signal(self)
        
            获取所有买入指示日期列表
            
            :rtype: DatetimeList
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_sell_signal(self) -> DatetimeList:
        """
        get_sell_signal(self)
        
            获取所有卖出指示日期列表
        
            :rtype: DatetimeList
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def next_time_should_buy(self) -> bool:
        """
        next_time_should_byu(self)
        
            下一时刻是否可以买入，相当于最后时刻是否指示买入
        """
    def next_time_should_sell(self) -> bool:
        """
        next_time_should_sell(self)
              
            下一时刻是否可以卖出，相当于最后时刻是否指示卖出
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    def should_buy(self, arg0: Datetime) -> bool:
        """
        should_buy(self, datetime)
        
            指定时刻是否可以买入
        
            :param Datetime datetime: 指定时刻
            :rtype: bool
        """
    def should_sell(self, arg0: Datetime) -> bool:
        """
        should_sell(self, datetime)
        
            指定时刻是否可以卖出
        
            :param Datetime datetime: 指定时刻
            :rtype: bool
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        设置或获取交易对象
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
class SlippageBase:
    """
    移滑价差算法基类
    
    自定义移滑价差接口：
    
        - getRealBuyPrice : 【必须】计算实际买入价格
        - getRealSellPrice : 【必须】计算实际卖出价格
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: SlippageBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def clone(self) -> SlippageBase:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_real_buy_price(self, arg0: Datetime, arg1: float) -> float:
        """
        get_real_buy_price(self, datetime, price)
        
            【重载接口】计算实际买入价格
        
            :param Datetime datetime: 买入时间
            :param float price: 计划买入价格
            :return: 实际买入价格
            :rtype: float
        """
    def get_real_sell_price(self, arg0: Datetime, arg1: float) -> float:
        """
        get_real_sell_price(self, datetime, price)
        
            【重载接口】计算实际卖出价格
        
            :param Datetime datetime: 卖出时间
            :param float price: 计划卖出价格
            :return: 实际卖出价格
            :rtype: float
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        关联交易对象
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
class SpotRecord:
    amount: float
    ask1: float
    ask1_amount: float
    ask2: float
    ask2_amount: float
    ask3: float
    ask3_amount: float
    ask4: float
    ask4_amount: float
    ask5: float
    ask5_amount: float
    bid1: float
    bid1_amount: float
    bid2: float
    bid2_amount: float
    bid3: float
    bid3_amount: float
    bid4: float
    bid4_amount: float
    bid5: float
    bid5_amount: float
    close: float
    code: str
    datetime: Datetime
    high: float
    low: float
    market: str
    name: str
    open: float
    volume: float
    yesterday_close: float
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __init__(self) -> None:
        ...
class Stock:
    """
    证券对象
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: Stock) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __hash__(self):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, market: str, code: str, name: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock) -> None:
        ...
    def __ne__(self, arg0: Stock) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def get_belong_to_block_list(self, category: typing.Any = None) -> list[...]:
        """
        get_belong_to_block_list(self[, category=None])
              
              获取所属板块列表
        
              :param str category: 指定的板块分类，为 None 时，返回所有板块分类下的所属板块
              :rtype: list
        """
    def get_count(self, ktype: str = 'DAY') -> int:
        """
        get_count(self, [ktype=Query.DAY])
        
                获取不同类型K线数据量
        
                :param Query.KType ktype: K线数据类别
                :return: K线记录数
                :rtype: int
        """
    def get_datetime_list(self, arg0: Query) -> DatetimeList:
        """
        get_datetime_list(self, query)
        
                获取日期列表
        
                :param Query query: 查询条件
                :rtype: DatetimeList
        """
    def get_finance_info(self) -> ...:
        """
        get_finance_info(self)
        
                获取当前财务信息
        
                :rtype: Parameter
        """
    def get_history_finance(self) -> list:
        """
        get_history_finance(self)
                
                获取所有历史财务信息历史记录， 字段信息可参考 StockManager 中的相关方法: get_history_finance_all_fields/get_history_finance_field_index/get_history_finance_field_name 方法
                日常建议直接使用指标 FINANCE 获取财务数据
        """
    def get_kdata(self, arg0: Query) -> KData:
        """
        get_kdata(self, query)
        
                获取K线数据
        
                :param Query query: 查询条件
                :return: 满足查询条件的K线数据
                :rtype: KData
        """
    @typing.overload
    def get_krecord(self, pos: int, ktype: str = 'DAY') -> KRecord:
        """
        get_krecord(self, pos[, ktype=Query.DAY])
        
                获取指定索引的K线数据记录，未作越界检查
        
                :param int pos: 指定的索引位置
                :param Query.KType ktype: K线数据类别
                :return: K线记录
                :rtype: KRecord
        """
    @typing.overload
    def get_krecord(self, date: Datetime, ktype: str = 'DAY') -> KRecord:
        """
        get_krecord(self, datetime[, ktype=Query.DAY])
        
                根据数据类型（日线/周线等），获取指定时刻的KRecord
        
                :param Datetime datetime: 指定日期时刻
                :param Query.KType ktype: K线数据类别
                :return: K线记录
                :rtype: KRecord
        """
    def get_krecord_list(self, arg0: Query) -> KRecordList:
        """
        get_krecord_list(self, start, end,
                  ktype)
        
                获取K线记录 [start, end)，一般不直接使用.
        
                :param int start: 起始位置
                :param int end: 结束位置
                :param Query.KType ktype: K线类别
                :return: K线记录列表
                :rtype: KRecordList
        """
    def get_market_value(self, arg0: Datetime, arg1: str) -> float:
        """
        get_market_value(self, date, ktype)
        
                获取指定时刻的市值，即小于等于指定时刻的最后一条记录的收盘价
        
                :param Datetime date: 指定时刻
                :param Query.KType ktype: K线数据类别
                :return: 指定时刻的市值
                :rtype: float
        """
    def get_timeline_list(self, arg0: Query) -> TimeLineList:
        """
        get_timeline_list(self, query)
        
                获取分时线
        
                :param Query query: 查询条件（查询条件中的K线类型、复权类型参数此时无用）
                :rtype: TimeLineList
        """
    def get_trading_calendar(self, query: Query) -> DatetimeList:
        """
        get_trading_calendar(self, query)
        
                获取自身市场的交易日日历（（不是本身的交易日期）
        
                :param KQuery query: Query查询条件
                :return: 日期列表
                :rtype: DatetimeList
        """
    def get_trans_list(self, arg0: Query) -> TransList:
        """
        get_trans_list(self, query)
        
                获取历史分笔数据
        
                :param Query query: 查询条件（查询条件中的K线类型、复权类型参数此时无用）
                :rtype: TransList
        """
    def get_weight(self, start: Datetime = ..., end: Datetime = ...) -> StockWeightList:
        """
        get_weight(self, [start, end])
        
                获取指定时间段[start,end)内的权息信息。未指定起始、结束时刻时，获取全部权息记录。
        
                :param Datetime start: 起始时刻
                :param Datetime end: 结束时刻
                :rtype: StockWeightList
        """
    def is_buffer(self, arg0: str) -> bool:
        """
        指定类型的K线数据是否被缓存
        """
    def is_null(self) -> bool:
        """
        is_null(self)
        
                是否为Null
        
                :rtype: bool
        """
    def load_kdata_to_buffer(self, arg0: str) -> None:
        """
        load_kdata_to_buffer(self,
                  ktype)
        
                将指定类别的K线数据加载至内存缓存
        
                :param Query.KType ktype: K线类型
        """
    def realtime_update(self, krecord: KRecord, ktype: str = 'DAY') -> None:
        """
        realtime_update(self, krecord)
        
                只用于更新缓存中的日线数据
        
                :param KRecord krecord: 新增的实时K线记录
                :param KQuery.KType ktype: K 线类型
        """
    def release_kdata_buffer(self, arg0: str) -> None:
        """
        release_kdata_buffer(self,
                  ktype)
        
                释放指定类别的内存K线数据
        
                :param Query.KType ktype: K线类型
        """
    def set_krecord_list(self, krecord_list: typing.Any, ktype: str = 'DAY') -> None:
        """
        set_krecord_list(self, krecord_list, [ktype=Query.DAY])
        
              "谨慎调用！！！直接设置当前内存 KRecordList, 仅供需临时增加的外部 Stock 设置 K 线数据
        
              :param krecord_list: KRecordList or list of KRecord
              :param Query.KType ktype: K线类别
        """
    @property
    def atom(self) -> float:
        """
        最小交易数量，同min_tradeNumber
        """
    @atom.setter
    def atom(self, arg1: float) -> None:
        ...
    @property
    def code(self) -> str:
        """
        证券代码
        """
    @code.setter
    def code(self, arg1: str) -> None:
        ...
    @property
    def id(self) -> int:
        """
        内部id
        """
    @property
    def last_datetime(self) -> Datetime:
        """
        证券最后日期
        """
    @last_datetime.setter
    def last_datetime(self, arg1: Datetime) -> None:
        ...
    @property
    def market(self) -> str:
        """
        所属市场简称，市场简称是市场的唯一标识
        """
    @market.setter
    def market(self, arg1: str) -> None:
        ...
    @property
    def market_code(self) -> str:
        """
        市场简称+证券代码，如: sh000001
        """
    @property
    def max_trade_number(self) -> float:
        """
        最大交易数量
        """
    @max_trade_number.setter
    def max_trade_number(self, arg1: float) -> None:
        ...
    @property
    def min_trade_number(self) -> float:
        """
        最小交易数量
        """
    @min_trade_number.setter
    def min_trade_number(self, arg1: float) -> None:
        ...
    @property
    def name(self) -> str:
        """
        证券名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def precision(self) -> int:
        """
        价格精度
        """
    @precision.setter
    def precision(self, arg1: int) -> None:
        ...
    @property
    def start_datetime(self) -> Datetime:
        """
        证券起始日期
        """
    @start_datetime.setter
    def start_datetime(self, arg1: Datetime) -> None:
        ...
    @property
    def tick(self) -> float:
        """
        最小跳动量
        """
    @tick.setter
    def tick(self, arg1: float) -> None:
        ...
    @property
    def tick_value(self) -> float:
        """
        最小跳动量价值
        """
    @tick_value.setter
    def tick_value(self, arg1: float) -> None:
        ...
    @property
    def type(self) -> int:
        """
        证券类型，参见：constant
        """
    @type.setter
    def type(self, arg1: int) -> None:
        ...
    @property
    def unit(self) -> float:
        """
        每单位价值 = tickValue / tick
        """
    @property
    def valid(self) -> bool:
        """
        该证券当前是否有效
        """
    @valid.setter
    def valid(self, arg1: bool) -> None:
        ...
class StockManager:
    """
    证券信息管理类
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def instance() -> StockManager:
        """
        获取StockManager单例实例
        """
    def __getitem__(self, arg0: str) -> ...:
        """
        同 get_stock
        """
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        """
        返回证券数量
        """
    def add_block(self, arg0: ...) -> None:
        """
        add_block(self, block)
              
            将独立的板块加入到数据库中， 板块通过 category+name 区分， 数据库中相同板块将被覆盖。注意，如果板块发生变化，需要调用 save_block 重新保存。
              
            :param Block block: 板块实例
        """
    def add_stock(self, arg0: ...) -> bool:
        """
        add_stock(self, stock)
              
            谨慎调用！！！仅供增加某些临时的外部 Stock
            @return True | False
        """
    def add_temp_csv_stock(self, code: str, day_filename: str, min_filename: str, tick: float = 0.01, tick_value: float = 0.01, precision: int = 2, min_trade_num: int = 1, max_trade_num: int = 1000000) -> ...:
        """
        add_temp_csv_stock(code, day_filename, min_filename[, tick=0.01, tick_value=0.01,
                precision=2, min_trade_num = 1, max_trade_num=1000000])
        
            从CSV文件（K线数据）增加临时的Stock，可用于只有CSV格式的K线数据时，进行临时测试。
        
            添加的 stock 对应的 market 为 "TMP", 如需通过 sm 获取，需加入 tmp，如：sm['tmp0001']
        
            CSV文件第一行为标题，需含有
            Datetime（或Date、日期）、OPEN（或开盘价）、HIGH（或最高价）、LOW（或最低价）、CLOSE（或收盘价）、AMOUNT（或成交金额）、VOLUME（或VOL、COUNT、成交量）。
        
            注意：请确保 csv 使用 utf8 格式存储，否则无法识别中文
        
            :param str code: 自行编号的证券代码，不能和已有的Stock相同，否则将返回Null<Stock>
            :param str day_filename: 日线CSV文件名
            :param str min_filename: 分钟线CSV文件名
            :param float tick: 最小跳动量，默认0.01
            :param float tick_value: 最小跳动量价值，默认0.01
            :param int precision: 价格精度，默认2
            :param int min_trade_num: 单笔最小交易量，默认1
            :param int max_trade_num: 单笔最大交易量，默认1000000
            :return: 加入的Stock
            :rtype: Stock
        """
    def datadir(self) -> str:
        """
        datadir(self) -> str
        
            获取财务数据存放路径
        """
    def get_base_info_parameter(self) -> ...:
        """
        获取当前基础信息驱动参数
        """
    def get_block(self, arg0: str, arg1: str) -> ...:
        """
        get_block(self, category, name)
        
            获取预定义的板块
        
            :param str category: 板块分类
            :param str name: 板块名称
            :return: 板块，如找不到返回空Block
            :rtype: Block
        """
    @typing.overload
    def get_block_list(self) -> list[...]:
        ...
    @typing.overload
    def get_block_list(self, arg0: str) -> list[...]:
        """
        get_block_list(self[, category])
        
            获取指定分类的板块列表
        
            :param str category: 板块分类
            :return: 板块列表
            :rtype: BlockList
        """
    def get_block_parameter(self) -> ...:
        """
        获取当前板块信息驱动参数
        """
    def get_context(self) -> StrategyContext:
        """
        获取当前上下文
        """
    def get_hikyuu_parameter(self) -> ...:
        """
        获取当前其他参数
        """
    def get_history_finance_all_fields(self) -> list:
        """
        get_history_finance_all_fields(self)
            获取所有历史财务信息字段及其索引
        """
    def get_history_finance_field_index(self, arg0: str) -> int:
        """
        get_history_finance_field_index(self, name)
            
            根据字段名称，获取历史财务信息相应字段索引
        """
    def get_history_finance_field_name(self, arg0: int) -> str:
        """
        get_history_finance_field_name(self, index)
                   
            根据字段索引，获取历史财务信息相应字段名
        """
    def get_kdata_parameter(self) -> ...:
        """
        获取当前K线数据驱动参数
        """
    def get_market_info(self, arg0: str) -> MarketInfo:
        """
        get_market_info(self, market)
        
            获取相应的市场信息
        
            :param string market: 指定的市场标识（市场简称）
            :return: 相应的市场信息，如果相应的市场信息不存在，则返回Null<MarketInfo>()
            :rtype: MarketInfo
        """
    def get_market_list(self) -> list[str]:
        """
        get_market_list(self)
        
            获取市场简称列表
        
            :rtype: StringList
        """
    def get_market_stock(self, arg0: str) -> ...:
        """
        get_market_stock(self, market)
        
            获取指定市场的代表指数（可能为空）
        
            :param string market: 指定的市场标识（市场简称）
            :return: 相应的市场代表指数，如果相应的市场信息不存在，则返回Null<Stock>()
            :rtype: Stock
        """
    def get_plugin_path(self) -> str:
        """
        get_plugin_path(self)
        
            获取插件路径
        """
    def get_preload_parameter(self) -> ...:
        """
        获取当前预加载参数
        """
    def get_stock(self, arg0: str) -> ...:
        """
        get_stock(self, querystr)
        
            根据"市场简称证券代码"获取对应的证券实例
        
            :param str querystr: 格式：“市场简称证券代码”，如"sh000001"
            :return: 对应的证券实例，如果实例不存在，则Null<Stock>()，不抛出异常
            :rtype: Stock
        """
    def get_stock_list(self, filter: typing.Any = None) -> list[...]:
        """
        get_stock_list(self[, filter=None])
                
            获取证券列表
        
            :param func filter: 输入参数为 stock, 返回 True | False 的过滤函数
        """
    def get_stock_type_info(self, arg0: int) -> StockTypeInfo:
        """
        get_stock_type_info(self, stk_type)
        
            获取相应的证券类型详细信息
        
            :param int stk_type: 证券类型，参见： :py:data:`constant`
            :return: 对应的证券类型信息，如果不存在，则返回Null<StockTypeInfo>()
            :rtype: StockTypeInfo
        """
    def get_trading_calendar(self, query: ..., market: str = 'SH') -> DatetimeList:
        """
        get_trading_calendar(self, query[, market='SH'])
        
            获取指定市场的交易日日历
        
            :param KQuery query: Query查询条件
            :param str market: 市场简称
            :return: 日期列表
            :rtype: DatetimeList
        """
    def init(self, base_info_param: ..., block_param: ..., kdata_param: ..., preload_param: ..., hikyuu_param: ..., context: StrategyContext = ...) -> None:
        """
        init(self, base_info_param, block_param, kdata_param, preload_param, hikyuu_param, context)
                      
            初始化函数，必须在程序入口调用
            
            :param base_info_param 基础信息驱动参数
             param block_param 板块信息驱动参数
             param kdata_param K线数据驱动参数
             param preload_param 预加载参数
             param hikyuu_param 其他参数
             param StrategyContext context 策略上下文, 默认加载全部证券
        """
    def is_holiday(self, arg0: Datetime) -> bool:
        """
        is_holiday(self, d)
        
            判断日期是否为节假日
        
            :param Datetime d: 待判断的日期
        """
    def reload(self) -> None:
        """
        重新加载所有证券数据
        """
    @typing.overload
    def remove_block(self, category: str, name: str) -> None:
        ...
    @typing.overload
    def remove_block(self, block: ...) -> None:
        """
        remove_block(self, block)
                   
            从数据库中删除板块
            
            :param Block block: 板块实例
        """
    def remove_stock(self, arg0: str) -> None:
        """
        remove_stock(self, market_code)
            
            从 sm 中移除 market_code 代表的证券，谨慎使用！！！通常用于移除临时增加的外部 Stock
            
            :param str market_code: 证券市场标识
        """
    def remove_temp_csv_stock(self, arg0: str) -> None:
        """
        remove_temp_csv_stock(self, code)
        
            移除增加的临时Stock
        
            :param str code: 创建时自定义的编码
        """
    def save_block(self, arg0: ...) -> None:
        """
        save_block(self, block)
              
            保存发生变化后的板块保存至数据库
        
            :param Block block: 板块实例
        """
    def set_plugin_path(self, arg0: str) -> None:
        """
        set_plugin_path(self, path)
                
            设置插件路径，仅在初始化之前设置有效
        """
    def tmpdir(self) -> str:
        """
        tmpdir(self) -> str
        
            获取用于保存零时变量等的临时目录，如未配置则为当前目录 由m_config中的“tmpdir”指定
        """
    @property
    def data_ready(self) -> bool:
        """
        是否所有数据已准备就绪（加载完毕）
        """
class StockTypeInfo:
    """
    股票类型详情记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: int, arg1: str, arg2: float, arg3: float, arg4: int, arg5: float, arg6: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def description(self) -> str:
        """
        描述信息
        """
    @property
    def max_trade_num(self) -> float:
        """
        每笔最大交易量
        """
    @property
    def min_trade_num(self) -> float:
        """
        每笔最小交易量
        """
    @property
    def precision(self) -> int:
        """
        价格精度
        """
    @property
    def tick(self) -> float:
        """
        最小跳动量
        """
    @property
    def tick_value(self) -> float:
        """
        每一个tick价格
        """
    @property
    def type(self) -> int:
        """
        证券类型
        """
    @property
    def unit(self) -> float:
        """
        每最小变动量价格，即单位价格 = tick_value/tick
        """
class StockWeight:
    """
    权息记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime, arg1: float, arg2: float, arg3: float, arg4: float, arg5: float, arg6: float, arg7: float, arg8: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def bonus(self) -> float:
        """
        每10股红利
        """
    @property
    def count_as_gift(self) -> float:
        """
        每10股送X股
        """
    @property
    def count_for_sell(self) -> float:
        """
        每10股配X股
        """
    @property
    def datetime(self) -> Datetime:
        """
        权息日期
        """
    @property
    def free_count(self) -> float:
        """
        流通股（万股）
        """
    @property
    def increasement(self) -> float:
        """
        每10股转增X股
        """
    @property
    def price_for_sell(self) -> float:
        """
        配股价
        """
    @property
    def suogu(self) -> float:
        """
        扩缩股比例
        """
    @property
    def total_count(self) -> float:
        """
        总股本（万股）
        """
class StockWeightList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: StockWeightList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> StockWeightList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: StockWeightList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: StockWeightList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: StockWeightList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: StockWeightList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class StoplossBase:
    """
    止损/止赢算法基类
    自定义止损/止赢策略接口：
    
        - _calculate : 【必须】子类计算接口
        - _clone : 【必须】克隆接口
        - _reset : 【可选】重载私有变量
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: StoplossBase) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        """
        初始化构造函数
                
            :param str name: 名称
        """
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def _calculate(self) -> None:
        """
        【重载接口】子类计算接口
        """
    def _reset(self) -> None:
        """
        【重载接口】子类复位接口，复位内部私有变量
        """
    def clone(self) -> StoplossBase:
        """
        克隆操作
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_price(self, arg0: Datetime, arg1: float) -> float:
        """
        get_price(self, datetime, price)
        
            【重载接口】获取本次预期交易（买入）时的计划止损价格，如果不存在止损价，则返回0。用于系统在交易执行前向止损策略模块查询本次交易的计划止损价。
        
            .. note::
                一般情况下，止损/止赢的算法可以互换，但止损的getPrice可以传入计划交易的价格，比如以买入价格的30%做为止损。而止赢则不考虑传入的price参数，即认为price为0.0。实际上，即使止损也不建议使用price参数，如可以使用前日最低价的30%作为止损，则不需要考虑price参数。
        
            :param Datetime datetime: 交易时间
            :param float price: 计划买入的价格
            :return: 止损价格
            :rtype: float
        """
    def get_short_price(self, arg0: Datetime, arg1: float) -> float:
        ...
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reset(self) -> None:
        """
        复位操作
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        关联交易管理实例
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        关联交易对象
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
class Strategy:
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, code_list: list[str], ktype_list: list[str], preload_num: dict[str, int] = {}, name: str = 'Strategy', config: str = '') -> None:
        """
        创建策略运行时
                   
            :param list code_list: 证券代码列表，如：["sz000001", "sz000002"], "all" 代表全部证券
            :param list ktype_list: K线类型列表, 如: ["day", "min"]
            :param dict preload_num: 预加载的K线数量，如：{"day_max": 1000, "min_max": 2000}
            :param str name: 策略名称
            :param str config: 配置文件名称(如需要使用独立的配置文件，否则为空时使用默认的hikyuu配置文件)
        """
    @typing.overload
    def __init__(self, context: StrategyContext, name: str = 'Strategy', config: str = '') -> None:
        """
        使用上下文创建策略运行时
        
            :param StrategyContext context: 上下文实例
            :param str name: 策略名称
            :param str config: 配置文件名称(如需要使用独立的配置文件，否则为空时使用默认的hikyuu配置文件)
        """
    def buy(self, stock: Stock, price: float, num: float, stoploss: float = 0.0, goal_price: float = 0.0, part: SystemPart = ..., remark: str = '') -> TradeRecord:
        ...
    def get_kdata(self, stk: Stock, start_date: Datetime, end_date: Datetime, ktype: str, recover_type: Query.RecoverType = ...) -> KData:
        """
        get_kdata(self, stk, start_date, end_date, ktype, recover_type)
        
            获取指定证券指定日期范围内的K线数据(为保证实盘和回测一致，请使用本方法获取K线数据)
            :param Stock stk: 指定的证券
            :param Datetime start_date: 开始日期
            :param Datetime end_date: 结束日期
            :param KQuery.KType ktype: K线类型
            :param KQuery.RecoverType recover_type: 恢复方式
            :return: K线数据
            :rtype: KData
        """
    @typing.overload
    def get_last_kdata(self, stk: Stock, start_date: Datetime, ktype: str, recover_type: Query.RecoverType = ...) -> KData:
        ...
    @typing.overload
    def get_last_kdata(self, stk: Stock, lastnum: int, ktype: str, recover_type: Query.RecoverType = ...) -> KData:
        """
        get_last_kdata(self, stk, start_date, ktype, recover_type)
        
            获取指定证券从指定日期开始到当前时间的对应K线数据(为保证实盘和回测一致，请使用本方法获取K线数据)
        
            或 指定当前能获取到的最后 last_num 条 K线数据(为保证实盘和回测一致，请使用本方法获取K线数据)
        
            :param Stock stk: 指定的证券
            :param Datetime start_date: 开始日期  (或为 int 类型，表示从当前日期往前推多少个交易日)
            :param KQuery.KType ktype: K线类型
            :param KQuery.RecoverType recover_type: 恢复方式
            :return: K线数据
            :rtype: KData
        """
    def next_datetime(self) -> Datetime:
        """
        next_datetime(self)
        
            下一交易时间点（回测使用）
        """
    def now(self) -> Datetime:
        """
        now(self)   
        
            获取当前时间（使用该方法而不是 Datatime.now(), 以便回测和实盘一直）
        """
    def on_change(self, arg0: typing.Any) -> None:
        """
        onchang(self, func)
                   
            设置证券数据更新回调通知
        
            :param func: 一个可调用的对象如普通函数, func(stg: Strategy, stock: Stock, spot: SpotRecord
        """
    def on_received_spot(self, arg0: typing.Any) -> None:
        """
        on_received_spot(self, func)
        
            设置证券数据更新通知回调
        
            :param func: 可调用对象如普通函数, func(stg: Strategy, revTime: Datetime)
        """
    def order(self, stock: Stock, num: float, remark: str = '') -> TradeRecord:
        """
        order(self, stk, num, remark='')
        
            按数量下单（正数为买入，负数为卖出）
            :param Stock stk: 指定的证券
            :param int num: 下单数量
            :param str remark: 下单备注
        """
    def order_value(self, stock: Stock, price: float, remark: str = '') -> TradeRecord:
        """
        order_value(self, stk, value, remark='')
        
            按预期的证劵市值下单，即希望买入多少钱的证券（正数为买入，负数为卖出）
            :param Stock stk: 指定的证券
            :param float value: 投入买入资金
            :param str remark: 下单备注
        """
    def run_daily(self, func: typing.Any, time: TimeDelta, market: str = 'SH', ignore_market: bool = False) -> None:
        """
        run_daily(self, func)
                
            设置日内循环执行回调。如果忽略市场开闭市，则自启动时刻开始按间隔时间循环，
            否则第一次执行时将开盘时间对齐时间间隔，且在非开市时间停止执行。
        
            :param func: 可调用对象如普通函数，func(stg: Strategy)
            :param TimeDelta time: 间隔时间，如间隔3秒：TimeDelta(0, 0, 0, 3) 或 Seconds(3)
            :param str market: 使用哪个市场的开闭市时间
            :param ignore_market: 忽略市场开闭市时间
        """
    def run_daily_at(self, func: typing.Any, time: TimeDelta, ignore_holiday: bool = True) -> None:
        """
        run_daily_at(self, func)
        
            设置每日定点执行回调
        
            :param func: 可调用对象如普通函数，func(stg: Strategy)
            :param TimeDelta time: 执行时刻，如每日15点：TimeDelta(0, 15)
            :param ignore_holiday: 节假日不执行
        """
    def sell(self, stock: Stock, price: float, num: float, stoploss: float = 0.0, goal_price: float = 0.0, part: SystemPart = ..., remark: str = '') -> TradeRecord:
        ...
    def start(self, auto_recieve_spot: bool = True) -> None:
        """
        start(self)
        
            启动策略执行，请在完成相关回调设置后执行。
        
            :param bool auto_recieve_spot: 是否自动接收行情数据
        """
    def today(self) -> Datetime:
        """
        today(self)
        
            获取当前交易日日期（使用该方法而不是 Datatime.today(), 以便回测和实盘一直）
        """
    @property
    def context(self) -> StrategyContext:
        """
        获取策略上下文
        """
    @property
    def is_backtesting(self) -> bool:
        """
        回测状态
        """
    @property
    def name(self) -> str:
        """
        策略名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def running(self) -> bool:
        """
        获取当前运行状态
        """
    @property
    def sp(self) -> SlippageBase:
        """
        移滑价差算法
        """
    @sp.setter
    def sp(self, arg1: SlippageBase) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        关联的交易管理实例
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
class StrategyContext:
    """
    策略上下文
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: list[str]) -> None:
        ...
    @typing.overload
    def __init__(self, stock_list: list[str], ktype_list: list[str], preload_num: dict[str, int] = {}) -> None:
        """
        __init__(self, stock_list, ktype_list, [preload_num={}])
                
          创建策略上下文
        
          :param stock_list: 需要加载的证券代码列表，如：["sz000001", "sz000002"], 如包含 'ALL', 表示加载全部
          :param ktype_list: 需要加载的K线类型列表, 如：["day", "min"], 未指定时取全局配置文件中配置的默认值
          :param preload_num: 预加载数量，默认为空，如：{"min_max": 100, "day_max": 200}. 未指定时取全局配置文件中配置的默认值
          :return: 策略上下文对象
        """
    def __repr__(self) -> str:
        ...
    def __str__(self) -> str:
        ...
    def empty(self) -> bool:
        """
        上下文证券代码列表是否为空
        """
    @property
    def ktype_list(self) -> list[str]:
        """
        需要的K线类型
        """
    @ktype_list.setter
    def ktype_list(self, arg1: list[str]) -> None:
        ...
    @property
    def preload_num(self) -> dict[str, int]:
        """
        预加载数量
        """
    @preload_num.setter
    def preload_num(self, arg1: dict[str, int]) -> None:
        ...
    @property
    def start_datetime(self) -> Datetime:
        """
        起始日期
        """
    @property
    def stock_list(self) -> list[str]:
        """
        股票代码列表
        """
    @stock_list.setter
    def stock_list(self, arg1: list[str]) -> None:
        ...
class System:
    """
    系统基类。需要扩展或实现更复杂的系统交易行为，可从此类继承。
    
    系统是指针对单个交易对象的完整策略，包括环境判断、系统有效条件、资金管理、止损、止盈、盈利目标、移滑价差的完整策略，用于模拟回测。
    
    公共参数：
    
      - delay=True (bool) : 是否延迟到下一个bar开盘时进行交易
      - delay_use_current_price=True (bool) : 延迟操作的情况下，是使用当前交易时bar的价格计算新的止损价/止赢价/目标价还是使用上次计算的结果
      - max_delay_count=3 (int) : 连续延迟交易请求的限制次数，应大于等于0，0表示只允许延迟1次
      - tp_monotonic=True (bool) : 止赢单调递增
      - tp_delay_n=3 (int) : 止盈延迟开始的天数，即止盈策略判断从实际交易几天后开始生效
      - ignore_sell_sg=False (bool) : 忽略卖出信号，只使用止损/止赢等其他方式卖出
      - ev_open_position=False (bool): 是否使用市场环境判定进行初始建仓
      - cn_open_position=False (bool): 是否使用系统有效性条件进行初始建仓
    """
    CONDITION: typing.ClassVar[SystemPart]  # value = <SystemPart.CONDITION: 1>
    ENVIRONMENT: typing.ClassVar[SystemPart]  # value = <SystemPart.ENVIRONMENT: 0>
    INVALID: typing.ClassVar[SystemPart]  # value = <SystemPart.INVALID: 10>
    MONEYMANAGER: typing.ClassVar[SystemPart]  # value = <SystemPart.MONEYMANAGER: 5>
    PROFITGOAL: typing.ClassVar[SystemPart]  # value = <SystemPart.PROFITGOAL: 6>
    SIGNAL: typing.ClassVar[SystemPart]  # value = <SystemPart.SIGNAL: 2>
    SLIPPAGE: typing.ClassVar[SystemPart]  # value = <SystemPart.SLIPPAGE: 7>
    STOPLOSS: typing.ClassVar[SystemPart]  # value = <SystemPart.STOPLOSS: 3>
    TAKEPROFIT: typing.ClassVar[SystemPart]  # value = <SystemPart.TAKEPROFIT: 4>
    Part = SystemPart
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def heatmap(sys, axes = None):
        """
        
        绘制系统收益年-月收益热力图
        """
    @staticmethod
    def performance(sys, ref_stk = None):
        """
        
        绘制系统绩效，即账户累积收益率曲线
        
        :param SystemBase | PortfolioBase sys: SYS或PF实例
        :param Stock ref_stk: 参考股票, 默认为沪深300: sh000300, 绘制参考标的的收益曲线
        :return: None
        """
    @staticmethod
    def plot(sys, new = True, axes = None, style = 1, only_draw_close = False):
        """
        绘制系统实际买入/卖出信号
        
        :param SystemBase sys: 系统实例
        :param new:   仅在未指定axes的情况下生效，当为True时，
                       创建新的窗口对象并在其中进行绘制
        :param axes:  指定在那个轴对象中进行绘制
        :param style: 1 | 2 信号箭头绘制样式
        :param bool only_draw_close: 不绘制K线，仅绘制 close
        """
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self, arg0: str) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: System) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: TradeManager, arg1: MoneyManagerBase, arg2: EnvironmentBase, arg3: ConditionBase, arg4: SignalBase, arg5: StoplossBase, arg6: StoplossBase, arg7: ProfitGoalBase, arg8: SlippageBase, arg9: str) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def clone(self) -> System:
        """
        clone(self)
        
            克隆操作，会依据部件的共享特性进行克隆，共享部件不进行实际的克隆操作，保持共享。
        """
    def force_reset_all(self) -> None:
        """
        force_reset_all(self)
        
            强制复位所有组件以及清空已有的交易对象，忽略组件的共享属性。
        """
    def get_buy_short_trade_request(self) -> TradeRequest:
        ...
    def get_buy_trade_request(self) -> TradeRequest:
        """
        get_buy_trade_request(self)
          
            获取买入请求，“delay”模式下查看下一时刻是否存在买入操作
        
            :rtype: TradeRequest
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_sell_short_trade_request(self) -> TradeRequest:
        ...
    def get_sell_trade_request(self) -> TradeRequest:
        """
        get_sell_trade_request(self)
        
            获取卖出请求，“delay”模式下查看下一时刻是否存在卖出操作
        
            :rtype: TradeRequest
        """
    def get_stock(self) -> Stock:
        """
        get_stock(self)
        
            获取关联的证券
        
            :rtype: Stock
        """
    def get_trade_record_list(self) -> TradeRecordList:
        """
        get_trade_record_list(self)
        
            获取实际执行的交易记录，和 TM 的区别是不包含权息调整带来的交易记录
        
            :rtype: TradeRecordList
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def ready(self) -> None:
        ...
    def reset(self) -> None:
        """
        reset(self)
        
            复位，但不包括已有的交易对象，以及共享的部件。
        """
    @typing.overload
    def run(self, query: Query, reset: bool = True, reset_all: bool = False) -> None:
        ...
    @typing.overload
    def run(self, kdata: KData, reset: bool = True, reset_all: bool = False) -> None:
        ...
    @typing.overload
    def run(self, stock: Stock, query: Query, reset: bool = True, reset_all: bool = False) -> None:
        """
        run(self, stock, query[, reset=True])
          
            运行系统，执行回测
        
            :param Stock stock: 交易的证券
            :param Query query: K线数据查询条件
            :param bool reset: 执行前是否依据系统部件共享属性复位
            :param bool reset_all: 强制复位所有部件
        """
    def set_not_shared_all(self) -> None:
        """
        将所有组件设置为非共享
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def cn(self) -> ConditionBase:
        """
        系统有效条件
        """
    @cn.setter
    def cn(self, arg1: ConditionBase) -> None:
        ...
    @property
    def ev(self) -> EnvironmentBase:
        """
        市场环境判断策略
        """
    @ev.setter
    def ev(self, arg1: EnvironmentBase) -> None:
        ...
    @property
    def mm(self) -> MoneyManagerBase:
        """
        资金管理策略
        """
    @mm.setter
    def mm(self, arg1: MoneyManagerBase) -> None:
        ...
    @property
    def name(self) -> str:
        """
        系统名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def pg(self) -> ProfitGoalBase:
        """
        盈利目标策略
        """
    @pg.setter
    def pg(self, arg1: ProfitGoalBase) -> None:
        ...
    @property
    def query(self) -> Query:
        """
        查询条件
        """
    @property
    def sg(self) -> SignalBase:
        """
        信号指示器
        """
    @sg.setter
    def sg(self, arg1: SignalBase) -> None:
        ...
    @property
    def sp(self) -> SlippageBase:
        """
        移滑价差算法
        """
    @sp.setter
    def sp(self, arg1: SlippageBase) -> None:
        ...
    @property
    def st(self) -> StoplossBase:
        """
        止损策略
        """
    @st.setter
    def st(self, arg1: StoplossBase) -> None:
        ...
    @property
    def tm(self) -> TradeManager:
        """
        关联的交易管理实例
        """
    @tm.setter
    def tm(self, arg1: TradeManager) -> None:
        ...
    @property
    def to(self) -> KData:
        """
        交易对象 KData
        """
    @to.setter
    def to(self, arg1: KData) -> None:
        ...
    @property
    def tp(self) -> StoplossBase:
        """
        止盈策略
        """
    @tp.setter
    def tp(self, arg1: StoplossBase) -> None:
        ...
class SystemPart:
    """
    系统关联部件（各自策略）枚举定义，用于修改相关部件参数
    
    Members:
    
      ENVIRONMENT : 外部环境
    
      CONDITION : 系统前提条件
    
      SIGNAL : 信号产生器
    
      STOPLOSS : 止损策略
    
      TAKEPROFIT : 止赢策略
    
      MONEYMANAGER : 资金管理策略
    
      PROFITGOAL : 盈利目标策略
    
      SLIPPAGE : 移滑价差算法
    
      ALLOCATEFUNDS : 资产分配算法
    
      INVALID : 无效系统部件
    
      EV : 外部环境
    
      CN : 系统前提条件
    
      SG : 信号产生器
    
      ST : 止损策略
    
      TP : 止赢策略
    
      MM : 资金管理策略
    
      PG : 盈利目标策略
    
      SP : 移滑价差算法
    
      AF : 资产分配算法
    """
    AF: typing.ClassVar[SystemPart]  # value = <SystemPart.ALLOCATEFUNDS: 8>
    ALLOCATEFUNDS: typing.ClassVar[SystemPart]  # value = <SystemPart.ALLOCATEFUNDS: 8>
    CN: typing.ClassVar[SystemPart]  # value = <SystemPart.CONDITION: 1>
    CONDITION: typing.ClassVar[SystemPart]  # value = <SystemPart.CONDITION: 1>
    ENVIRONMENT: typing.ClassVar[SystemPart]  # value = <SystemPart.ENVIRONMENT: 0>
    EV: typing.ClassVar[SystemPart]  # value = <SystemPart.ENVIRONMENT: 0>
    INVALID: typing.ClassVar[SystemPart]  # value = <SystemPart.INVALID: 10>
    MM: typing.ClassVar[SystemPart]  # value = <SystemPart.MONEYMANAGER: 5>
    MONEYMANAGER: typing.ClassVar[SystemPart]  # value = <SystemPart.MONEYMANAGER: 5>
    PG: typing.ClassVar[SystemPart]  # value = <SystemPart.PROFITGOAL: 6>
    PROFITGOAL: typing.ClassVar[SystemPart]  # value = <SystemPart.PROFITGOAL: 6>
    SG: typing.ClassVar[SystemPart]  # value = <SystemPart.SIGNAL: 2>
    SIGNAL: typing.ClassVar[SystemPart]  # value = <SystemPart.SIGNAL: 2>
    SLIPPAGE: typing.ClassVar[SystemPart]  # value = <SystemPart.SLIPPAGE: 7>
    SP: typing.ClassVar[SystemPart]  # value = <SystemPart.SLIPPAGE: 7>
    ST: typing.ClassVar[SystemPart]  # value = <SystemPart.STOPLOSS: 3>
    STOPLOSS: typing.ClassVar[SystemPart]  # value = <SystemPart.STOPLOSS: 3>
    TAKEPROFIT: typing.ClassVar[SystemPart]  # value = <SystemPart.TAKEPROFIT: 4>
    TP: typing.ClassVar[SystemPart]  # value = <SystemPart.TAKEPROFIT: 4>
    __members__: typing.ClassVar[dict[str, SystemPart]]  # value = {'ENVIRONMENT': <SystemPart.ENVIRONMENT: 0>, 'CONDITION': <SystemPart.CONDITION: 1>, 'SIGNAL': <SystemPart.SIGNAL: 2>, 'STOPLOSS': <SystemPart.STOPLOSS: 3>, 'TAKEPROFIT': <SystemPart.TAKEPROFIT: 4>, 'MONEYMANAGER': <SystemPart.MONEYMANAGER: 5>, 'PROFITGOAL': <SystemPart.PROFITGOAL: 6>, 'SLIPPAGE': <SystemPart.SLIPPAGE: 7>, 'ALLOCATEFUNDS': <SystemPart.ALLOCATEFUNDS: 8>, 'INVALID': <SystemPart.INVALID: 10>, 'EV': <SystemPart.ENVIRONMENT: 0>, 'CN': <SystemPart.CONDITION: 1>, 'SG': <SystemPart.SIGNAL: 2>, 'ST': <SystemPart.STOPLOSS: 3>, 'TP': <SystemPart.TAKEPROFIT: 4>, 'MM': <SystemPart.MONEYMANAGER: 5>, 'PG': <SystemPart.PROFITGOAL: 6>, 'SP': <SystemPart.SLIPPAGE: 7>, 'AF': <SystemPart.ALLOCATEFUNDS: 8>}
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class SystemWeight:
    """
    系统权重系数结构，在资产分配时，指定对应系统的资产占比系数
    """
    weight: float
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: ..., arg1: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def sys(self) -> ...:
        """
        对应的 System 实例
        """
    @sys.setter
    def sys(self, arg0: ...) -> None:
        ...
class SystemWeightList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: SystemWeightList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> SystemWeightList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: SystemWeightList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: SystemWeightList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: SystemWeightList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: SystemWeightList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class TimeDelta:
    """
    时间时长，用于时间计算。可通过以下方式构建：
    
        - 通过 datetime.timedelta 构建。TimdeDelta(timedelta实例)
        - TimeDelta(days=0, hours=0, minutes=0, seconds=0, milliseconds=0, microseconds=0)
    
            - -99999999 <= days <= 99999999
            - -100000 <= hours <= 100000
            - -100000 <= minutes <= 100000
            - -8639900 <= seconds <= 8639900
            - -86399000000 <= milliseconds <= 86399000000
            - -86399000000 <= microseconds <= 86399000000
    
        以上参数限制，主要为防止求总微秒数时可能出现溢出的情况。如只使用一个参数不希望存在上述限制时，可使用快捷函数：
        Days, Hours, Minutes, Seconds, Milliseconds, Microseconds
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def max() -> TimeDelta:
        """
        max()
        
            支持的最大时长
        
            :return: TimeDelta(99999999, 23, 59, 59, 999, 999)
        """
    @staticmethod
    def max_ticks() -> int:
        """
        max_ticks()
        
            支持的最大 ticks （即微秒数）
        
            :rtype: int
        """
    @staticmethod
    def min() -> TimeDelta:
        """
        min()
        
            支持的最小时长
        
            :return: TimeDelta(-99999999, 0, 0, 0, 0, 0)
        """
    @staticmethod
    def min_ticks() -> int:
        """
        min_ticks()
        
            支持的最小 ticks （即微秒数）
        
            :rtype: int
        """
    @staticmethod
    def resolution() -> TimeDelta:
        """
        resolution()
        
            支持的最小精度
                
            :return: TimeDelta(0, 0, 0, 0, 0, 1)
        """
    def __abs__(self) -> TimeDelta:
        ...
    def __add__(self, td):
        """
        可和 TimeDelta, datetime.timedelta, Datetime执行相加操作
        """
    def __eq__(self, arg0: TimeDelta) -> bool:
        ...
    def __floordiv__(self, arg0: float) -> TimeDelta:
        ...
    def __ge__(self, arg0: TimeDelta) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    def __gt__(self, arg0: TimeDelta) -> bool:
        ...
    def __hash__(self):
        ...
    def __init__(self, *args, **kwargs):
        """
        
        可通过以下方式构建：
        
        - 通过 datetime.timedelta 构建。TimdeDelta(timedelta实例)
        - TimeDelta(days=0, hours=0, minutes=0, seconds=0, milliseconds=0, microseconds=0)
        
            - -99999999 <= days <= 99999999
            - -100000 <= hours <= 100000
            - -100000 <= minutes <= 100000
            - -8639900 <= seconds <= 8639900
            - -86399000000 <= milliseconds <= 86399000000
            - -86399000000 <= microseconds <= 86399000000
        """
    def __le__(self, arg0: TimeDelta) -> bool:
        ...
    def __lt__(self, arg0: TimeDelta) -> bool:
        ...
    def __mod__(self, arg0: TimeDelta) -> TimeDelta:
        ...
    def __mul__(self, arg0: float) -> TimeDelta:
        ...
    def __ne__(self, arg0: TimeDelta) -> bool:
        ...
    def __neg__(self) -> TimeDelta:
        ...
    def __pos__(self) -> TimeDelta:
        ...
    def __repr__(self) -> str:
        ...
    def __rmul__(self, arg0: float) -> TimeDelta:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def __sub__(self, td):
        """
        可减去TimeDelta, datetime.timedelta
        """
    @typing.overload
    def __truediv__(self, arg0: TimeDelta) -> float:
        ...
    @typing.overload
    def __truediv__(self, arg0: float) -> TimeDelta:
        ...
    def from_ticks(self: int) -> TimeDelta:
        """
        from_ticks(ticks)
        
            使用 ticks（即微秒数） 值创建
        
            :param int ticks: 微秒数
            :rtype: TimeDelta
        """
    def isNegative(self) -> bool:
        """
        isNegative(self)
        
            是否为负时长
        
            :rtype: bool
        """
    def timedelta(self):
        """
        转化为 datetime.timedelta 
        """
    def total_days(self) -> float:
        """
        total_days(self)
        
            获取带小数的总天数
        
            :rtype: float
        """
    def total_hours(self) -> float:
        """
        total_hours(self)
        
            获取带小数的总小时数
        
            :rtype: float
        """
    def total_milliseconds(self) -> float:
        """
        total_milliseconds(self)
        
            获取带小数的总毫秒数
        
            :rtype: float
        """
    def total_minutes(self) -> float:
        """
        total_minutes(self)
        
            获取带小数的总分钟数
        
            :rtype: float
        """
    def total_seconds(self) -> float:
        """
        total_seconds(self)
        
            获取带小数的总秒数
        
            :rtype: float
        """
    @property
    def days(self) -> int:
        """
        天数 [-99999999, 99999999]
        """
    @property
    def hours(self) -> int:
        """
        小时数 [0, 23]
        """
    @property
    def microseconds(self) -> int:
        """
        微秒数 [0, 999]
        """
    @property
    def milliseconds(self) -> int:
        """
        毫秒数 [0, 999]
        """
    @property
    def minutes(self) -> int:
        """
        分钟数 [0, 59]
        """
    @property
    def seconds(self) -> int:
        """
        秒数 [0, 59]
        """
    @property
    def ticks(self) -> int:
        """
        同总微秒数
        """
class TimeLineList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(kdata):
        """
        转化为pandas的DataFrame
        """
    @staticmethod
    def to_np(data):
        """
        转化为numpy结构数组
        """
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: TimeLineList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> TimeLineList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: TimeLineList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: TimeLineList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: TimeLineList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: TimeLineList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class TimeLineRecord:
    """
    分时线记录，属性可读写
    """
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: TimeLineRecord) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime, arg1: float, arg2: float) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def date(self) -> Datetime:
        """
        日期时间
        """
    @date.setter
    def date(self, arg0: Datetime) -> None:
        ...
    @property
    def price(self) -> float:
        """
        价格
        """
    @price.setter
    def price(self, arg0: float) -> None:
        ...
    @property
    def vol(self) -> float:
        """
        成交量
        """
    @vol.setter
    def vol(self, arg0: float) -> None:
        ...
class TradeCostBase:
    """
    交易成本算法基类
    
        自定义交易成本算法接口：
    
        :py:meth:`TradeCostBase.getBuyCost` - 【必须】获取买入成本
        :py:meth:`TradeCostBase.getSellCost` - 【必须】获取卖出成本
        :py:meth:`TradeCostBase._clone` - 【必须】子类克隆接口
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    def __init__(self, arg0: str) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def clone(self) -> TradeCostBase:
        """
        克隆操作
        """
    def get_buy_cost(self, date: Datetime, stock: Stock, price: float, num: float) -> CostRecord:
        """
        get_buy_cost(self, datetime, stock, price, num)
            
                【重载接口】获取买入成本
                
                :param Datetime datetime: 买入时刻
                :param Stock stock: 买入对象
                :param float price: 买入价格
                :param int num: 买入数量
                :return: 交易成本记录
                :rtype: CostRecord
        """
    def get_param(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_sell_cost(self, date: Datetime, stock: Stock, price: float, num: float) -> CostRecord:
        """
        get_sell_cost(self, datetime, stock, price, num)
            
                【重载接口】获取卖出成本
                
                :param Datetime datetime: 卖出时刻
                :param Stock stock: 卖出对象
                :param float price: 卖出价格
                :param int num: 卖出数量
                :return: 交易成本记录
                :rtype: CostRecord
        """
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    @property
    def name(self) -> str:
        """
        成本算法名称
        """
class TradeManager:
    """
    交易管理类，可理解为一个模拟账户进行模拟交易。一般使用 crtTM 创建交易管理实例。
    
    交易管理可理解为一个模拟账户进行模拟交易。一般使用 crtTM 创建交易管理实例。
    
    公共参数：
    
        - precision=2 (int) : 价格计算精度
        - support_borrow_cash=False (bool) : 是否自动融资
        - support_borrow_stock=False (bool) : 是否自动融券
        - save_action=True (bool) : 是否保存Python命令序列
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def heatmap(tm, start_date, end_date = None, axes = None):
        """
        
        绘制账户收益年-月收益热力图
        
        :param tm: 交易账户
        :param start_date: 开始日期
        :param end_date: 结束日期，默认为今天
        :param axes: 绘制的轴对象，默认为None，表示创建新的轴对象
        :return: None
        """
    @staticmethod
    def performance(tm: TradeManager, query: Query, ref_stk: Stock = None):
        """
        
        绘制系统绩效，即账户累积收益率曲线
        
        :param SystemBase | PortfolioBase sys: SYS或PF实例
        :param Stock ref_stk: 参考股票, 默认为沪深300: sh000300, 绘制参考标的的收益曲线
        :return: None
        """
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: str, arg1: TradeCostBase) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def add_position(self, arg0: PositionRecord) -> bool:
        """
        add_postion(self, position)
        
            建立初始账户后，直接加入持仓记录，仅用于构建初始有持仓的账户
        
            :param PositionRecord position: 持仓记录
            return True | False
        """
    def add_trade_record(self, arg0: TradeRecord) -> bool:
        """
        add_trade_record(self, tr)
        
            直接加入交易记录，如果加入初始化账户记录，将清除全部已有交易及持仓记录。
        
            :param TradeRecord tr: 交易记录
            :return: True（成功） | False（失败）
            :rtype: bool
        """
    def borrow_cash(self, arg0: Datetime, arg1: float) -> bool:
        ...
    def borrow_stock(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> bool:
        ...
    def buy(self, datetime: Datetime, stock: Stock, real_price: float, num: float, stoploss: float = 0.0, goal_price: float = 0.0, plan_price: float = 0.0, part: SystemPart = ..., remark: str = '') -> TradeRecord:
        """
        buy(self, datetime, stock, real_price, number[, stoploss=0.0, goal_price=0.0, plan_price=0.0, part=System.INVALID, remark=""])
        
            买入操作
        
            :param Datetime datetime: 买入时间
            :param Stock stock:       买入的证券
            :param float real_price:  实际买入价格
            :param float num:         买入数量
            :param float stoploss:    止损价
            :param float goal_price:  目标价格
            :param float plan_price:  计划买入价格
            :param SystemPart part:   交易指示来源
            :param string remark:     备注信息
            :rtype: TradeRecord
        """
    def buy_short(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> TradeRecord:
        ...
    def cash(self, datetime: Datetime, ktype: str = 'DAY') -> float:
        """
        cash(self, datetime[, ktype=Query.KType.DAY])
        
            获取指定时刻的现金。（注：如果不带日期参数，无法根据权息信息调整持仓。）
        
            :param Datetime datetime: 指定时刻
            :param ktype: K线类型
            :rtype: float
        """
    def checkin(self, arg0: Datetime, arg1: float) -> bool:
        """
        checkin(self, datetime, cash)
        
            向账户内存入现金
        
            :param Datetime datetime: 交易时间
            :param float cash: 存入的现金量
            :rtype: TradeRecord
        """
    def checkin_stock(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> bool:
        ...
    def checkout(self, arg0: Datetime, arg1: float) -> bool:
        """
        checkout(self, datetime, cash)
        
            从账户内取出现金
        
            :param Datetime datetime: 交易时间
            :param float cash: 取出的资金量
            :rtype: TradeRecord
        """
    def checkout_stock(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> bool:
        ...
    def clear_broker(self) -> None:
        """
        clear_broker(self)
        
            清空所有已注册订单代理
        """
    def clone(self) -> TradeManager:
        """
        克隆（深复制）实例
        """
    def fetch_asset_info_from_broker(self, broker: OrderBrokerBase, date: Datetime = ...) -> None:
        """
        fetch_asset_info_from_broker(self, date)
        
              从Broker同步当前时刻的资产信息，必须按时间顺序被调用
        
              :param broker 订单代理实例
              :param datetime 同步时，通常为当前时间（Null)，也可以强制为指定的时间点
        """
    def getParam(self, arg0: str) -> any:
        """
        get_param(self, name)
        
            获取指定的参数
        
            :param str name: 参数名称
            :return: 参数值
            :raises out_of_range: 无此参数
        """
    def get_base_assets_curve(self, dates: DatetimeList, ktype: str = 'DAY') -> list[float]:
        """
        get_profit_curve(self, dates[, ktype = Query.DAY])
        
            获取投入本值资产曲线（投入本钱）
        
            :param DatetimeList dates: 日期列表
            :param Query.KType ktype: K线类型，必须与日期列表匹配
            :rtype: PriceList
        """
    def get_borrow_cash_cost(self, arg0: Datetime, arg1: float) -> CostRecord:
        ...
    def get_borrow_stock_cost(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> CostRecord:
        ...
    def get_buy_cost(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> CostRecord:
        """
        get_buy_cost(self, datetime, stock, price, num)
        
            计算买入成本
        
            :param Datetime datetime: 交易时间
            :param Stock stock:       交易的证券
            :param float price:       买入价格
            :param float num:         买入数量
            :rtype: CostRecord
        """
    @typing.overload
    def get_funds(self, ktype: str = 'DAY') -> FundsRecord:
        ...
    @typing.overload
    def get_funds(self, datetime: Datetime, ktype: str = 'DAY') -> FundsRecord:
        """
        get_funds(self, [datetime, ktype = Query.DAY])
        
            获取指定时刻的资产市值详情
        
            :param Datetime datetime:  指定时刻
            :param Query.KType ktype: K线类型
            :rtype: FundsRecord
        """
    def get_funds_curve(self, dates: DatetimeList, ktype: str = 'DAY') -> list[float]:
        """
        get_funds_curve(self, dates[, ktype = Query.DAY])
        
            获取资产净值曲线
        
            :param DatetimeList dates: 日期列表，根据该日期列表获取其对应的资产净值曲线
            :param Query.KType ktype: K线类型，必须与日期列表匹配
            :return: 资产净值列表
            :rtype: PriceList
        """
    def get_funds_list(self, dates: DatetimeList, ktype: str = 'DAY') -> list[FundsRecord]:
        """
        get_funds_list(self, dates[, ktype = Query.DAY])
            
            获取指定日期列表的每日资产记录
            :param Datetime datetime:  指定时刻
            :param Query.KType ktype: K线类型
            :rtype: FundsList
        """
    def get_history_position_ext_info_list(self, ktype: str = 'DAY', trade_mode: int = 0) -> list[...]:
        """
        get_history_position_ext_info_list(self, ktype=Query.DAY, trade_mode=0) -> list[PositionExtInfo])
                  
            获取账户历史持仓扩展详情（已平仓记录）
         
            :param Query.KType ktype: k线类型
            :param int trade_mode: 交易模式，影响部分统计项: 0-收盘时交易, 1-下一开盘时交易
            :return: 持仓扩展详情列表
        """
    def get_history_position_list(self) -> PositionRecordList:
        """
        get_history_position_list(self)
        
            获取全部历史持仓记录，即已平仓记录
        
            :rtype: PositionRecordList
        """
    def get_hold_num(self, arg0: Datetime, arg1: Stock) -> float:
        """
        get_hold_num(self, datetime, stock)
        
                获取指定时刻指定证券的持有数量
                
                :param Datetime datetime: 指定时刻
                :param Stock stock: 指定的证券
                :rtype: int
        """
    def get_margin_rate(self, arg0: Datetime, arg1: Stock) -> float:
        ...
    def get_max_pull_back(self, date: Datetime = ..., ktype: str = 'DAY') -> float:
        """
        get_max_pull_back(self, date, ktype=Query.DAY) -> float
            
            获取指定时刻时账户的最大回撤百分比（负数）
        
            :param Datetime date: 指定日期（包含该时刻）
            :param Query.KType ktype: k线类型
            :return: 最大回撤百分比
        """
    def get_performance(self, datetime: Datetime = ..., ktype: str = 'DAY') -> ...:
        """
        get_performance(self[, datetime=Datetime.now(), ktype=Query.DAY]) -> Performance)
                
            获取账户指定时刻的账户表现
        
            :param Datetime datetime: 指定时刻
            :param Query.KType ktype: K线类型
            :return: 账户表现
        """
    def get_position(self, arg0: Datetime, arg1: Stock) -> PositionRecord:
        """
        get_position(self, date, stock)
        
            获取指定日期指定证券的持仓记录，如当前未持有该票，返回PositionRecord()
        
            :param Datetime date: 指定日期
            :param Stock stock: 指定的证券
            :rtype: PositionRecord
        """
    def get_position_ext_info_list(self, current_time: Datetime, ktype: str = 'DAY', trade_mode: int = 0) -> list[...]:
        """
        get_position_ext_info_list(self, current_time, ktype=Query.DAY, trade_mode=0) -> list[PositionExtInfo])
                  
            获取账户最后交易时刻之后指定时间的持仓详情（未平常记录）
         
            :param Datetime current_time: 当前时刻（需大于等于最后交易时刻）
            :param Query.KType ktype: k线类型
            :param int trade_mode: 交易模式，影响部分统计项: 0-收盘时交易, 1-下一开盘时交易
            :return: 持仓扩展详情列表
        """
    def get_position_list(self) -> PositionRecordList:
        """
        get_position_list(self)
        
            获取当前全部持仓记录
        
            :rtype: PositionRecordList
        """
    def get_profit_cum_change_curve(self, dates: DatetimeList, ktype: str = 'DAY') -> list[float]:
        """
        get_profit_cum_change_curve(self, dates[, ktype = Query.DAY])
        
            获取累积收益率曲线
        
            :param DatetimeList dates: 日期列表
            :param Query.KType ktype: K线类型，必须与日期列表匹配
            :rtype: PriceList
        """
    def get_profit_curve(self, dates: DatetimeList, ktype: str = 'DAY') -> list[float]:
        """
        get_profit_curve(self, dates[, ktype = Query.DAY])
        
            获取收益曲线，即扣除历次存入资金后的资产净值曲线
        
            :param DatetimeList dates: 日期列表，根据该日期列表获取其对应的收益曲线，应为递增顺序
            :param Query.KType ktype: K线类型，必须与日期列表匹配
            :return: 收益曲线
            :rtype: PriceList
        """
    def get_profit_percent_monthly(self, datetime: Datetime = ...) -> list[tuple[Datetime, float]]:
        """
        get_profit_percent_monthly(self, datetime=Datetime.now()) -> list[tuple[Datetime, double]])
        
            获取账户指定截止时刻的账户收益百分比（月度）
        
            :param Datetime datetime: 指定截止时刻
            :return: 账户收益百分比（月度）
        """
    def get_profit_percent_yearly(self, datetime: Datetime = ...) -> list[tuple[Datetime, float]]:
        """
        get_profit_percent_yearly(self, datetime=Datetime.now()) -> list[tuple[Datetime, double]])
        
            获取账户指定截止时刻的账户收益百分比（年度）
        
            :param Datetime datetime: 指定截止时刻
            :return: 账户收益百分比（年度）
        """
    def get_return_cash_cost(self, arg0: Datetime, arg1: Datetime, arg2: float) -> CostRecord:
        ...
    def get_return_stock_cost(self, arg0: Datetime, arg1: Datetime, arg2: Stock, arg3: float, arg4: float) -> CostRecord:
        ...
    def get_sell_cost(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> CostRecord:
        """
        get_sell_cost(self, datetime, stock, price, num)
        
            计算卖出成本
        
            :param Datetime datetime: 交易时间
            :param Stock stock:       交易的证券
            :param float price:       卖出价格
            :param float num:         卖出数量
            :rtype: CostRecord
        """
    def get_short_hold_num(self, arg0: Datetime, arg1: Stock) -> float:
        ...
    def get_short_stock_num(self) -> int:
        ...
    def get_stock_num(self) -> int:
        """
        get_stock_num(self)
        
            当前持有的证券种类数量，即当前持有几只股票（非各个股票的持仓数）
        
            :rtype: int
        """
    @typing.overload
    def get_trade_list(self) -> TradeRecordList:
        ...
    @typing.overload
    def get_trade_list(self, arg0: Datetime, arg1: Datetime) -> TradeRecordList:
        """
        get_trade_list(self[, start, end])
        
            获取交易记录，未指定参数时，获取全部交易记录
        
            :param Datetime start: 起始日期
            :param Datetime end: 结束日期
            :rtype: TradeRecordList
        """
    def have(self, arg0: Stock) -> bool:
        """
        have(self, stock)
        
            当前是否持有指定的证券
        
            :param Stock stock: 指定证券
            :rtype: bool
        """
    def have_param(self, arg0: str) -> bool:
        """
        是否存在指定参数
        """
    def reg_broker(self, arg0: OrderBrokerBase) -> None:
        """
        reg_broker(self, broker)
            
            注册订单代理。可执行多次该命令注册多个订单代理。
                
            :param OrderBrokerBase broker: 订单代理实例
        """
    def reset(self) -> None:
        """
        复位，清空交易、持仓记录
        """
    def return_cash(self, arg0: Datetime, arg1: float) -> bool:
        ...
    def return_stock(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float) -> bool:
        ...
    def sell(self, datetime: Datetime, stock: Stock, real_price: float, num: float = 1.7976931348623157e+308, stoploss: float = 0.0, goal_price: float = 0.0, plan_price: float = 0.0, part: SystemPart = ..., remark: str = '') -> TradeRecord:
        """
        sell(self, datetime, stock, realPrice[, number=constant.max_double, stoploss=0.0, goal_price=0.0, plan_price=0.0, part=System.INVALID, remark=""])
        
            卖出操作
        
            :param Datetime datetime: 卖出时间
            :param Stock stock:       卖出的证券
            :param float real_price:  实际卖出价格
            :param float num:         卖出数量，如果等于constant.max_double，表示全部卖出
            :param float stoploss:    新的止损价
            :param float goal_price:  新的目标价格
            :param float plan_price:  原计划卖出价格
            :param SystemPart part:   交易指示来源
            :param string remark:     交易备注
            :rtype: TradeRecord
        """
    def sell_short(self, arg0: Datetime, arg1: Stock, arg2: float, arg3: float, arg4: float, arg5: float, arg6: float, arg7: SystemPart, arg8: str) -> TradeRecord:
        ...
    def set_param(self, arg0: str, arg1: any) -> None:
        """
        set_param(self, name, value)
        
            设置参数
        
            :param str name: 参数名称
            :param value: 参数值
            :type value: int | bool | float | string | Query | KData | Stock | DatetimeList
            :raises logic_error: Unsupported type! 不支持的参数类型
        """
    def tocsv(self, arg0: str) -> None:
        """
        tocsv(self, path)
        
            以csv格式输出交易记录、未平仓记录、已平仓记录、资产净值曲线
        
            :param str path: 输出文件所在目录
        """
    def update_with_weight(self, arg0: Datetime) -> None:
        """
        update_with_weight(self, date)
        
              根据权息信息更新当前持仓及交易记录，必须按时间顺序被调用
        
              :param Datetime date: 当前时刻
        """
    @property
    def broker_last_datetime(self) -> Datetime:
        """
        实际开始订单代理操作的时刻。
                
            默认情况下，TradeManager会在执行买入/卖出操作时，调用订单代理执行代理的买入/卖出动作，但这样在实盘操作时会存在问题。因为系统在计算信号指示时，需要回溯历史数据才能得到最新的信号，这样TradeManager会在历史时刻就执行买入/卖出操作，此时如果订单代理本身没有对发出买入/卖出指令的时刻进行控制，会导致代理发送错误的指令。此时，需要指定在某一个时刻之后，才允许指定订单代理的买入/卖出操作。属性 brokeLastDatetime 即用于指定该时刻。
        """
    @broker_last_datetime.setter
    def broker_last_datetime(self, arg1: Datetime) -> None:
        ...
    @property
    def cost_func(self) -> TradeCostBase:
        """
        交易成本算法
        """
    @cost_func.setter
    def cost_func(self, arg1: TradeCostBase) -> None:
        ...
    @property
    def current_cash(self) -> float:
        """
        （只读）当前资金
        """
    @property
    def first_datetime(self) -> Datetime:
        """
        （只读）第一笔买入交易发生日期，如未发生交易返回 Datetime>()
        """
    @property
    def init_cash(self) -> float:
        """
        （只读）初始资金
        """
    @property
    def init_datetime(self) -> Datetime:
        """
        （只读）账户建立日期
        """
    @property
    def last_datetime(self) -> Datetime:
        """
        （只读）最后一笔交易日期，注意和交易类型无关，如未发生交易返回账户建立日期
        """
    @property
    def name(self) -> str:
        """
        名称
        """
    @name.setter
    def name(self, arg1: str) -> None:
        ...
    @property
    def precision(self) -> int:
        """
        （只读）价格精度，同公共参数“precision”
        """
class TradeRecord:
    """
    交易记录
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Stock, arg1: Datetime, arg2: BUSINESS, arg3: float, arg4: float, arg5: float, arg6: float, arg7: CostRecord, arg8: float, arg9: float, arg10: SystemPart) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    def is_null(self) -> bool:
        ...
    @property
    def business(self) -> BUSINESS:
        """
        交易类型（BUSINESS）
        """
    @business.setter
    def business(self, arg0: BUSINESS) -> None:
        ...
    @property
    def cash(self) -> float:
        """
        现金余额（float）
        """
    @cash.setter
    def cash(self, arg0: float) -> None:
        ...
    @property
    def cost(self) -> CostRecord:
        """
        交易成本
        """
    @cost.setter
    def cost(self, arg0: CostRecord) -> None:
        ...
    @property
    def datetime(self) -> Datetime:
        """
         交易时间（Datetime）
        """
    @datetime.setter
    def datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def goal_price(self) -> float:
        """
        目标价格（float），如果为0表示未限定目标
        """
    @goal_price.setter
    def goal_price(self, arg0: float) -> None:
        ...
    @property
    def number(self) -> float:
        """
        成交数量（float）
        """
    @number.setter
    def number(self, arg0: float) -> None:
        ...
    @property
    def part(self) -> SystemPart:
        """
        交易指示来源，区别是交易系统哪个部件发出的指示，参见： :py:class:`System.Part`
        """
    @part.setter
    def part(self, arg0: SystemPart) -> None:
        ...
    @property
    def plan_price(self) -> float:
        """
        计划交易价格（float）
        """
    @plan_price.setter
    def plan_price(self, arg0: float) -> None:
        ...
    @property
    def real_price(self) -> float:
        """
        实际交易价格（float）
        """
    @real_price.setter
    def real_price(self, arg0: float) -> None:
        ...
    @property
    def remark(self) -> str:
        """
        备注
        """
    @remark.setter
    def remark(self, arg0: str) -> None:
        ...
    @property
    def stock(self) -> Stock:
        """
        股票（Stock）
        """
    @stock.setter
    def stock(self, arg0: Stock) -> None:
        ...
    @property
    def stoploss(self) -> float:
        """
        止损价（float）
        """
    @stoploss.setter
    def stoploss(self, arg0: float) -> None:
        ...
class TradeRecordList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(t):
        """
        转化为pandas的DataFrame
        """
    @staticmethod
    def to_np(t_list):
        """
        转化为numpy结构数组
        """
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: TradeRecordList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> TradeRecordList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: TradeRecordList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: TradeRecordList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: TradeRecordList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: TradeRecordList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class TradeRequest:
    """
    交易请求记录。系统内部在实现延迟操作时登记的交易请求信息。暴露该结构的主要目的是用于
    在“delay”模式（延迟到下一个bar开盘时进行交易）的情况下，系统实际已知下一个Bar将要
    进行交易，此时可通过 System.getBuyTradeRequest() 、 System.getSellTradeRequest()
    来获知下一个BAR是否需要买入/卖出。主要用于提醒或打印下一个Bar需要进行操作。对于系统
    本身的运行没有影响。
    """
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __getstate__(self) -> tuple:
        ...
    def __init__(self) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def business(self) -> BUSINESS:
        """
        交易业务类型，参见：:py:class:`hikyuu.trade_manage.BUSINESS`
        """
    @business.setter
    def business(self, arg0: BUSINESS) -> None:
        ...
    @property
    def count(self) -> int:
        """
        因操作失败，连续延迟的次数
        """
    @count.setter
    def count(self, arg0: int) -> None:
        ...
    @property
    def datetime(self) -> Datetime:
        """
        发出交易请求的时刻
        """
    @datetime.setter
    def datetime(self, arg0: Datetime) -> None:
        ...
    @property
    def part(self) -> SystemPart:
        """
        发出交易请求的来源，参见：:py:class:`System.Part`
        """
    @part.setter
    def part(self, arg0: SystemPart) -> None:
        ...
    @property
    def stoploss(self) -> float:
        """
        发出交易请求时刻的止损价
        """
    @stoploss.setter
    def stoploss(self, arg0: float) -> None:
        ...
    @property
    def valid(self) -> bool:
        """
        该交易请求记录是否有效（True | False）
        """
    @valid.setter
    def valid(self, arg0: bool) -> None:
        ...
class TransList:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    @staticmethod
    def to_df(kdata):
        """
        转化为pandas的DataFrame
        """
    @staticmethod
    def to_np(data):
        """
        转化为numpy结构数组
        """
    def __bool__(self) -> bool:
        """
        Check whether the list is nonempty
        """
    def __contains__(self, x: ...) -> bool:
        """
        Return true the container contains ``x``
        """
    @typing.overload
    def __delitem__(self, arg0: int) -> None:
        """
        Delete the list elements at index ``i``
        """
    @typing.overload
    def __delitem__(self, arg0: slice) -> None:
        """
        Delete list elements using a slice object
        """
    def __eq__(self, arg0: TransList) -> bool:
        ...
    @typing.overload
    def __getitem__(self, s: slice) -> TransList:
        """
        Retrieve list elements using a slice object
        """
    @typing.overload
    def __getitem__(self, arg0: int) -> ...:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: TransList) -> None:
        """
        Copy constructor
        """
    @typing.overload
    def __init__(self, arg0: typing.Iterable) -> None:
        ...
    def __iter__(self) -> typing.Iterator[...]:
        ...
    def __len__(self) -> int:
        ...
    def __ne__(self, arg0: TransList) -> bool:
        ...
    def __repr__(self) -> str:
        """
        Return the canonical string representation of this list.
        """
    @typing.overload
    def __setitem__(self, arg0: int, arg1: ...) -> None:
        ...
    @typing.overload
    def __setitem__(self, arg0: slice, arg1: TransList) -> None:
        """
        Assign list elements using a slice object
        """
    def append(self, x: ...) -> None:
        """
        Add an item to the end of the list
        """
    def clear(self) -> None:
        """
        Clear the contents
        """
    def count(self, x: ...) -> int:
        """
        Return the number of times ``x`` appears in the list
        """
    @typing.overload
    def extend(self, L: TransList) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    @typing.overload
    def extend(self, L: typing.Iterable) -> None:
        """
        Extend the list by appending all the items in the given list
        """
    def insert(self, i: int, x: ...) -> None:
        """
        Insert an item at a given position.
        """
    @typing.overload
    def pop(self) -> ...:
        """
        Remove and return the last item
        """
    @typing.overload
    def pop(self, i: int) -> ...:
        """
        Remove and return the item at index ``i``
        """
    def remove(self, x: ...) -> None:
        """
        Remove the first item from the list whose value is x. It is an error if there is no such item.
        """
class TransRecord:
    __hash__: typing.ClassVar[None] = None
    @staticmethod
    def _pybind11_conduit_v1_(*args, **kwargs):
        ...
    def __eq__(self, arg0: TransRecord) -> bool:
        ...
    def __getstate__(self) -> tuple:
        ...
    @typing.overload
    def __init__(self) -> None:
        ...
    @typing.overload
    def __init__(self, arg0: Datetime, arg1: float, arg2: float, arg3: ...) -> None:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, arg0: tuple) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def date(self) -> Datetime:
        """
        时间
        """
    @date.setter
    def date(self, arg0: Datetime) -> None:
        ...
    @property
    def direct(self) -> ...:
        """
        买卖盘性质, 参见: TransRecord.DIRECT
        """
    @direct.setter
    def direct(self, arg0: ...) -> None:
        ...
    @property
    def price(self) -> float:
        """
        价格
        """
    @price.setter
    def price(self, arg0: float) -> None:
        ...
    @property
    def vol(self) -> float:
        """
        成交量
        """
    @vol.setter
    def vol(self, arg0: float) -> None:
        ...
@typing.overload
def ABS() -> Indicator:
    ...
@typing.overload
def ABS(arg0: float) -> Indicator:
    ...
@typing.overload
def ABS(arg0: Indicator) -> Indicator:
    """
    ABS([data])
    
        求绝对值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def ACOS() -> Indicator:
    ...
@typing.overload
def ACOS(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def ACOS(arg0: float) -> Indicator:
    """
    ACOS([data])
    
        反余弦值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def AD() -> Indicator:
    ...
@typing.overload
def AD(arg0: KData) -> Indicator:
    """
    AD(kdata)
    
       累积/派发线
    
       :param KData kdata: k线数据
       :rtype: Indicator
    """
def ADVANCE(query: Query = ..., market: str = 'SH', stk_type: int = 1, ignore_context: bool = False, fill_null: bool = True) -> Indicator:
    """
    ADVANCE([query=Query(-100), market='SH', stk_type='constant.STOCKTYPE_A'])
    
        上涨家数。当存在指定上下文且 ignore_context 为 false 时，将忽略 query, market, stk_type 参数。
    
        :param Query query: 查询条件
        :param str market: 所属市场，等于 "" 时，获取所有市场
        :param int stk_type: 证券类型, 大于 constant.STOCKTYPE_TMP 时，获取所有类型证券
        :param bool ignore_context: 是否忽略上下文。忽略时，强制使用 query, market, stk_type 参数。
        :para. bool fill_null: 缺失数据使用 nan 填充; 否则使用小于对应日期且最接近对应日期的数据
        :rtype: Indicator
    """
def AF_EqualWeight() -> AllocateFundsBase:
    """
    AF_EqualWeight()
        
        等权重资产分配，对选中的资产进行等比例分配
    """
def AF_FixedWeight(weight: float = 0.1) -> AllocateFundsBase:
    """
    AF_FixedWeight(weight)
        
        固定比例资产分配
    
        :param float weight:  指定的资产比例 [0, 1]
    """
def AF_FixedWeightList(weights: list[float]) -> AllocateFundsBase:
    """
    AF_FixedWeightList(weights)
        
        固定比例资产分配列表.
    
        :param float weights:  指定的资产比例列表
    """
def AF_MultiFactor() -> AllocateFundsBase:
    """
    AF_MultiFactor()
          
        创建 MultiFactor 评分权重的资产分配算法实例, 即直接以SE返回的评分作为权重。
    """
@typing.overload
def ALIGN(ref: DatetimeList, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def ALIGN(data: Indicator, ref: DatetimeList, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def ALIGN(data: Indicator, ref: Indicator, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def ALIGN(data: Indicator, ref: KData, fill_null: bool = True) -> Indicator:
    """
    ALIGN(data, ref):
    
        按指定的参考日期对齐
    
        :param Indicator data: 输入数据
        :param DatetimeList|Indicator|KData ref: 指定做为日期参考的 DatetimeList、Indicator 或 KData
        :param bool fill_null: 缺失数据使用 nan 填充; 否则使用小于对应日期且最接近对应日期的数据
        :retype: Indicator
    """
@typing.overload
def AMA(n: int = 10, fast_n: int = 2, slow_n: int = 30) -> Indicator:
    ...
@typing.overload
def AMA(n: IndParam, fast_n: IndParam, slow_n: IndParam) -> Indicator:
    ...
@typing.overload
def AMA(data: Indicator, n: IndParam, fast_n: IndParam, slow_n: IndParam) -> Indicator:
    ...
@typing.overload
def AMA(data: Indicator, n: Indicator, fast_n: Indicator, slow_n: Indicator) -> Indicator:
    ...
@typing.overload
def AMA(data: Indicator, n: int = 10, fast_n: int = 2, slow_n: int = 30) -> Indicator:
    """
    AMA([data, n=10, fast_n=2, slow_n=30])
    
        佩里.J 考夫曼（Perry J.Kaufman）自适应移动平均 [BOOK1]_
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 计算均值的周期窗口，必须为大于2的整数
        :param int|Indicator|IndParam fast_n: 对应快速周期N
        :param int|Indicator|IndParam slow_n: 对应慢速EMA线的N值
        :rtype: Indicator
    
        * result(0): AMA
        * result(1): ER
    """
@typing.overload
def ASIN() -> Indicator:
    ...
@typing.overload
def ASIN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def ASIN(arg0: float) -> Indicator:
    """
    ASIN([data])
    
        反正弦值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def ATAN() -> Indicator:
    ...
@typing.overload
def ATAN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def ATAN(arg0: float) -> Indicator:
    """
    ATAN([data])
    
        反正切值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def ATR(n: int = 14) -> Indicator:
    ...
@typing.overload
def ATR(kdata: KData, n: int = 14) -> Indicator:
    """
    ATR([kdata, n=14])
    
        平均真实波幅(Average True Range), 真实波动幅度 TR 的简单移动均值
    
        :param KData kdata 待计算的源数据
        :param int n: 计算均值的周期窗口，必须为大于1的整数
        :rtype: Indicator
    """
@typing.overload
def AVEDEV(data: Indicator, n: int = 22) -> Indicator:
    ...
@typing.overload
def AVEDEV(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def AVEDEV(data: Indicator, n: Indicator) -> Indicator:
    """
    AVEDEV(data[, n=22])
    
        平均绝对偏差，求X的N日平均绝对偏差
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def BACKSET(n: int = 2) -> Indicator:
    ...
@typing.overload
def BACKSET(n: IndParam) -> Indicator:
    ...
@typing.overload
def BACKSET(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def BACKSET(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def BACKSET(data: Indicator, n: int = 2) -> Indicator:
    """
    BACKSET([data, n=2])
    
        向前赋值将当前位置到若干周期前的数据设为1。
    
        用法：BACKSET(X,N),X非0,则将当前位置到N周期前的数值设为1。
    
        例如：BACKSET(CLOSE>OPEN,2)若收阳则将该周期及前一周期数值设为1,否则为0
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: N周期
        :rtype: Indicator
    """
@typing.overload
def BARSCOUNT() -> Indicator:
    ...
@typing.overload
def BARSCOUNT(arg0: Indicator) -> Indicator:
    """
    BARSCOUNT([data])
    
        有效值周期数, 求总的周期数。
    
        用法：BARSCOUNT(X)第一个有效数据到当前的天数。
    
        例如：BARSCOUNT(CLOSE)对于日线数据取得上市以来总交易日数，对于1分钟线取得当日交易分钟数。
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def BARSLAST() -> Indicator:
    ...
@typing.overload
def BARSLAST(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def BARSLAST(arg0: float) -> Indicator:
    """
    BARSLAST([data])
    
        上一次条件成立位置 上一次条件成立到当前的周期数。
    
        用法：BARSLAST(X): 上一次 X 不为 0 到现在的天数。
    
        例如：BARSLAST(CLOSE/REF(CLOSE,1)>=1.1) 表示上一个涨停板到当前的周期数
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def BARSLASTCOUNT() -> Indicator:
    ...
@typing.overload
def BARSLASTCOUNT(data: Indicator) -> Indicator:
    """
    BARSLASTCOUNT([data])
    
        用于统计连续满足条件的周期数
        BARSLASTCOUNT(X), 其中X为条件表达式。例如, BARSLASTCOUNT(CLOSE>OPEN)表示统计连续收阳的周期数
    
        :param Indicator data: 条件指标
        :rtype: Indicator
    """
@typing.overload
def BARSSINCE() -> Indicator:
    ...
@typing.overload
def BARSSINCE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def BARSSINCE(arg0: float) -> Indicator:
    """
    BARSSINCE([data])
    
        第一个条件成立位置到当前的周期数。
    
        用法：BARSSINCE(X):第一次X不为0到现在的天数。
    
        例如：BARSSINCE(HIGH>10)表示股价超过10元时到当前的周期数
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def BARSSINCEN(n: int) -> Indicator:
    ...
@typing.overload
def BARSSINCEN(cond: Indicator, n: int) -> Indicator:
    """
    BARSSINCEN(cond, n)
        
        N周期内第一个条件成立到当前的周期数
    
        用法：BARSSINCEN(X,N):N周期内第一次X不为0到现在的周期数,N为常量BARSSINCEN(X,N)
        例如：BARSSINCEN(HIGH>10,10)表示10个周期内股价超过10元时到当前的周期数
    
        :param Indicator cond: 条件
        :param int|Indicator n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def BETWEEN(arg0: Indicator, arg1: Indicator, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: Indicator, arg1: Indicator, arg2: float) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: Indicator, arg1: float, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: Indicator, arg1: float, arg2: float) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: float, arg1: Indicator, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: float, arg1: Indicator, arg2: float) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: float, arg1: float, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def BETWEEN(arg0: float, arg1: float, arg2: float) -> Indicator:
    """
    BETWEEN(a, b, c)
    
        介于(介于两个数之间)
    
        用法：BETWEEN(A,B,C)表示A处于B和C之间时返回1，否则返回0
    
        例如：BETWEEN(CLOSE,MA(CLOSE,10),MA(CLOSE,5))表示收盘价介于5日均线和10日均线之间
    
        :param Indicator a: A
        :param Indicator b: B
        :param Indicator c: C
        :rtype: Indicator
    """
@typing.overload
def BLOCKSETNUM(block: Block) -> Indicator:
    ...
@typing.overload
def BLOCKSETNUM(block: Block, query: Query) -> Indicator:
    """
    BLOCKSETNUM(block, query)
        
        横向统计（返回板块股个数）
    
        :param Block block: 待统计的板块
        :param Query query: 统计范围
    """
@typing.overload
def BLOCKSETNUM(stks: typing.Sequence) -> Indicator:
    ...
@typing.overload
def BLOCKSETNUM(stks: typing.Sequence, query: Query) -> Indicator:
    """
    BLOCKSETNUM(block, query)
        
        横向统计（返回板块股个数）
    
        :param Sequence stks: stock list
        :param Query query: 统计范围
    """
@typing.overload
def CEILING() -> Indicator:
    ...
@typing.overload
def CEILING(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def CEILING(arg0: float) -> Indicator:
    """
    CEILING([data])
    
        向上舍入(向数值增大方向舍入)取整
       
        用法：CEILING(A)返回沿A数值增大方向最接近的整数
       
        例如：CEILING(12.3)求得13；CEILING(-3.5)求得-3
       
        :param data: 输入数据
        :rtype: Indicator
    """
def CN_Bool(arg0: Indicator) -> ConditionBase:
    """
    CN_Bool(ind)
    
        布尔信号指标系统有效条件, 指标中相应位置>0则代表系统有效，否则无效
    
        :param Indicator ind: bool型指标，输入为 KData
        :return: 系统有效条件实例
        :rtype: ConditionBase
    """
def CN_OPLine(arg0: Indicator) -> ConditionBase:
    """
    CN_OPLine(ind)
    
        固定使用股票最小交易量进行交易，计算权益曲线的ind值，当权益曲线高于ind时，系统有效，否则无效。
    
        :param Indicator ind: Indicator实例
        :return: 系统有效条件实例
        :rtype: ConditionBase
    """
@typing.overload
def CONTEXT(fill_null: bool = False, use_self_ktype: bool = False, use_self_recover_type: bool = False) -> Indicator:
    ...
@typing.overload
def CONTEXT(ind: Indicator, fill_null: bool = False, use_self_ktype: bool = False, use_self_recover_type: bool = False) -> Indicator:
    """
    CONTEXT(ind)
        
        独立上下文。使用 ind 自带的上下文。当指定新的上下文时，不会改变已有的上下文。
        例如：ind = CLOSE(k1), 当指定新的上下文 ind = ind(k2) 时，使用的是 k2 的收盘价。如想仍使用 k1 收盘价，
        则需使用 ind = CONTEXT(CLOSE(k1)), 此时 ind(k2) 将仍旧使用 k1 的收盘价。
        
        :param Indicator ind: 指标对象
        :param bool fill_null: 日期对齐时，缺失日期对应填充空值，否则使用前值填充。
        :param bool use_self_ktype: 公式计算时使用自身独立上下文中的KTYPE
        :param bool use_self_recover_type: 公式计算时使用自身独立上下文中的RECOVER_TYPE
        :rtype: Indicator
    """
def CONTEXT_K(arg0: Indicator) -> KData:
    """
    CONTEXT_K(ind)
    
        获取指标上下文。Indicator::getContext()方法获取的是当前的上下文，但对于 CONTEXT 独立上下文指标无法获取其指定的独立上下文，需用此方法获取
    
        :param Indicator ind: 指标对象
        :rtype: KData
    """
@typing.overload
def CORR(ref_ind: Indicator, n: int = 10, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def CORR(ind: Indicator, ref_ind: Indicator, n: int = 10, fill_null: bool = True) -> Indicator:
    """
    CORR(ind, ref_ind[, n=10, fill_null=True])
    
        计算 ind 和 ref_ind 的相关系数。返回中存在两个结果，第一个为相关系数，第二个为协方差。
        与 CORR(ref_ind, n)(ind) 等效。
    
        :param Indicator ind: 指标1
        :param Indicator ref_ind: 指标2
        :param int n: 按指定 n 的长度计算两个 ind 直接数据相关系数。如果为0，使用输入的ind长度。
        :param bool fill_null: 日期对齐时缺失日期填充nan值
        :rtype: Indicator
    """
@typing.overload
def COS() -> Indicator:
    ...
@typing.overload
def COS(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def COS(arg0: float) -> Indicator:
    """
    COS([data])
    
        余弦值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def COST(x: float = 10.0) -> Indicator:
    ...
@typing.overload
def COST(k: KData, x: float = 10.0) -> Indicator:
    """
    COST(k[, x=10.0])
    
        成本分布。该函数仅对日线分析周期有效，对不能存在流通盘权息数据的指数、ETF等无效。
        用法：COST(k, X) 表示X%获利盘的价格是多少
        例如：COST(k, 10),表示10%获利盘的价格是多少，即有10%的持仓量在该价格以下，其余90%在该价格以上，为套牢盘
    
        :param KData k: 关联的K线数据
        :param float x: x%获利价格, 0~100
        :rtype: Indicator
    """
@typing.overload
def COUNT(n: int = 20) -> Indicator:
    ...
@typing.overload
def COUNT(n: IndParam) -> Indicator:
    ...
@typing.overload
def COUNT(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def COUNT(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def COUNT(data: Indicator, n: int = 20) -> Indicator:
    """
    COUNT([data, n=20])
    
        统计满足条件的周期数。
    
        用法：COUNT(X,N),统计N周期中满足X条件的周期数,若N=0则从第一个有效值开始。
    
        例如：COUNT(CLOSE>OPEN,20)表示统计20周期内收阳的周期数
    
        :param Indicator data: 条件
        :param int|Indicator|IndParam n: 周期
        :rtype: Indicator
    """
@typing.overload
def CROSS(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def CROSS(arg0: Indicator, arg1: float) -> Indicator:
    ...
@typing.overload
def CROSS(arg0: float, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def CROSS(arg0: float, arg1: float) -> Indicator:
    """
    CROSS(x, y)
    
        交叉函数
    
        :param x: 变量或常量，判断交叉的第一条线
        :param y: 变量或常量，判断交叉的第二条线
        :rtype: Indicator
    """
@typing.overload
def CVAL(value: float = 0.0, discard: int = 0) -> Indicator:
    ...
@typing.overload
def CVAL(data: Indicator, value: float = 0.0, discard: int = 0) -> Indicator:
    """
    CVAL([data, value=0.0, discard=0])
    
        data 为 Indicator 实例，创建和 data 等长的常量指标，其值和为value，抛弃长度discard和data一样
    
        :param Indicator data: Indicator实例
        :param float value: 常数值
        :param int discard: 抛弃数量
        :rtype: Indicator
    """
@typing.overload
def CYCLE(adjust_cycle: int = 1, adjust_mode: str = 'query', delay_to_trading_day: bool = True) -> Indicator:
    ...
@typing.overload
def CYCLE(kdata: KData, adjust_cycle: int = 1, adjust_mode: str = 'query', delay_to_trading_day: bool = True) -> Indicator:
    """
    CYCLE(kdata, [adjust_cycle=1], [adjust_mode='query'], [delay_to_trading_day=True])
              
        PF调仓周期指标，主要用于PF调仓日验证，及作为SG
    
        :param KData kdata: K线数据
        :param int adjust_cycle: 调整周期
        :param string adjust_mode: 调整方式
        :param bool delay_to_trading_day: 调整周期是否延至交易日
        :rtype: Indicator
    """
@typing.overload
def C_AMO(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_AMO() -> Indicator:
    """
    AMO([data])
    
        获取成交金额，包装KData的成交金额成Indicator
        
        :param data: 输入数据（KData 或 Indicator）
        :rtype: Indicator
    """
@typing.overload
def C_CLOSE(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_CLOSE() -> Indicator:
    """
    CLOSE([data])
    
        获取收盘价，包装KData的收盘价成Indicator
    
        :param data: 输入数据（KData 或 Indicator）
        :rtype: Indicator
    """
@typing.overload
def C_HIGH(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_HIGH() -> Indicator:
    """
    HIGH([data])
    
        获取最高价，包装KData的最高价成Indicator
    
        :param data: 输入数据（KData 或 Indicator） 
        :rtype: Indicator
    """
@typing.overload
def C_KDATA(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_KDATA() -> Indicator:
    """
    KDATA([data])
    
        包装KData成Indicator，用于其他指标计算
    
        :param data: KData 或 具有6个返回结果的Indicator（如KDATA生成的Indicator）
        :rtype: Indicator
    """
@typing.overload
def C_LOW(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_LOW() -> Indicator:
    """
    LOW([data])
    
        获取最低价，包装KData的最低价成Indicator
    
        :param data: 输入数据（KData 或 Indicator） 
        :rtype: Indicator
    """
@typing.overload
def C_OPEN(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_OPEN() -> Indicator:
    """
    OPEN([data])
    
        获取开盘价，包装KData的开盘价成Indicator
    
        :param data: 输入数据（KData 或 Indicator） 
        :rtype: Indicator
    """
@typing.overload
def C_VOL(arg0: KData) -> Indicator:
    ...
@typing.overload
def C_VOL() -> Indicator:
    """
    VOL([data])
    
        获取成交量，包装KData的成交量成Indicator
    
        :param data: 输入数据（KData 或 Indicator）
        :rtype: Indicator
    """
@typing.overload
def DATE() -> Indicator:
    ...
@typing.overload
def DATE(arg0: KData) -> Indicator:
    """
    DATE([data])
    
        取得该周期从1900以来的年月日。用法: DATE 例如函数返回1000101，表示2000年1月1日。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def DAY() -> Indicator:
    ...
@typing.overload
def DAY(arg0: KData) -> Indicator:
    """
    DAY([data])
    
        取得该周期的日期。用法: DAY 函数返回有效值范围为(1-31)。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
def DECLINE(query: Query = ..., market: str = 'SH', stk_type: int = 1, ignore_context: bool = False, fill_null: bool = True) -> Indicator:
    """
    DECLINE([query=Query(-100), market='SH', stk_type='constant.STOCKTYPE_A'])
    
        下跌家数。当存在指定上下文且 ignore_context 为 false 时，将忽略 query, market, stk_type 参数。
    
        :param Query query: 查询条件
        :param str market: 所属市场，等于 "" 时，获取所有市场
        :param int stk_type: 证券类型, 大于 constant.STOCKTYPE_TMP 时，获取所有类型证券
        :param bool ignore_context: 是否忽略上下文。忽略时，强制使用 query, market, stk_type 参数。
        :param bool fill_null: 缺失数据使用 nan 填充; 否则使用小于对应日期且最接近对应日期的数据
        :rtype: Indicator
    """
@typing.overload
def DEVSQ(n: int = 10) -> Indicator:
    ...
@typing.overload
def DEVSQ(n: IndParam) -> Indicator:
    ...
@typing.overload
def DEVSQ(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def DEVSQ(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def DEVSQ(data: Indicator, n: int = 10) -> Indicator:
    """
    DEVSQ([data, n=10])
    
        数据偏差平方和，求X的N日数据偏差平方和
    
        :param Indicator data: 输入数据
        :param int|Indicator n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def DIFF() -> Indicator:
    ...
@typing.overload
def DIFF(arg0: Indicator) -> Indicator:
    """
    DIFF([data])
    
        差分指标，即data[i] - data[i-1]
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def DISCARD(discard: int) -> Indicator:
    ...
@typing.overload
def DISCARD(ind: Indicator, discard: int) -> Indicator:
    """
    DISCARD(data, discard)
        
        以指标公式的方式设置指标结果的丢弃数据量。
    
        :param Indicator data: 指标
        :param int discard: 丢弃数据量
        :rtype: Indicator
    """
def DMA(x: Indicator, a: Indicator, fill_null: bool = True) -> Indicator:
    """
    DMA(ind, a[, fill_null=True])
    
        动态移动平均
    
        用法：DMA(X,A),求X的动态移动平均。
    
        算法：若Y=DMA(X,A) 则 Y=A*X+(1-A)*Y',其中Y'表示上一周期Y值。
    
        例如：DMA(CLOSE,VOL/CAPITAL)表示求以换手率作平滑因子的平均价
    
        :param Indicator ind: 输入数据
        :param Indicator a: 动态系数
        :param bool fill_null: 日期对齐时缺失数据填充 nan 值。
        :rtype: Indicator
    """
@typing.overload
def DOWNNDAY(data: Indicator, n: int = 3) -> Indicator:
    ...
@typing.overload
def DOWNNDAY(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def DOWNNDAY(data: Indicator, n: Indicator) -> Indicator:
    """
    DOWNNDAY(data[, n=3])
    
        连跌周期数, DOWNNDAY(CLOSE,M)表示连涨M个周期
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def DROPNA() -> Indicator:
    ...
@typing.overload
def DROPNA(arg0: Indicator) -> Indicator:
    """
    DROPNA([data])
    
        删除 nan 值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
def Days(arg0: int) -> TimeDelta:
    """
    Days(days)
    
          TimeDelta 快捷创建函数
    
          :param int days: 天数 [-99999999, 99999999]
          :rtype: TimeDelta
    """
@typing.overload
def EMA(n: int = 22) -> Indicator:
    ...
@typing.overload
def EMA(n: IndParam) -> Indicator:
    ...
@typing.overload
def EMA(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def EMA(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def EMA(data: Indicator, n: int = 22) -> Indicator:
    """
    EMA([data, n=22])
    
        指数移动平均线(Exponential Moving Average)
    
        :param data: 输入数据
        :param int|Indicator|IndParam n n: 计算均值的周期窗口，必须为大于0的整数 
        :rtype: Indicator
    """
@typing.overload
def EVERY(n: int = 20) -> Indicator:
    ...
@typing.overload
def EVERY(n: IndParam) -> Indicator:
    ...
@typing.overload
def EVERY(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def EVERY(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def EVERY(data: Indicator, n: int = 20) -> Indicator:
    """
    EVERY([data, n=20])
    
        一直存在
    
        用法：EVERY (X,N) 表示条件X在N周期一直存在
    
        例如：EVERY(CLOSE>OPEN,10) 表示前10日内一直是阳线
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: 计算均值的周期窗口，必须为大于0的整数 
        :rtype: Indicator
    """
def EV_Bool(ind: Indicator, market: str = 'SH') -> EnvironmentBase:
    """
    EV_Bool(ind, market='SH')
    
        布尔信号指标市场环境
    
        :param Indicator ind: bool类型的指标，指标中相应位置>0则代表市场有效，否则无效
        :param str market: 指定的市场，用于获取相应的交易日历
    """
def EV_TwoLine(fast: Indicator, slow: Indicator, market: str = 'SH') -> EnvironmentBase:
    """
    EV_TwoLine(fast, slow[, market = 'SH'])
    
        快慢线判断策略，市场指数的快线大于慢线时，市场有效，否则无效。
    
        :param Indicator fast: 快线指标
        :param Indicator slow: 慢线指标
        :param string market: 市场名称
    """
@typing.overload
def EXIST(n: int = 20) -> Indicator:
    ...
@typing.overload
def EXIST(n: IndParam) -> Indicator:
    ...
@typing.overload
def EXIST(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def EXIST(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def EXIST(data: Indicator, n: int = 20) -> Indicator:
    """
    EXIST([data, n=20])
    
        存在, EXIST(X,N) 表示条件X在N周期有存在
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: 计算均值的周期窗口，必须为大于0的整数 
        :rtype: Indicator
    """
@typing.overload
def EXP() -> Indicator:
    ...
@typing.overload
def EXP(arg0: float) -> Indicator:
    ...
@typing.overload
def EXP(arg0: Indicator) -> Indicator:
    """
    EXP([data])
    
        EXP(X)为e的X次幂
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def FILTER(n: int = 5) -> Indicator:
    ...
@typing.overload
def FILTER(n: IndParam) -> Indicator:
    ...
@typing.overload
def FILTER(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def FILTER(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def FILTER(data: Indicator, n: int = 5) -> Indicator:
    """
    FILTER([data, n=5])
    
        信号过滤, 过滤连续出现的信号。
    
        用法：FILTER(X,N): X 满足条件后，删除其后 N 周期内的数据置为 0。
    
        例如：FILTER(CLOSE>OPEN,5) 查找阳线，5 天内再次出现的阳线不被记录在内。
    
        :param Indicator data: 输入数据
        :param int|Indicaot|IndParam n: 过滤周期
        :rtype: Indicator
    """
@typing.overload
def FINANCE(ix: int) -> Indicator:
    ...
@typing.overload
def FINANCE(name: str) -> Indicator:
    ...
@typing.overload
def FINANCE(kdata: KData, ix: int) -> Indicator:
    ...
@typing.overload
def FINANCE(kdata: KData, name: str) -> Indicator:
    """
    FINANCE([kdata, ix, name])
    
        获取历史财务信息。（可通过 StockManager.get_history_finance_all_fields 查询相应的历史财务字段信息）
    
        ix, name 使用时，为二选一。即要不使用 ix，要不就使用 name 进行获取。
    
        :param KData kdata: K线数据
        :param int ix: 历史财务信息字段索引
        :param int name: 历史财务信息字段名称
    """
@typing.overload
def FLOOR() -> Indicator:
    ...
@typing.overload
def FLOOR(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def FLOOR(arg0: float) -> Indicator:
    """
    FLOOR([data])
    
        向下舍入(向数值减小方向舍入)取整
    
        用法：FLOOR(A)返回沿A数值减小方向最接近的整数
    
        例如：FLOOR(12.3)求得12
    
        :param data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def HHV(n: int = 20) -> Indicator:
    ...
@typing.overload
def HHV(n: IndParam) -> Indicator:
    ...
@typing.overload
def HHV(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def HHV(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def HHV(data: Indicator, n: int = 20) -> Indicator:
    """
    HHV([data, n=20])
    
        N日内最高价，N=0则从第一个有效值开始。
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: N日时间窗口
        :rtype: Indicator
    """
@typing.overload
def HHVBARS(n: int = 20) -> Indicator:
    ...
@typing.overload
def HHVBARS(n: IndParam) -> Indicator:
    ...
@typing.overload
def HHVBARS(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def HHVBARS(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def HHVBARS(data: Indicator, n: int = 20) -> Indicator:
    """
    HHVBARS([data, n=20])
    
        上一高点位置 求上一高点到当前的周期数。
    
        用法：HHVBARS(X,N):求N周期内X最高值到当前周期数N=0表示从第一个有效值开始统计
    
        例如：HHVBARS(HIGH,0)求得历史新高到到当前的周期数
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: N日时间窗口
        :rtype: Indicator
    """
@typing.overload
def HOUR() -> Indicator:
    ...
@typing.overload
def HOUR(arg0: KData) -> Indicator:
    """
    HOUR([data])
    
        取得该周期的小时数。用法：HOUR 函数返回有效值范围为(0-23)，对于日线及更长的分析周期值为0。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def HSL() -> Indicator:
    ...
@typing.overload
def HSL(arg0: KData) -> Indicator:
    """
    HSL(kdata)
    
        获取换手率, 乘以 100 才是百分比，等于 VOL(k) / CAPITAL(k) * 0.01
    
        :param KData kdata: k线数据
        :rtype: Indicator
    """
def Hours(arg0: int) -> TimeDelta:
    """
    Hours(hours)
    
          TimeDelta 快捷创建函数
    
          :param int hours: 小时数
          :rtype: TimeDelta
    """
def IC(ind: Indicator, stks: typing.Any, query: Query, ref_stk: Stock, n: int = 1, spearman: bool = True) -> Indicator:
    """
    IC(ind, stks, query, ref_stk[, n=1])
    
        计算指定的因子相对于参考证券的 IC （实际为 RankIC）
        
        :param Indicator ind: 输入因子
        :param sequence(stock)|Block stks 证券组合
        :param Query query: 查询条件
        :param Stock ref_stk: 参照证券，通常使用 sh000300 沪深300
        :param int n: 时间窗口
        :param bool spearman: 使用 spearman 相关系数，否则为 pearson
    """
def ICIR(ind: Indicator, stks: typing.Any, query: Query, ref_stk: Stock, n: int = 1, rolling_n: int = 120, spearman: bool = True) -> Indicator:
    """
    ICIR(ind, stks, query, ref_stk[, n=1, rolling_n=120])
    
        计算 IC 因子 IR = IC的多周期均值/IC的标准方差
    
        :param Indicator ind: 输入因子
        :param sequence(stock)|Block stks 证券组合
        :param Query query: 查询条件
        :param Stock ref_stk: 参照证券，通常使用 sh000300 沪深300
        :param int n: 计算IC时对应的 n 日收益率
        :param int rolling_n: 滚动周期
        :param bool spearman: 使用 spearman 相关系数，否则为 pearson
    """
@typing.overload
def IF(arg0: Indicator, arg1: Indicator, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def IF(arg0: Indicator, arg1: float, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def IF(arg0: Indicator, arg1: Indicator, arg2: float) -> Indicator:
    ...
@typing.overload
def IF(arg0: Indicator, arg1: float, arg2: float) -> Indicator:
    """
    IF(x, a, b)
    
        条件函数, 根据条件求不同的值。
    
        用法：IF(X,A,B)若X不为0则返回A,否则返回B
    
        例如：IF(CLOSE>OPEN,HIGH,LOW)表示该周期收阳则返回最高值,否则返回最低值
    
        :param Indicator x: 条件指标
        :param Indicator a: 待选指标 a
        :param Indicator b: 待选指标 b
        :rtype: Indicator
    """
@typing.overload
def INBLOCK(category: str, name: str) -> Indicator:
    ...
@typing.overload
def INBLOCK(data: KData, category: str, name: str) -> Indicator:
    """
    INBLOCK(data, category, name)        
    
        当前上下文证券是否在指定的板块中。
    
        :param KData data: 指定的K线数据(上下文)
        :param string category: 板块类别
        :param string name: 板块名称
        :rtype: Indicator
    """
@typing.overload
def INDEXA(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXA(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXA([kdata])
        
        返回对应的大盘成交金额,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INDEXADV() -> Indicator:
    ...
@typing.overload
def INDEXADV(arg0: Query) -> Indicator:
    """
    INDEXADV([query])
        
        通达信 880005 大盘上涨家数, 可能无法盘中更新!
    """
@typing.overload
def INDEXC(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXC(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXC([kdata])
        
        返回对应的大盘收盘价,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INDEXDEC() -> Indicator:
    ...
@typing.overload
def INDEXDEC(arg0: Query) -> Indicator:
    """
    INDEXDEC([query])
        
        通达信 880005 大盘下跌家数, 可能无法盘中更新!
    """
@typing.overload
def INDEXH(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXH(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXH([kdata])
        
        返回对应的大盘最高价,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INDEXL(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXL(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXL([kdata])
        
        返回对应的大盘最低价,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INDEXO(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXO(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXO([kdata])
        
        返回对应的大盘开盘价,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INDEXV(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INDEXV(kdata: KData, fill_null: bool = True) -> Indicator:
    """
    INDEXV([kdata])
        
        返回对应的大盘成交量,分别是上证指数,深证成指,科创50,创业板指
    """
@typing.overload
def INSUM(block: Block, ind: Indicator, mode: int, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INSUM(block: Block, query: Query, ind: Indicator, mode: int, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INSUM(stks: typing.Sequence, ind: Indicator, mode: int, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def INSUM(stks: typing.Sequence, query: Query, ind: Indicator, mode: int, fill_null: bool = True) -> Indicator:
    """
    INSUM(stks, query, ind, mode[, fill_null=True])
    
        返回板块各成分该指标相应输出按计算类型得到的计算值.计算类型:0-累加,1-平均数,2-最大值,3-最小值,4-降序排名,5-升序排名.
    
        注意: INSUM使用模式4/5时相当于RANK功能, 但不适合在MF中使用, 在 MF 中使用时计算量为 N x N 级别, 计算缓慢。如果希望在 MF 中使用，建议直接使用 RANK[VIP] 指标。
    
        :param Sequence stks: stock list
        :param Query query: 指定范围
        :param Indicator ind: 指定指标
        :param int mode: 计算类型:0-累加,1-平均数,2-最大值,3-最小值,4-降序排名(指标值最高的排名为1), 5-升序排名(指标值越高排名值越高)
        :param bool fill_null: 日期对齐时缺失数据填充 nan 值。
        :rtype: Indicator
    """
@typing.overload
def INTPART() -> Indicator:
    ...
@typing.overload
def INTPART(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def INTPART(arg0: float) -> Indicator:
    """
    INTPART([data])
    
        取整(绝对值减小取整，即取得数据的整数部分)
    
        :param data: 输入数据
        :rtype: Indicator
    """
def IR(p: Indicator, b: Indicator, n: int = 100) -> Indicator:
    """
    IR(p, b[, n])
    
        信息比率（Information Ratio，IR）
    
        公式: (P-B) / TE
        P: 组合收益率
        B: 比较基准收益率
        TE: 投资周期中每天的 p 和 b 之间的标准差
        实际使用时，P 一般为 TM 的资产曲线，B 为沪深 3000 收盘价，如:
        ref_k = sm["sh000300"].get_kdata(query)
        funds = my_tm.get_funds_curve(ref_k.get_datetime.list())
        ir = IR(PRICELIST(funds), ref_k.close, 0)
    
        :param Indicator p:
        :param Indicator b:
        :param int n: 时间窗口，如果只想使用最后的值，可以使用 0, 或 len(p),len(b) 指定
    """
@typing.overload
def ISINF() -> Indicator:
    ...
@typing.overload
def ISINF(ind: Indicator) -> Indicator:
    """
    ISINF(ind)
    
        判断指标是否为正无穷大 (+inf) 值，若为 +inf 值, 则返回1, 否则返回0。如判断负无穷大, 使用 ISINFA。
    
        :param Indicator ind: 指定指标
        :rtype: Indicator
    """
@typing.overload
def ISINFA() -> Indicator:
    ...
@typing.overload
def ISINFA(ind: Indicator) -> Indicator:
    """
    ISINFA(ind)
    
        判断指标是否为负无穷大 (-inf) 值，若为 -inf 值, 则返回1, 否则返回0。如判断正无穷大, 使用 ISINF。
    
        :param Indicator ind: 指定指标
        :rtype: Indicator
    """
@typing.overload
def ISLASTBAR() -> Indicator:
    ...
@typing.overload
def ISLASTBAR(data: KData) -> Indicator:
    ...
@typing.overload
def ISLASTBAR(data: Indicator) -> Indicator:
    """
    ISLASTBAR(ind)
    
        判断当前数据是否为最后一个数据，若为最后一个数据，则返回1，否则返回0.
    
        :param Indicator|KData data: 指定指标
        :rtype: Indicator
    """
@typing.overload
def ISNA(ignore_discard: bool = False) -> Indicator:
    ...
@typing.overload
def ISNA(ind: Indicator, ignore_discard: bool = False) -> Indicator:
    """
    ISNA(ind[, ignore_discard=False])
    
        判断指标是否为 nan 值，若为 nan 值, 则返回1, 否则返回0.
    
        :param Indicator ind: 指定指标
        :param bool ignore_discard: 忽略指标丢弃数据
    """
@typing.overload
def JUMPDOWN() -> Indicator:
    ...
@typing.overload
def JUMPDOWN(arg0: Indicator) -> Indicator:
    """
    JUMPDOWN([ind])
        
        边缘跳变，从大于0.0，跳变到 <= 0.0
    
        :param Indicator ind: 指标
        :rtype: Indicator
    """
@typing.overload
def JUMPUP() -> Indicator:
    ...
@typing.overload
def JUMPUP(arg0: Indicator) -> Indicator:
    """
    JUMPUP([ind])
        
        边缘跳变，从小于等于0.0，跳变到 > 0.0
        
        :param Indicator ind: 指标
        :rtype: Indicator
    """
@typing.overload
def KALMAN(q: float = 0.01, r: float = 0.1) -> Indicator:
    ...
@typing.overload
def KALMAN(ind: Indicator, q: float = 0.01, r: float = 0.1) -> Indicator:
    """
    KALMAN(ind, [q=0.01], [r=0.1])
    
        Kalman滤波器, 用于平滑指标, 可设置平滑系数q和r, 默认q=0.01, r=0.1
    
        :param Indicator ind: 指标
        :param float q: 平滑系数
        :param float r: 噪声系数
        :rtype: Indicator
    """
@typing.overload
def KDATA_PART(data: KData, kpart: str) -> Indicator:
    ...
@typing.overload
def KDATA_PART(kpart: str) -> Indicator:
    """
    KDATA_PART([data, kpart])
    
        根据字符串选择返回指标KDATA/OPEN/HIGH/LOW/CLOSE/AMO/VOL，如:KDATA_PART("CLOSE")等同于CLOSE()
    
        :param data: 输入数据（KData 或 Indicator） 
        :param string kpart: KDATA|OPEN|HIGH|LOW|CLOSE|AMO|VOL
        :rtype: Indicator
    """
@typing.overload
def LAST(m: int = 10, n: int = 5) -> Indicator:
    ...
@typing.overload
def LAST(m: int, n: IndParam) -> Indicator:
    ...
@typing.overload
def LAST(m: IndParam, n: int = 5) -> Indicator:
    ...
@typing.overload
def LAST(m: IndParam, n: IndParam) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: int = 10, n: int = 5) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: int, n: IndParam) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: IndParam, n: int = 5) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: IndParam, n: IndParam) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: int, n: Indicator) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: Indicator, n: int = 5) -> Indicator:
    ...
@typing.overload
def LAST(data: Indicator, m: Indicator, n: Indicator) -> Indicator:
    """
    LAST([data, m=10, n=5])
    
        区间存在。
    
        用法：LAST (X,M,N) 表示条件 X 在前 M 周期到前 N 周期存在。
    
        例如：LAST(CLOSE>OPEN,10,5) 表示从前10日到前5日内一直阳线。
    
        :param data: 输入数据
        :param int|Indicator|IndParam m: m周期
        :param int|Indicator|IndParam n: n周期
        :rtype: Indicator
    """
@typing.overload
def LASTVALUE(ignore_discard: bool = False) -> Indicator:
    ...
@typing.overload
def LASTVALUE(ind: Indicator, ignore_discard: bool = False) -> Indicator:
    """
    LASTVALUE(ind, [ignore_discard=False])
    
        等同于通达信CONST指标。取输入指标最后值为常数, 即结果中所有值均为输入指标的最后值, 谨慎使用。含未来函数, 谨慎使用。
    
        :param Indicator ind: 指标
        :param bool ignore_discard: 忽略指标丢弃数据
        :rtype: Indicator
    """
@typing.overload
def LIUTONGPAN() -> Indicator:
    ...
@typing.overload
def LIUTONGPAN(arg0: KData) -> Indicator:
    """
    LIUTONGPAN(kdata)
    
       获取流通盘（单位：万股），同 CAPITAL
    
       :param KData kdata: k线数据
       :rtype: Indicator
    """
@typing.overload
def LLV(n: int = 20) -> Indicator:
    ...
@typing.overload
def LLV(n: IndParam) -> Indicator:
    ...
@typing.overload
def LLV(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def LLV(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def LLV(data: Indicator, n: int = 20) -> Indicator:
    """
    LLV([data, n=20])
    
        N日内最低价，N=0则从第一个有效值开始。
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: N日时间窗口
        :rtype: Indicator
    """
@typing.overload
def LLVBARS(n: int = 20) -> Indicator:
    ...
@typing.overload
def LLVBARS(n: IndParam) -> Indicator:
    ...
@typing.overload
def LLVBARS(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def LLVBARS(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def LLVBARS(data: Indicator, n: int = 20) -> Indicator:
    """
    LLVBARS([data, n=20])
    
        上一低点位置 求上一低点到当前的周期数。
    
        用法：LLVBARS(X,N):求N周期内X最低值到当前周期数N=0表示从第一个有效值开始统计
    
        例如：LLVBARS(HIGH,20)求得20日最低点到当前的周期数
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: N日时间窗口
        :rtype: Indicator
    """
@typing.overload
def LN() -> Indicator:
    ...
@typing.overload
def LN(arg0: float) -> Indicator:
    ...
@typing.overload
def LN(arg0: Indicator) -> Indicator:
    """
    LN([data])
    
        求自然对数, LN(X)以e为底的对数
    
        :param data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def LOG() -> Indicator:
    ...
@typing.overload
def LOG(arg0: float) -> Indicator:
    ...
@typing.overload
def LOG(arg0: Indicator) -> Indicator:
    """
    LOG([data])
    
        以10为底的对数
    
        :param data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def LONGCROSS(a: Indicator, b: Indicator, n: int = 3) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: Indicator, b: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: Indicator, b: float, n: int = 3) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: Indicator, b: float, n: Indicator) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: float, b: Indicator, n: int = 3) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: float, b: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: float, b: float, n: int = 3) -> Indicator:
    ...
@typing.overload
def LONGCROSS(a: float, b: float, n: Indicator) -> Indicator:
    """
    LONGCROSS(a, b[, n=3])
    
        两条线维持一定周期后交叉
    
        用法：LONGCROSS(A,B,N)表示A在N周期内都小于B，本周期从下方向上穿过B时返 回1，否则返回0
    
        例如：LONGCROSS(MA(CLOSE,5),MA(CLOSE,10),5)表示5日均线维持5周期后与10日均线交金叉
    
        :param Indicator a:
        :param Indicator b:
        :param int|Indicator n:
        :rtype: Indicator
    """
@typing.overload
def MA(n: int = 22) -> Indicator:
    ...
@typing.overload
def MA(n: IndParam) -> Indicator:
    ...
@typing.overload
def MA(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def MA(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def MA(data: Indicator, n: int = 22) -> Indicator:
    """
    MA([data, n=22])
    
        简单移动平均
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def MACD(n1: int = 12, n2: int = 26, n3: int = 9) -> Indicator:
    ...
@typing.overload
def MACD(n1: IndParam, n2: IndParam, n3: IndParam) -> Indicator:
    ...
@typing.overload
def MACD(data: Indicator, n1: int = 12, n2: int = 26, n3: int = 9) -> Indicator:
    ...
@typing.overload
def MACD(data: Indicator, n1: IndParam, n2: IndParam, n3: IndParam) -> Indicator:
    ...
@typing.overload
def MACD(data: Indicator, n1: Indicator, n2: Indicator, n3: Indicator) -> Indicator:
    """
    MACD([data, n1=12, n2=26, n3=9])
    
        平滑异同移动平均线
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n1: 短期EMA时间窗
        :param int|Indicator|IndParam n2: 长期EMA时间窗
        :param int|Indicator|IndParam n3: （短期EMA-长期EMA）EMA平滑时间窗
        :rtype: 具有三个结果集的 Indicator
    
        * result(0): MACD_BAR：MACD直柱，即MACD快线－MACD慢线
        * result(1): DIFF: 快线,即（短期EMA-长期EMA）
        * result(2): DEA: 慢线，即快线的n3周期EMA平滑
    """
@typing.overload
def MAX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def MAX(arg0: Indicator, arg1: float) -> Indicator:
    ...
@typing.overload
def MAX(arg0: float, arg1: Indicator) -> Indicator:
    """
    MAX(ind1, ind2)
    
        求最大值, MAX(A,B)返回A和B中的较大值。
    
        :param Indicator ind1: A
        :param Indicator ind2: B
        :rtype: Indicator
    """
@typing.overload
def MDD() -> Indicator:
    ...
@typing.overload
def MDD(arg0: Indicator) -> Indicator:
    """
    MDD([data])
        
        当前价格相对历史最高值的回撤百分比，通常用于计算最大回撤
    """
@typing.overload
def MF_EqualWeight() -> MultiFactorBase:
    ...
@typing.overload
def MF_EqualWeight(inds: typing.Sequence, stks: typing.Sequence, query: Query, ref_stk: typing.Any = None, ic_n: int = 5, spearman: bool = True, mode: int = 0, save_all_factors: bool = False) -> MultiFactorBase:
    """
    MF_EqualWeight(inds, stks, query, ref_stk[, ic_n=5])
    
        等权重合成因子
    
        :param sequense(Indicator) inds: 原始因子列表
        :param sequense(stock) stks: 计算证券列表
        :param Query query: 日期范围
        :param Stock ref_stk: 参考证券 (未指定时，默认为 sh000300 沪深300)
        :param int ic_n: 默认 IC 对应的 N 日收益率
        :param bool spearman: 默认使用 spearman 计算相关系数，否则为 pearson
        :param int mode: 获取截面数据时排序模式: 0-降序, 1-升序, 2-不排序
        :param bool save_all_factors: 是否保存所有因子值,影响 get_actor/get_all_factors 方法
        :rtype: MultiFactorBase
    """
@typing.overload
def MF_ICIRWeight() -> MultiFactorBase:
    ...
@typing.overload
def MF_ICIRWeight(inds: typing.Sequence, stks: typing.Sequence, query: Query, ref_stk: typing.Any = None, ic_n: int = 5, ic_rolling_n: int = 120, spearman: bool = True, mode: int = 0, save_all_factors: bool = False) -> MultiFactorBase:
    """
    MF_EqualWeight(inds, stks, query, ref_stk[, ic_n=5, ic_rolling_n=120])
    
        滚动ICIR权重合成因子
    
        :param sequense(Indicator) inds: 原始因子列表
        :param sequense(stock) stks: 计算证券列表
        :param Query query: 日期范围
        :param Stock ref_stk: 参考证券 (未指定时，默认为 sh000300 沪深300)
        :param int ic_n: 默认 IC 对应的 N 日收益率
        :param int ic_rolling_n: IC 滚动周期
        :param bool spearman: 默认使用 spearman 计算相关系数，否则为 pearson
        :param int mode: 获取截面数据时排序模式: 0-降序, 1-升序, 2-不排序
        :param bool save_all_factors: 是否保存所有因子值,影响 get_actor/get_all_factors 方法
        :rtype: MultiFactorBase
    """
@typing.overload
def MF_ICWeight() -> MultiFactorBase:
    ...
@typing.overload
def MF_ICWeight(inds: typing.Sequence, stks: typing.Sequence, query: Query, ref_stk: typing.Any = None, ic_n: int = 5, ic_rolling_n: int = 120, spearman: bool = True, mode: int = 0, save_all_factors: bool = False) -> MultiFactorBase:
    """
    MF_EqualWeight(inds, stks, query, ref_stk[, ic_n=5, ic_rolling_n=120])
    
        滚动IC权重合成因子
    
        :param sequense(Indicator) inds: 原始因子列表
        :param sequense(stock) stks: 计算证券列表
        :param Query query: 日期范围
        :param Stock ref_stk:  (未指定时，默认为 sh000300 沪深300)
        :param int ic_n: 默认 IC 对应的 N 日收益率
        :param int ic_rolling_n: IC 滚动周期
        :param bool spearman: 默认使用 spearman 计算相关系数，否则为 pearson
        :param int mode: 获取截面数据时排序模式: 0-降序, 1-升序, 2-不排序
        :param bool save_all_factors: 是否保存所有因子值,影响 get_actor/get_all_factors 方法
        :rtype: MultiFactorBase
    """
@typing.overload
def MF_Weight() -> MultiFactorBase:
    ...
@typing.overload
def MF_Weight(inds: typing.Sequence, stks: typing.Sequence, weights: typing.Sequence, query: Query, ref_stk: typing.Any = None, ic_n: int = 5, spearman: bool = True, mode: int = 0, save_all_factors: bool = False) -> MultiFactorBase:
    """
    MF_EqualWeight(inds, stks, query, ref_stk[, ic_n=5])
    
        按指定权重合成因子 = ind1 * weight1 + ind2 * weight2 + ... + indn * weightn
    
        :param sequense(Indicator) inds: 原始因子列表
        :param sequense(float) weights: 权重列表(需和 inds 等长)
        :param sequense(stock) stks: 计算证券列表
        :param Query query: 日期范围
        :param Stock ref_stk: 参考证券 (未指定时，默认为 sh000300 沪深300)
        :param int ic_n: 默认 IC 对应的 N 日收益率
        :param bool spearman: 默认使用 spearman 计算相关系数，否则为 pearson
        :param int mode: 获取截面数据时排序模式: 0-降序, 1-升序, 2-不排序
        :param bool save_all_factors: 是否保存所有因子值,影响 get_actor/get_all_factors 方法
        :rtype: MultiFactorBase
    """
@typing.overload
def MIN(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def MIN(arg0: Indicator, arg1: float) -> Indicator:
    ...
@typing.overload
def MIN(arg0: float, arg1: Indicator) -> Indicator:
    """
    MIN(ind1, ind2)
    
        求最小值, MIN(A,B)返回A和B中的较小值。
    
        :param Indicator ind1: A
        :param Indicator ind2: B
        :rtype: Indicator
    """
@typing.overload
def MINUTE() -> Indicator:
    ...
@typing.overload
def MINUTE(arg0: KData) -> Indicator:
    """
    MINUTE([data])
    
        取得该周期的分钟数。用法：MINUTE 函数返回有效值范围为(0-59)，对于日线及更长的分析周期值为0。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
def MM_FixedCapital(capital: float = 10000.0) -> MoneyManagerBase:
    """
    MM_FixedCapital([capital = 10000.0])
    
        固定资金管理策略。买入数量 = 当前现金 / capital
    
        :param float capital: 固定资本单位
        :return: 资金管理策略实例
    """
def MM_FixedCapitalFunds(capital: float = 10000.0) -> MoneyManagerBase:
    """
    MM_FixedCapitalFunds([capital = 10000.0]) 
    
        固定资本管理策略。买入数量 = 当前总资产 / capital
      
        :param float capital: 固定资本单位
        :return: 资金管理策略实例
    """
def MM_FixedCount(n: float = 100) -> MoneyManagerBase:
    """
    MM_FixedCount([n = 100])
    
        固定交易数量资金管理策略。每次买入固定的数量。
        
        :param float n: 每次买入的数量（应该是交易对象最小交易数量的整数，此处程序没有此进行判断）
        :return: 资金管理策略实例
    """
def MM_FixedCountTps(buy_counts: list[float], sell_counts: list[float]) -> MoneyManagerBase:
    """
    MM_FixedCountTps([buy_counts, sell_counts])
              
        连续买入/卖出固定数量资金管理策略。
        
        :param list buy_counts: 买入数量列表
        :param list sell_counts: 卖出数量列表
        :return: 资金管理策略实例
    """
def MM_FixedPercent(p: float = 0.03) -> MoneyManagerBase:
    """
    MM_FixedPercent([p = 0.03])
    
        固定百分比风险模型。公式：P（头寸规模）＝ 账户余额 * 百分比 / R（每股的交易风险）。[BOOK3]_, [BOOK4]_ .
        
        :param float p: 百分比
        :return: 资金管理策略实例
    """
def MM_FixedRisk(risk: float = 1000.0) -> MoneyManagerBase:
    """
    MM_FixedRisk([risk = 1000.00])
    
        固定风险资金管理策略对每笔交易限定一个预先确定的或者固定的资金风险，如每笔交易固定风险1000元。公式：交易数量 = 固定风险 / 交易风险。
    
        :param float risk: 固定风险
        :return: 资金管理策略实例
    """
def MM_FixedUnits(n: int = 33) -> MoneyManagerBase:
    """
    MM_FixedUnits([n = 33])
    
        固定单位资金管理策略。公式: 买入数量 = 当前现金 / n / 当前风险risk
    
        :param int n: n个资金单位
        :return: 资金管理策略实例
    """
def MM_Nothing() -> MoneyManagerBase:
    """
    MM_Nothing()
    
        特殊的资金管理策略，相当于不做资金管理，有多少钱买多少。
    """
def MM_WilliamsFixedRisk(p: float = 0.1, max_loss: float = 1000.0) -> MoneyManagerBase:
    """
     MM_WilliamsFixedRisk([p=0.1, max_loss=1000.0])
    
        威廉斯固定风险资金管理策略。买入数量 =（账户余额 × 风险百分比p）÷ 最大损失(max_loss)
    
        :param float p: 风险百分比
        :param float max_loss: 最大损失
        :return: 资金管理策略实例
    """
@typing.overload
def MOD(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def MOD(arg0: Indicator, arg1: float) -> Indicator:
    ...
@typing.overload
def MOD(arg0: float, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def MOD(arg0: float, arg1: float) -> Indicator:
    """
    MOD(ind1, ind2)
    
        取整后求模。该函数仅为兼容通达信。实际上，指标求模可直接使用 % 操作符
    
        用法：MOD(A,B)返回A对B求模
    
        例如：MOD(26,10) 返回 6
    
        :param Indicator ind1:
        :param Indicator ind2:
        :rtype: Indicator
    """
@typing.overload
def MONTH() -> Indicator:
    ...
@typing.overload
def MONTH(arg0: KData) -> Indicator:
    """
    MONTH([data])
    
        取得该周期的月份。用法: MONTH 函数返回有效值范围为(1-12)。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def MRR() -> Indicator:
    ...
@typing.overload
def MRR(arg0: Indicator) -> Indicator:
    """
    MRR([data])
        
        当前价格相对历史最低值的盈利百分比，可用于计算历史最高盈利比例
    """
def Microseconds(arg0: int) -> TimeDelta:
    """
    Microseconds(microsecs)
    
          TimeDelta 快捷创建函数
    
          :param int microsecs: 微秒数
          :rtype: TimeDelta
    """
def Milliseconds(arg0: int) -> TimeDelta:
    """
    Milliseconds(milliseconds)
    
          TimeDelta 快捷创建函数
    
          :param int milliseconds: 毫秒数
          :rtype: TimeDelta
    """
def Minutes(arg0: int) -> TimeDelta:
    """
    Minutes(mins)
    
          TimeDelta 快捷创建函数
    
          :param int mins: 分钟数
          :rtype: TimeDelta
    """
@typing.overload
def NDAY(x: Indicator, y: Indicator, n: int = 3) -> Indicator:
    ...
@typing.overload
def NDAY(x: Indicator, y: Indicator, n: int) -> Indicator:
    ...
@typing.overload
def NDAY(x: Indicator, y: Indicator, n: IndParam) -> Indicator:
    """
    NDAY(x, y[, n=3])
    
        连大, NDAY(X,Y,N)表示条件X>Y持续存在N个周期
    
        :param Indicator x:
        :param Indicator y:
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def NOT() -> Indicator:
    ...
@typing.overload
def NOT(arg0: Indicator) -> Indicator:
    """
    NOT([data])
    
        求逻辑非。NOT(X)返回非X,即当X=0时返回1，否则返回0。
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
def PF_Simple(tm: TradeManager = None, se: SelectorBase = ..., af: AllocateFundsBase = ..., adjust_cycle: int = 1, adjust_mode: str = 'query', delay_to_trading_day: bool = True) -> Portfolio:
    """
    PF_Simple([tm, se, af, adjust_cycle=1, adjust_mode="query", delay_to_trading_day=True])
    
        创建一个多标的、单系统策略的投资组合
    
        调仓模式 adjust_mode 说明：
        - "query" 模式，跟随输入参数 query 中的 ktype，此时 adjust_cycle 为以 query 中的 ktype
          决定周期间隔；
        - "day" 模式，adjust_cycle 为调仓间隔天数
        - "week" | "month" | "quarter" | "year" 模式时，adjust_cycle
          为对应的每周第N日、每月第n日、每季度第n日、每年第n日，在 delay_to_trading_day 为 false 时
          如果当日不是交易日将会被跳过调仓；当 delay_to_trading_day 为 true时，如果当日不是交易日
          将会顺延至当前周期内的第一个交易日，如指定每月第1日调仓，但当月1日不是交易日，则将顺延至当月
          的第一个交易日。    
    
        :param TradeManager tm: 交易管理
        :param SelectorBase se: 交易对象选择算法
        :param AllocateFundsBase af: 资金分配算法
        :param int adjust_cycle: 调仓周期
        :param str adjust_mode: 调仓模式
        :param bool delay_to_trading_day: 如果当日不是交易日将会被顺延至当前周期内的第一个交易日
    """
def PF_WithoutAF(tm: TradeManager = None, se: SelectorBase = ..., adjust_cycle: int = 1, adjust_mode: str = 'query', delay_to_trading_day: bool = True, trade_on_close: bool = True, sys_use_self_tm: bool = False, sell_at_not_selected: bool = False) -> Portfolio:
    """
    PF_WithoutAF([tm, se, adjust_cycle=1, adjust_mode="query", delay_to_trading_day=True, trade_on_close=True, sys_use_self_tm=False,sell_at_not_selected=False])
        
        创建无资金分配算法的投资组合，所有单系统策略使用共同的 tm 管理账户
    
        调仓模式 adjust_mode 说明：
        - "query" 模式，跟随输入参数 query 中的 ktype，此时 adjust_cycle 为以 query 中的 ktype
          决定周期间隔；
        - "day" 模式，adjust_cycle 为调仓间隔天数
        - "week" | "month" | "quarter" | "year" 模式时，adjust_cycle
          为对应的每周第N日、每月第n日、每季度第n日、每年第n日，在 delay_to_trading_day 为 false 时
          如果当日不是交易日将会被跳过调仓；当 delay_to_trading_day 为 true时，如果当日不是交易日
          将会顺延至当前周期内的第一个交易日，如指定每月第1日调仓，但当月1日不是交易日，则将顺延至当月
          的第一个交易日。    
    
        :param TradeManager tm: 交易管理
        :param SelectorBase se: 交易对象选择算法
        :param int adjust_cycle: 调仓周期
        :param str adjust_mode: 调仓模式
        :param bool delay_to_trading_day: 如果当日不是交易日将会被顺延至当前周期内的第一个交易日
        :param bool trade_on_close: 交易是否在收盘时进行
        :param bool sys_use_self_tm: 原型系统使用自身附带的tm进行计算
        :param bool sell_at_not_selected: 调仓日未选中的股票是否强制卖出
    """
def PG_FixedHoldDays(days: int = 5) -> ProfitGoalBase:
    """
    PG_FixedHoldDays([days=5])
    
        固定持仓天数盈利目标策略
        
        :param int days: 允许持仓天数（按交易日算）,默认5天
        :return: 盈利目标策略实例
    """
def PG_FixedPercent(p: float = 0.2) -> ProfitGoalBase:
    """
    PG_FixedPercent([p = 0.2])
    
        固定百分比盈利目标，目标价格 = 买入价格 * (1 + p)
        
        :param float p: 百分比
        :return: 盈利目标策略实例
    """
def PG_NoGoal() -> ProfitGoalBase:
    """
    PG_NoGoal()
    
        无盈利目标策略，通常为了进行测试或对比。
        
        :return: 盈利目标策略实例
    """
def POS(block: Block, query: Query, sg: ...) -> Indicator:
    ...
@typing.overload
def POW(n: int) -> Indicator:
    ...
@typing.overload
def POW(n: IndParam) -> Indicator:
    ...
@typing.overload
def POW(data: Indicator, n: int) -> Indicator:
    ...
@typing.overload
def POW(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def POW(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def POW(data: float, n: int) -> Indicator:
    """
    POW(data, n)
    
        乘幂
    
        用法：POW(A,B)返回A的B次幂
    
        例如：POW(CLOSE,3)求得收盘价的3次方
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: 幂
        :rtype: Indicator
    """
def PRICELIST(data: typing.Any = None, discard: int = 0, align_dates: typing.Any = None) -> Indicator:
    """
    PRICELIST([data=None, discard=0, align_dates=None])
          
        将python数组（如 list, tuple, numpy.array）转换为Indicator对象。
        
        :param sequence data: 输入数据
        :param int discard: 丢弃前多少个数据
        :param sequence align_dates: 对齐日期列表，如果为空则不进行对齐
        :rtype: Indicator
    """
@typing.overload
def RANK(block: Block, ref_ind: Indicator, mode: int = 0, fill_null: bool = True, market: str = 'SH') -> Indicator:
    ...
@typing.overload
def RANK(stks: typing.Sequence, ref_ind: Indicator, mode: int = 0, fill_null: bool = True, market: str = 'SH') -> Indicator:
    """
    RANK(stks, ref_ind, mode = 0, fill_null = true, market = 'SH')
          
        计算指标值在指定板块中的排名
    
        :param stks: 指定证券列表 或 Block
        :param ref_ind: 参考指标
        :param mode: 排序方式: 0-降序排名(指标值最高值排名为1), 1-升序排名(指标值越大排名值越大), 2-降序排名百分比, 3-升序排名百分比
        :param fill_null: 是否填充缺失值
        :param market: 板块所属市场
        :return: 指标值在指定板块中的排名
        :rtype: Indicator
    """
@typing.overload
def RECOVER_BACKWARD() -> Indicator:
    ...
@typing.overload
def RECOVER_BACKWARD(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def RECOVER_BACKWARD(arg0: KData) -> Indicator:
    """
    RECOVER_BACKWARD([data])
        
        对输入的指标数据 (CLOSE|OPEN|HIGH|LOW) 进行后向复权
    
        :param Indicator|KData data: 只接受 CLOSE|OPEN|HIGH|LOW 指标，或 KData（此时默认使用 KData 的收盘价）
        :rtype: Indicator
    """
@typing.overload
def RECOVER_EQUAL_BACKWARD() -> Indicator:
    ...
@typing.overload
def RECOVER_EQUAL_BACKWARD(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def RECOVER_EQUAL_BACKWARD(arg0: KData) -> Indicator:
    """
    RECOVER_EQUAL_BACKWARD([data])
        
        对输入的指标数据 (CLOSE|OPEN|HIGH|LOW) 进行等比后向复权
    
        :param Indicator|KData data: 只接受 CLOSE|OPEN|HIGH|LOW 指标，或 KData（此时默认使用 KData 的收盘价）
        :rtype: Indicator
    """
@typing.overload
def RECOVER_EQUAL_FORWARD() -> Indicator:
    ...
@typing.overload
def RECOVER_EQUAL_FORWARD(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def RECOVER_EQUAL_FORWARD(arg0: KData) -> Indicator:
    """
    RECOVER_EQUAL_FORWARD([data])
        
        对输入的指标数据 (CLOSE|OPEN|HIGH|LOW) 进行等比前向复权
    
        :param Indicator|KData data: 只接受 CLOSE|OPEN|HIGH|LOW 指标，或 KData（此时默认使用 KData 的收盘价）
        :rtype: Indicator
    """
@typing.overload
def RECOVER_FORWARD() -> Indicator:
    ...
@typing.overload
def RECOVER_FORWARD(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def RECOVER_FORWARD(arg0: KData) -> Indicator:
    """
    RECOVER_FORWARD([data])
        
        对输入的指标数据 (CLOSE|OPEN|HIGH|LOW) 进行前向复权
    
        :param Indicator|KData data: 只接受 CLOSE|OPEN|HIGH|LOW 指标，或 KData（此时默认使用 KData 的收盘价）
        :rtype: Indicator
    """
@typing.overload
def REF(n: int) -> Indicator:
    ...
@typing.overload
def REF(n: IndParam) -> Indicator:
    ...
@typing.overload
def REF(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def REF(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def REF(data: Indicator, n: int) -> Indicator:
    """
    REF([data, n])
    
        向前引用 （即右移），引用若干周期前的数据。
    
        用法：REF(X，A)　引用A周期前的X值。
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 引用n周期前的值，即右移n位
        :rtype: Indicator
    """
@typing.overload
def REPLACE(old_value: float = ..., new_value: float = 0.0, ignore_discard: bool = False) -> Indicator:
    ...
@typing.overload
def REPLACE(ind: Indicator, old_value: float = ..., new_value: float = 0.0, ignore_discard: bool = False) -> Indicator:
    """
    REPLACE(ind, [old_value=constant.nan, new_value=0.0, ignore_discard=False]
              
        替换指标中指定值，默认为替换 nan 值为 0.0。
    
        :param Indicator ind: 指定指标
        :param double old_value: 指定值
        :param double new_value: 替换值
        :param bool ignore_discard: 忽略指标丢弃数据
        :rtype: Indicator
    """
@typing.overload
def RESULT(arg0: int) -> Indicator:
    ...
@typing.overload
def RESULT(data: Indicator, result_ix: int) -> Indicator:
    """
    RESULT(data, result_ix)
              
        以公式指标的方式返回指定指标中的指定结果集
    
        :param Indicator data: 指定的指标
        :param int result_ix: 指定的结果集
    """
@typing.overload
def REVERSE() -> Indicator:
    ...
@typing.overload
def REVERSE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def REVERSE(arg0: float) -> Indicator:
    """
    REVERSE([data])
    
        求相反数，REVERSE(X)返回-X
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def ROC(n: int = 10) -> Indicator:
    ...
@typing.overload
def ROC(n: IndParam) -> Indicator:
    ...
@typing.overload
def ROC(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def ROC(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def ROC(data: Indicator, n: int = 10) -> Indicator:
    """
    ROC([data, n=10])
    
        变动率指标: ((price / prevPrice)-1)*100
    
        :param data: 输入数据
        :param int n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def ROCP(n: int = 10) -> Indicator:
    ...
@typing.overload
def ROCP(n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCP(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCP(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def ROCP(data: Indicator, n: int = 10) -> Indicator:
    """
    ROCP([data, n=10])
    
        变动率指标: (price - prevPrice) / prevPrice
    
        :param data: 输入数据
        :param int n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def ROCR(n: int = 10) -> Indicator:
    ...
@typing.overload
def ROCR(n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCR(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCR(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def ROCR(data: Indicator, n: int = 10) -> Indicator:
    """
    ROCR([data, n=10])
    
        变动率指标: (price / prevPrice)
    
        :param data: 输入数据
        :param int n|Indicator|IndParam: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def ROCR100(n: int = 10) -> Indicator:
    ...
@typing.overload
def ROCR100(n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCR100(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def ROCR100(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def ROCR100(data: Indicator, n: int = 10) -> Indicator:
    """
    ROCR100([data, n=10])
    
        变动率指标: (price / prevPrice) * 100
    
        :param data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def ROUND(ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUND(data: Indicator, ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUND(data: float, ndigits: int = 2) -> Indicator:
    """
    ROUND([data, ndigits=2])
    
        四舍五入
    
        :param data: 输入数据
        :param int ndigits: 保留的小数点后位数
        :rtype: Indicator
    """
@typing.overload
def ROUNDDOWN(ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUNDDOWN(data: Indicator, ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUNDDOWN(data: float, ndigits: int = 2) -> Indicator:
    """
    ROUND([data, ndigits=2])
    
        四舍五入
    
        :param data: 输入数据
        :param int ndigits: 保留的小数点后位数
        :rtype: Indicator
    """
@typing.overload
def ROUNDUP(ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUNDUP(data: Indicator, ndigits: int = 2) -> Indicator:
    ...
@typing.overload
def ROUNDUP(data: float, ndigits: int = 2) -> Indicator:
    """
    ROUNDUP([data, ndigits=2])
    
        向上截取，如10.1截取后为11
    
        :param data: 输入数据
        :param int ndigits: 保留的小数点后位数
        :rtype: Indicator
    """
@typing.overload
def RSI(n: int = 14) -> Indicator:
    ...
@typing.overload
def RSI(data: Indicator, n: int = 14) -> Indicator:
    """
    RSI([data, n=14])
    
        相对强弱指数
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def SAFTYLOSS(n1: int = 10, n2: int = 3, p: float = 2.0) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(n1: IndParam, n2: IndParam, p: float = 2.0) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(n1: IndParam, n2: IndParam, p: IndParam) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(data: Indicator, n1: int = 10, n2: int = 3, p: float = 2.0) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(data: Indicator, n1: IndParam, n2: IndParam, p: float = 2.0) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(data: Indicator, n1: IndParam, n2: IndParam, p: IndParam) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(data: Indicator, n1: Indicator, n2: Indicator, p: float = 2.0) -> Indicator:
    ...
@typing.overload
def SAFTYLOSS(data: Indicator, n1: Indicator, n2: Indicator, p: Indicator) -> Indicator:
    """
    SAFTYLOSS([data, n1=10, n2=3, p=2.0])
    
        亚历山大 艾尔德安全地带止损线，参见 [BOOK2]_
    
        计算说明：在回溯周期内（一般为10到20天），将所有向下穿越的长度相加除以向下穿越的次数，得到噪音均值（即回溯期内所有最低价低于前一日最低价的长度除以次数），并用今日最低价减去（前日噪音均值乘以一个倍数）得到该止损线。为了抵消波动并且保证止损线的上移，在上述结果的基础上再取起N日（一般为3天）内的最高值
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n1: 计算平均噪音的回溯时间窗口
        :param int|Indicator|IndParam n2: 对初步止损线去n2日内的最高值
        :param float|Indicator|IndParam p: 噪音系数
        :rtype: Indicator
    """
def SE_EvaluateOptimal(arg0: typing.Any) -> SelectorBase:
    """
    SE_EvaluateOptimal(evalulate_func)
    
        使用自定义函数进行寻优的选择器
    
        :param func: 一个可调用对象，接收参数为 (sys, lastdate)，返回一个 float 数值
    """
@typing.overload
def SE_Fixed(weight: float = 1.0) -> SelectorBase:
    ...
@typing.overload
def SE_Fixed(stk_list: typing.Sequence, sys: ..., weight: float = 1.0) -> SelectorBase:
    """
    SE_Fixed([stk_list, sys])
    
        固定选择器，即始终选择初始划定的标的及其系统策略原型
    
        :param list stk_list: 初始划定的标的
        :param System sys: 系统策略原型
        :param float weight: 默认权重
        :return: SE选择器实例
    """
def SE_MaxFundsOptimal() -> SelectorBase:
    """
    账户资产最大寻优选择器
    """
@typing.overload
def SE_MultiFactor(mf: ..., topn: int = 10) -> SelectorBase:
    ...
@typing.overload
def SE_MultiFactor(inds: typing.Sequence, topn: int = 10, ic_n: int = 5, ic_rolling_n: int = 120, ref_stk: typing.Any = None, spearman: bool = True, mode: str = 'MF_ICIRWeight') -> SelectorBase:
    """
    SE_MultiFactor
    
        创建基于多因子评分的选择器，两种创建方式
    
        - 直接指定 MF:
          :param MultiFactorBase mf: 直接指定的多因子合成算法
          :param int topn: 只选取时间截面中前 topn 个系统
    
        - 参数直接创建:
          :param sequense(Indicator) inds: 原始因子列表
          :param int topn: 只选取时间截面中前 topn 个系统，小于等于0时代表不限制
          :param int ic_n: 默认 IC 对应的 N 日收益率
          :param int ic_rolling_n: IC 滚动周期
          :param Stock ref_stk: 参考证券 (未指定时，默认为 sh000300 沪深300)
          :param bool spearman: 默认使用 spearman 计算相关系数，否则为 pearson
          :param str mode: "MF_ICIRWeight" | "MF_ICWeight" | "MF_EqualWeight" 因子合成算法名称
    """
def SE_PerformanceOptimal(key: str = '帐户平均年收益率%', mode: int = 0) -> SelectorBase:
    """
    SE_PerformanceOptimal(key="帐户平均年收益率%", mode=0)
    
        使用 Performance 统计结果进行寻优的选择器
    
        :param string key: Performance 统计项
        :param int mode:  0 取统计结果最大的值系统 | 1 取统计结果为最小值的系统
    """
@typing.overload
def SE_Signal() -> SelectorBase:
    ...
@typing.overload
def SE_Signal(arg0: list[Stock], arg1: ...) -> SelectorBase:
    """
    SE_Signal([stk_list, sys])
    
        信号选择器，仅依靠系统买入信号进行选中
    
        :param list stk_list: 初始划定的标的
        :param System sys: 系统策略原型
        :return: SE选择器实例
    """
@typing.overload
def SGN() -> Indicator:
    ...
@typing.overload
def SGN(arg0: float) -> Indicator:
    ...
@typing.overload
def SGN(arg0: Indicator) -> Indicator:
    """
    SGN([data])
    
        求符号值, SGN(X)，当 X>0, X=0, X<0分别返回 1, 0, -1。
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def SG_Add(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_Add(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_Add(sg1, sg2, alternate)
    
        生成两个指标之和的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
        :return: 信号指示器
    """
def SG_AllwaysBuy() -> SignalBase:
    """
    SG_AllwaysBuy()
        
        一个特殊的SG，持续每天发出买入信号，通常配合 PF 使用
    """
@typing.overload
def SG_And(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_And(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_And(sg1, sg2, alternate)
    
        生成两个指标与的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
    """
@typing.overload
def SG_Band(ind: Indicator, lower: Indicator, upper: Indicator) -> SignalBase:
    ...
@typing.overload
def SG_Band(ind: Indicator, lower: float, upper: float) -> SignalBase:
    """
    SG_Band(ind, lower, upper)
              
        指标区间指示器, 当指标超过上轨时，买入；
        当指标低于下轨时，卖出。::
    
            SG_Band(MA(C, n=10), 100, 200)
            SG_Band(CLOSE, MA(LOW), MA(HIGH))
    """
def SG_Bool(buy: Indicator, sell: Indicator, alternate: bool = True) -> SignalBase:
    """
    SG_Bool(buy, sell)
    
        布尔信号指示器，使用运算结果为类似bool数组的Indicator分别作为买入、卖出指示。
    
        :param Indicator buy: 买入指示（结果Indicator中相应位置>0则代表买入）
        :param Indicator sell: 卖出指示（结果Indicator中相应位置>0则代表卖出）
        :param bool alternate: 是否交替买入卖出，默认为True
        :return: 信号指示器
    """
def SG_Buy(ind: Indicator) -> SignalBase:
    """
    SG_Buy(ind)
        
        生成单边买入信号
    
        :param Indicator ind: 输入指标
        :return: 信号指示器
    """
def SG_Cross(fast: Indicator, slow: Indicator) -> SignalBase:
    """
    SG_Cross(fast, slow)
    
        双线交叉指示器，当快线从下向上穿越慢线时，买入；当快线从上向下穿越慢线时，卖出。如：5日MA上穿10日MA时买入，5日MA线下穿MA10日线时卖出:: 
    
            SG_Cross(MA(C, n=10), MA(C, n=30))
    
        :param Indicator fast: 快线
        :param Indicator slow: 慢线
        :return: 信号指示器
    """
def SG_CrossGold(fast: Indicator, slow: Indicator) -> SignalBase:
    """
    SG_CrossGold(fast, slow)
    
        金叉指示器，当快线从下向上穿越慢线且快线和慢线的方向都是向上时为金叉，买入；
        当快线从上向下穿越慢线且快线和慢线的方向都是向下时死叉，卖出。::
    
            SG_CrossGold(MA(C, n=10), MA(C, n=30))
    
        :param Indicator fast: 快线
        :param Indicator slow: 慢线
        :return: 信号指示器
    """
def SG_Cycle() -> SignalBase:
    """
    SG_Cycle()
        
        一个特殊的SG，配合PF使用，以 PF 调仓周期为买入信号
    """
@typing.overload
def SG_Div(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_Div(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_Div(sg1, sg2, alternate)
    
        生成两个指标之差的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
    """
def SG_Flex(op: Indicator, slow_n: int) -> SignalBase:
    """
    SG_Flex(ind, slow_n)
    
        使用自身的EMA(slow_n)作为慢线，自身作为快线，快线向上穿越慢线买入，快线向下穿越慢线卖出。
    
        :param Indicator ind:
        :param int slow_n: 慢线EMA周期
        :return: 信号指示器
    """
@typing.overload
def SG_Mul(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_Mul(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_Mul(sg1, sg2, alternate)
    
        生成两个指标之差的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
    """
def SG_OneSide(ind: Indicator, is_buy: bool) -> SignalBase:
    """
    SG_OneSide(ind, is_buy)
              
        根据输入指标构建单边信号（单纯的只包含买入或卖出信号），如果指标值大于0，则加入信号。也可以使用 SG_Buy 或 SG_Sell 函数。
        
        :param Indicator ind: 输入指标
        :param bool is_buy: 构建的是买入信号，否则为卖出信号
        :return: 信号指示器
    """
@typing.overload
def SG_Or(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_Or(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_Or(sg1, sg2, alternate)
    
        生成两个指标与的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
    """
def SG_Sell(ind: Indicator) -> SignalBase:
    """
    SG_Sell(ind)
        
        生成单边卖出信号
    
        :param Indicator ind: 输入指标
        :return: 信号指示器
    """
def SG_Single(ind: Indicator, filter_n: int = 10, filter_p: float = 0.1) -> SignalBase:
    """
    SG_Single(ind[, filter_n = 10, filter_p = 0.1])
        
        生成单线拐点信号指示器。使用《精明交易者》 [BOOK1]_ 中给出的曲线拐点算法判断曲线趋势，公式见下::
    
            filter = percentage * STDEV((AMA-AMA[1], N)
    
            Buy  When AMA - AMA[1] > filter
            or Buy When AMA - AMA[2] > filter
            or Buy When AMA - AMA[3] > filter 
        
        :param Indicator ind:
        :param int filer_n: N日周期
        :param float filter_p: 过滤器百分比
        :return: 信号指示器
    """
def SG_Single2(ind: Indicator, filter_n: int = 10, filter_p: float = 0.1) -> SignalBase:
    """
    SG_Single2(ind[, filter_n = 10, filter_p = 0.1])
        
        生成单线拐点信号指示器2 [BOOK1]_::
    
            filter = percentage * STDEV((AMA-AMA[1], N)
    
            Buy  When AMA - @lowest(AMA,n) > filter
            Sell When @highest(AMA, n) - AMA > filter
        
        :param Indicator ind:
        :param int filer_n: N日周期
        :param float filter_p: 过滤器百分比
        :return: 信号指示器
    """
@typing.overload
def SG_Sub(sg_list: typing.Sequence, alternate: bool) -> SignalBase:
    ...
@typing.overload
def SG_Sub(sg1: SignalBase, sg2: SignalBase, alternate: bool) -> SignalBase:
    """
    SG_Sub(sg1, sg2, alternate)
    
        生成两个指标之差的信号
    
        由于 SG 的 alternate 默认为 True, 在使用如  "sg1 + sg2 + sg3" 的形式时，容易忽略 sg1 + sg2 的 alternate 属性
        建议使用: SG_Add(sg1, sg2, False) + sg3 来避免 alternate 的问题
    
        :param SignalBase sg1: 输入信号1
        :param SignalBase sg2: 输入信号2
        :param bool alternate: 是否交替买入卖出，默认为True
        :return: 信号指示器
    """
@typing.overload
def SIN() -> Indicator:
    ...
@typing.overload
def SIN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def SIN(arg0: float) -> Indicator:
    """
    SIN([data])
    
        正弦值
    
        :param Indicator data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def SLICE(data: list[float], start: int, end: int) -> Indicator:
    ...
@typing.overload
def SLICE(start: int, end: int, result_index: int = 0) -> Indicator:
    ...
@typing.overload
def SLICE(data: Indicator, start: int, end: int, result_index: int = 0) -> Indicator:
    """
    SLICE(data, start, end, result_index=0)
    
        获取某指标中指定范围 [start, end) 的数据，生成新的指标
    
        :param Indicator|PriceList data: 输入数据
        :param int start: 起始位置
        :param int end: 终止位置（不包含本身）
        :param int result_index: 原输入数据中的结果集
    """
@typing.overload
def SLOPE(n: int = 22) -> Indicator:
    ...
@typing.overload
def SLOPE(n: IndParam) -> Indicator:
    ...
@typing.overload
def SLOPE(data: Indicator, n: int = 22) -> Indicator:
    ...
@typing.overload
def SLOPE(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def SLOPE(data: Indicator, n: Indicator) -> Indicator:
    """
    SLOPE([data, n=22])
    
        计算线性回归斜率，N支持变量
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def SMA(n: int = 22, m: float = 2.0) -> Indicator:
    ...
@typing.overload
def SMA(n: int, m: IndParam) -> Indicator:
    ...
@typing.overload
def SMA(n: IndParam, m: float = 2.0) -> Indicator:
    ...
@typing.overload
def SMA(n: IndParam, m: IndParam) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: int = 22, m: float = 2.0) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: int, m: IndParam) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: IndParam, m: float = 2.0) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: IndParam, m: IndParam) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: int, m: Indicator) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: Indicator, m: float = 2.0) -> Indicator:
    ...
@typing.overload
def SMA(data: Indicator, n: Indicator, m: Indicator) -> Indicator:
    """
    SMA([data, n=22, m=2])
    
        求移动平均
    
        用法：若Y=SMA(X,N,M) 则 Y=[M*X+(N-M)*Y')/N,其中Y'表示上一周期Y值
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :param float|Indicator|IndParam m: 系数
        :rtype: Indicator
    """
@typing.overload
def SPEARMAN(ref_ind: Indicator, n: int = 0, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def SPEARMAN(ind: Indicator, ref_ind: Indicator, n: int = 0, fill_null: bool = True) -> Indicator:
    """
    SPEARMAN(ind, ref_ind[, n=0, fill_null=True])
    
        Spearman 相关系数。与 SPEARMAN(ref_ind, n)(ind) 等效。
    
        :param Indicator ind: 输入参数1
        :param Indicator ref_ind: 输入参数2
        :param int n: 滚动窗口(大于2 或 等于0)，等于0时，代表 n 实际使用 ind 的长度
        :param bool fill_null: 缺失数据使用 nan 填充; 否则使用小于对应日期且最接近对应日期的数据
    """
def SP_FixedPercent(p: float = 0.001) -> SlippageBase:
    """
    SP_FixedPercent([p=0.001])
    
        固定百分比移滑价差算法，买入实际价格 = 计划买入价格 * (1 + p)，卖出实际价格 = 计划卖出价格 * (1 - p)
    
        :param float p: 偏移的固定百分比
        :return: 移滑价差算法实例
    """
def SP_FixedValue(value: float = 0.01) -> SlippageBase:
    """
    SP_FixedValuet([p=0.001])
    
        固定价格移滑价差算法，买入实际价格 = 计划买入价格 + 偏移价格，卖出实际价格 = 计划卖出价格 - 偏移价格
    
        :param float p: 偏移价格
        :return: 移滑价差算法实例
    """
@typing.overload
def SQRT() -> Indicator:
    ...
@typing.overload
def SQRT(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def SQRT(arg0: float) -> Indicator:
    """
    SQRT([data])
    
        开平方
    
        用法：SQRT(X)为X的平方根
    
        例如：SQRT(CLOSE)收盘价的平方根
    
        :param data: 输入数据
        :rtype: Indicator
    """
@typing.overload
def STDEV(n: int = 10) -> Indicator:
    ...
@typing.overload
def STDEV(n: IndParam) -> Indicator:
    ...
@typing.overload
def STDEV(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def STDEV(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def STDEV(data: Indicator, n: int = 10) -> Indicator:
    """
    STDEV([data, n=10])
    
        计算N周期内样本标准差
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def STDP(n: int = 10) -> Indicator:
    ...
@typing.overload
def STDP(n: IndParam) -> Indicator:
    ...
@typing.overload
def STDP(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def STDP(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def STDP(data: Indicator, n: int = 10) -> Indicator:
    """
    STDP([data, n=10])
    
        总体标准差，STDP(X,N)为X的N日总体标准差
    
        :param data: 输入数据
        :param int n: 时间窗口
        :rtype: Indicator
    """
def ST_FixedPercent(p: float = 0.03) -> StoplossBase:
    """
    ST_FixedPercent([p=0.03])
    
        固定百分比止损策略，即当价格低于买入价格的某一百分比时止损
    
        :param float p: 百分比(0,1]
        :return: 止损/止赢策略实例
    """
def ST_Indicator(ind: Indicator) -> StoplossBase:
    """
    ST_Indicator(ind)
    
        使用技术指标作为止损价。如使用10日EMA作为止损：::
    
            ST_Indicator(EMA(CLOSE(), n=10))
    
        :param Indicator ind:
        :return: 止损/止赢策略实例
    """
def ST_Saftyloss(n1: int = 10, n2: int = 3, p: float = 2.0) -> StoplossBase:
    """
    ST_Saftyloss([n1=10, n2=3, p=2.0])
    
        参见《走进我的交易室》（2007年 地震出版社） 亚历山大.艾尔德(Alexander Elder) P202
        计算说明：在回溯周期内（一般为10到20天），将所有向下穿越的长度相加除以向下穿越的次数，
        得到噪音均值（即回溯期内所有最低价低于前一日最低价的长度除以次数），并用今日
        最低价减去（前日噪音均值乘以一个倍数）得到该止损线。为了抵消波动并且保证止损线的
        上移，在上述结果的基础上再取起N日（一般为3天）内的最高值
    
        :param int n1: 计算平均噪音的回溯时间窗口，默认为10天
        :param int n2: 对初步止损线去n2日内的最高值，默认为3
        :param double p: 噪音系数，默认为2
        :return: 止损/止赢策略实例
    """
@typing.overload
def SUM(n: int = 20) -> Indicator:
    ...
@typing.overload
def SUM(n: IndParam) -> Indicator:
    ...
@typing.overload
def SUM(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def SUM(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def SUM(data: Indicator, n: int = 20) -> Indicator:
    """
    SUM([data, n=20])
    
        求总和。SUM(X,N),统计N周期中X的总和,N=0则从第一个有效值开始。
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def SUMBARS(a: float) -> Indicator:
    ...
@typing.overload
def SUMBARS(a: IndParam) -> Indicator:
    ...
@typing.overload
def SUMBARS(data: Indicator, a: IndParam) -> Indicator:
    ...
@typing.overload
def SUMBARS(data: Indicator, a: Indicator) -> Indicator:
    ...
@typing.overload
def SUMBARS(data: Indicator, a: float) -> Indicator:
    """
    SUMBARS([data,] a)
    
        累加到指定周期数, 向前累加到指定值到现在的周期数
    
        用法：SUMBARS(X,A):将X向前累加直到大于等于A,返回这个区间的周期数
    
        例如：SUMBARS(VOL,CAPITAL)求完全换手到现在的周期数
    
        :param Indicator data: 输入数据
        :param float a|Indicator|IndParam: 指定累加和
        :rtype: Indicator
    """
def SYS_Simple(tm: typing.Any = None, mm: typing.Any = None, ev: typing.Any = None, cn: typing.Any = None, sg: typing.Any = None, st: typing.Any = None, tp: typing.Any = None, pg: typing.Any = None, sp: typing.Any = None) -> System:
    """
    SYS_Simple([tm=None, mm=None, ev=None, cn=None, sg=None, st=None, tp=None, pg=None, sp=None])
    
      创建简单系统实例（每次交易不进行多次加仓或减仓，即每次买入后在卖出时全部卖出），  系统实例在运行时(调用run方法），至少需要一个配套的交易管理实例、一个资金管理策略
      和一个信号指示器），可以在创建系统实例后进行指定。如果出现调用run时没有任何输出，
      且没有正确结果的时候，可能是未设置tm、sg、mm。进行回测时，使用 run 方法，如::
        
            #创建模拟交易账户进行回测，初始资金30万
            my_tm = crtTM(init_cash = 300000)
    
            #创建信号指示器（以5日EMA为快线，5日EMA自身的10日EMA作为慢线，快线向上穿越慢线时买入，反之卖出）
            my_sg = SG_Flex(EMA(C, n=5), slow_n=10)
    
            #固定每次买入1000股
            my_mm = MM_FixedCount(1000)
    
            #创建交易系统并运行
            sys = SYS_Simple(tm = my_tm, sg = my_sg, mm = my_mm)
            sys.run(sm['sz000001'], Query(-150))
        
        :param TradeManager tm: 交易管理实例 
        :param MoneyManager mm: 资金管理策略
        :param EnvironmentBase ev: 市场环境判断策略
        :param ConditionBase cn: 系统有效条件
        :param SignalBase sg: 信号指示器
        :param StoplossBase st: 止损策略
        :param StoplossBase tp: 止盈策略
        :param ProfitGoalBase pg: 盈利目标策略
        :param SlippageBase sp: 移滑价差算法
        :return: system实例
    """
def SYS_WalkForward(sys_list: typing.Sequence, tm: TradeManager = None, train_len: int = 100, test_len: int = 20, se: SelectorBase = None, train_tm: TradeManager = None) -> System:
    """
    SYS_WalkForward(sys_list, tm, train_len, test_len, train_tm)
    
      创建滚动寻优系统，当输入的候选系统列表中仅有一个候选系统时，即为滚动系统
    
      :param sequence sys_list: 候选系统列表
      :param TradeManager tm: 交易账户
      :param int train_len: 滚动评估系统绩效时使用的数据长度
      :param int test_len: 使用在 train_len 中选出的最优系统执行的数据长度
      :param SelectorBase se: 寻优选择器，默认为按“帐户平均年收益率%”最大选择
      :param TradeManager train_tm: 滚动评估时使用的交易账户, 为None时, 使用 tm 的拷贝进行评估
    """
def Seconds(arg0: int) -> TimeDelta:
    """
    Seconds(secs)
    
          TimeDelta 快捷创建函数
    
          :param int secs: 秒数
          :rtype: TimeDelta
    """
@typing.overload
def TAN() -> Indicator:
    ...
@typing.overload
def TAN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TAN(arg0: float) -> Indicator:
    """
    TAN([data])
    
        正切值
    
        :param Indicator data: 输入数据
        :rtype: Indicato
    """
@typing.overload
def TA_ACCBANDS(n: int = 20) -> Indicator:
    ...
@typing.overload
def TA_ACCBANDS(data: KData, n: int = 20) -> Indicator:
    """
    TA_ACCBANDS - Acceleration Bands
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_ACOS() -> Indicator:
    ...
@typing.overload
def TA_ACOS(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ACOS(arg0: float) -> Indicator:
    """
    TA_ACOS - Vector Trigonometric ACos
    """
@typing.overload
def TA_AD() -> Indicator:
    ...
@typing.overload
def TA_AD(data: KData) -> Indicator:
    """
    TA_AD - Chaikin A/D Line
    """
@typing.overload
def TA_ADD(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_ADD(ind1: Indicator, ind2: Indicator, fill_null: bool = True) -> Indicator:
    """
    TA_ADD - Vector Arithmetic Add
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    """
@typing.overload
def TA_ADOSC(fast_n: int = 3, slow_n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_ADOSC(data: KData, fast_n: int = 3, slow_n: int = 10) -> Indicator:
    """
    TA_ADOSC - Chaikin A/D Oscillator
    
    :param KData data: input KData
    :param int fast_n: Number of period for the fast MA (From 2 to 100000)
    :param int slow_n: Number of period for the slow MA (From 2 to 100000)
    """
@typing.overload
def TA_ADX(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_ADX(data: KData, n: int = 14) -> Indicator:
    """
    TA_ADX - Average Directional Movement Index
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_ADXR(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_ADXR(data: KData, n: int = 14) -> Indicator:
    """
    TA_ADXR - Average Directional Movement Index Rating
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_APO(fast_n: int = 12, slow_n: int = 26, matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_APO(data: Indicator, fast_n: int = 12, slow_n: int = 26, matype: int = 0) -> Indicator:
    """
    TA_APO - Absolute Price Oscillator
    
    :param Indicator data: input data
    :param int fast_n: Number of period for the fast MA (From 2 to 100000)
    :param int slow_n: Number of period for the slow MA (From 2 to 100000)    
    :param int matype: Type of Moving Average
    """
@typing.overload
def TA_AROON(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_AROON(data: KData, n: int = 14) -> Indicator:
    """
    TA_AROON - Aroon
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    :return: result(0) - Aroon down
             result(2) - Aroon up
    """
@typing.overload
def TA_AROONOSC(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_AROONOSC(data: KData, n: int = 14) -> Indicator:
    """
    TA_AROONOSC - Aroon Oscillator
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_ASIN() -> Indicator:
    ...
@typing.overload
def TA_ASIN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ASIN(arg0: float) -> Indicator:
    """
    TA_ASIN - Vector Trigonometric ASin
    """
@typing.overload
def TA_ATAN() -> Indicator:
    ...
@typing.overload
def TA_ATAN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ATAN(arg0: float) -> Indicator:
    """
    TA_ATAN - Vector Trigonometric ATan
    """
@typing.overload
def TA_ATR(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_ATR(data: KData, n: int = 14) -> Indicator:
    """
    TA_ATR - Average True Range
        
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_AVGDEV(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_AVGDEV(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_AVGDEV(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_AVGDEV(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_AVGDEV(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_AVGDEV - Average Deviation
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_AVGPRICE() -> Indicator:
    ...
@typing.overload
def TA_AVGPRICE(data: KData) -> Indicator:
    """
    TA_AVGPRICE - Average Price
    """
@typing.overload
def TA_BBANDS(n: int = 5, nbdevup: float = 2.0, nbdevdn: float = 2.0, matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_BBANDS(data: Indicator, n: int = 5, nbdevup: float = 2.0, nbdevdn: float = 2.0, matype: int = 0) -> Indicator:
    """
    TA_BBANDS - Bollinger Bands
              
    :param Indicator data: input data
    :param int n: Number of periode (From 1 to 100000)
    :param float nbdevup: Deviation multiplier for upper band
    :param float nbdevdn: Deviation multiplier for lower band
    :rtype: 具有三个结果集的 Indicator
    
        * result(0): Upper Band
        * result(1): Middle Band
        * result(2): Lower Band
    """
@typing.overload
def TA_BETA(n: int = 5, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_BETA(ind1: Indicator, ind2: Indicator, n: int = 5, fill_null: bool = True) -> Indicator:
    """
    TA_BETA - Beta
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    :param int n: Number of periode (From 1 to 100000)
    """
@typing.overload
def TA_BOP() -> Indicator:
    ...
@typing.overload
def TA_BOP(data: KData) -> Indicator:
    """
    TA_BOP - Balance Of Power
    """
@typing.overload
def TA_CCI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_CCI(data: KData, n: int = 14) -> Indicator:
    """
    TA_CCI - Commodity Channel Index
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_CDL2CROWS() -> Indicator:
    ...
@typing.overload
def TA_CDL2CROWS(data: KData) -> Indicator:
    """
    TA_CDL2CROWS - Two Crows
    """
@typing.overload
def TA_CDL3BLACKCROWS() -> Indicator:
    ...
@typing.overload
def TA_CDL3BLACKCROWS(data: KData) -> Indicator:
    """
    TA_CDL3BLACKCROWS - Three Black Crows
    """
@typing.overload
def TA_CDL3INSIDE() -> Indicator:
    ...
@typing.overload
def TA_CDL3INSIDE(data: KData) -> Indicator:
    """
    TA_CDL3INSIDE - Three Inside Up/Down
    """
@typing.overload
def TA_CDL3LINESTRIKE() -> Indicator:
    ...
@typing.overload
def TA_CDL3LINESTRIKE(data: KData) -> Indicator:
    """
    TA_CDL3LINESTRIKE - Three-Line Strike
    """
@typing.overload
def TA_CDL3OUTSIDE() -> Indicator:
    ...
@typing.overload
def TA_CDL3OUTSIDE(data: KData) -> Indicator:
    """
    TA_CDL3OUTSIDE - Three Outside Up/Down
    """
@typing.overload
def TA_CDL3STARSINSOUTH() -> Indicator:
    ...
@typing.overload
def TA_CDL3STARSINSOUTH(data: KData) -> Indicator:
    """
    TA_CDL3STARSINSOUTH - Three Stars In The South
    """
@typing.overload
def TA_CDL3WHITESOLDIERS() -> Indicator:
    ...
@typing.overload
def TA_CDL3WHITESOLDIERS(data: KData) -> Indicator:
    """
    TA_CDL3WHITESOLDIERS - Three Advancing White Soldiers
    """
@typing.overload
def TA_CDLABANDONEDBABY(penetration: float = 0.3) -> Indicator:
    ...
@typing.overload
def TA_CDLABANDONEDBABY(data: KData, penetration: float = 0.3) -> Indicator:
    """
    TA_CDLABANDONEDBABY - Abandoned Baby
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLADVANCEBLOCK() -> Indicator:
    ...
@typing.overload
def TA_CDLADVANCEBLOCK(data: KData) -> Indicator:
    """
    TA_CDLADVANCEBLOCK - Advance Block
    """
@typing.overload
def TA_CDLBELTHOLD() -> Indicator:
    ...
@typing.overload
def TA_CDLBELTHOLD(data: KData) -> Indicator:
    """
    TA_CDLBELTHOLD - Belt-hold
    """
@typing.overload
def TA_CDLBREAKAWAY() -> Indicator:
    ...
@typing.overload
def TA_CDLBREAKAWAY(data: KData) -> Indicator:
    """
    TA_CDLBREAKAWAY - Breakaway
    """
@typing.overload
def TA_CDLCLOSINGMARUBOZU() -> Indicator:
    ...
@typing.overload
def TA_CDLCLOSINGMARUBOZU(data: KData) -> Indicator:
    """
    TA_CDLCLOSINGMARUBOZU - Closing Marubozu
    """
@typing.overload
def TA_CDLCONCEALBABYSWALL() -> Indicator:
    ...
@typing.overload
def TA_CDLCONCEALBABYSWALL(data: KData) -> Indicator:
    """
    TA_CDLCONCEALBABYSWALL - Concealing Baby Swallow
    """
@typing.overload
def TA_CDLCOUNTERATTACK() -> Indicator:
    ...
@typing.overload
def TA_CDLCOUNTERATTACK(data: KData) -> Indicator:
    """
    TA_CDLCOUNTERATTACK - Counterattack
    """
@typing.overload
def TA_CDLDARKCLOUDCOVER(penetration: float = 0.5) -> Indicator:
    ...
@typing.overload
def TA_CDLDARKCLOUDCOVER(data: KData, penetration: float = 0.5) -> Indicator:
    """
    TA_CDLDARKCLOUDCOVER - Dark Cloud Cover
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLDOJI() -> Indicator:
    ...
@typing.overload
def TA_CDLDOJI(data: KData) -> Indicator:
    """
    TA_CDLDOJI - Doji
    """
@typing.overload
def TA_CDLDOJISTAR() -> Indicator:
    ...
@typing.overload
def TA_CDLDOJISTAR(data: KData) -> Indicator:
    """
    TA_CDLDOJISTAR - Doji Star
    """
@typing.overload
def TA_CDLDRAGONFLYDOJI() -> Indicator:
    ...
@typing.overload
def TA_CDLDRAGONFLYDOJI(data: KData) -> Indicator:
    """
    TA_CDLDRAGONFLYDOJI - Dragonfly Doji
    """
@typing.overload
def TA_CDLENGULFING() -> Indicator:
    ...
@typing.overload
def TA_CDLENGULFING(data: KData) -> Indicator:
    """
    TA_CDLENGULFING - Engulfing Pattern
    """
@typing.overload
def TA_CDLEVENINGDOJISTAR(penetration: float = 0.3) -> Indicator:
    ...
@typing.overload
def TA_CDLEVENINGDOJISTAR(data: KData, penetration: float = 0.3) -> Indicator:
    """
    TA_CDLEVENINGDOJISTAR - Evening Doji Star
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLEVENINGSTAR(penetration: float = 0.3) -> Indicator:
    ...
@typing.overload
def TA_CDLEVENINGSTAR(data: KData, penetration: float = 0.3) -> Indicator:
    """
    TA_CDLEVENINGSTAR - Evening Star
                
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLGAPSIDESIDEWHITE() -> Indicator:
    ...
@typing.overload
def TA_CDLGAPSIDESIDEWHITE(data: KData) -> Indicator:
    """
    TA_CDLGAPSIDESIDEWHITE - Up/Down-gap side-by-side white lines
    """
@typing.overload
def TA_CDLGRAVESTONEDOJI() -> Indicator:
    ...
@typing.overload
def TA_CDLGRAVESTONEDOJI(data: KData) -> Indicator:
    """
    TA_CDLGRAVESTONEDOJI - Gravestone Doji
    """
@typing.overload
def TA_CDLHAMMER() -> Indicator:
    ...
@typing.overload
def TA_CDLHAMMER(data: KData) -> Indicator:
    """
    TA_CDLHAMMER - Hammer
    """
@typing.overload
def TA_CDLHANGINGMAN() -> Indicator:
    ...
@typing.overload
def TA_CDLHANGINGMAN(data: KData) -> Indicator:
    """
    TA_CDLHANGINGMAN - Hanging Man
    """
@typing.overload
def TA_CDLHARAMI() -> Indicator:
    ...
@typing.overload
def TA_CDLHARAMI(data: KData) -> Indicator:
    """
    TA_CDLHARAMI - Harami Pattern
    """
@typing.overload
def TA_CDLHARAMICROSS() -> Indicator:
    ...
@typing.overload
def TA_CDLHARAMICROSS(data: KData) -> Indicator:
    """
    TA_CDLHARAMICROSS - Harami Cross Pattern
    """
@typing.overload
def TA_CDLHIGHWAVE() -> Indicator:
    ...
@typing.overload
def TA_CDLHIGHWAVE(data: KData) -> Indicator:
    """
    TA_CDLHIGHWAVE - High-Wave Candle
    """
@typing.overload
def TA_CDLHIKKAKE() -> Indicator:
    ...
@typing.overload
def TA_CDLHIKKAKE(data: KData) -> Indicator:
    """
    TA_CDLHIKKAKE - Hikkake Pattern
    """
@typing.overload
def TA_CDLHIKKAKEMOD() -> Indicator:
    ...
@typing.overload
def TA_CDLHIKKAKEMOD(data: KData) -> Indicator:
    """
    TA_CDLHIKKAKEMOD - Modified Hikkake Pattern
    """
@typing.overload
def TA_CDLHOMINGPIGEON() -> Indicator:
    ...
@typing.overload
def TA_CDLHOMINGPIGEON(data: KData) -> Indicator:
    """
    TA_CDLHOMINGPIGEON - Homing Pigeon
    """
@typing.overload
def TA_CDLIDENTICAL3CROWS() -> Indicator:
    ...
@typing.overload
def TA_CDLIDENTICAL3CROWS(data: KData) -> Indicator:
    """
    TA_CDLIDENTICAL3CROWS - Identical Three Crows
    """
@typing.overload
def TA_CDLINNECK() -> Indicator:
    ...
@typing.overload
def TA_CDLINNECK(data: KData) -> Indicator:
    """
    TA_CDLINNECK - In-Neck Pattern
    """
@typing.overload
def TA_CDLINVERTEDHAMMER() -> Indicator:
    ...
@typing.overload
def TA_CDLINVERTEDHAMMER(data: KData) -> Indicator:
    """
    TA_CDLINVERTEDHAMMER - Inverted Hammer
    """
@typing.overload
def TA_CDLKICKING() -> Indicator:
    ...
@typing.overload
def TA_CDLKICKING(data: KData) -> Indicator:
    """
    TA_CDLKICKING - Kicking
    """
@typing.overload
def TA_CDLKICKINGBYLENGTH() -> Indicator:
    ...
@typing.overload
def TA_CDLKICKINGBYLENGTH(data: KData) -> Indicator:
    """
    TA_CDLKICKINGBYLENGTH - Kicking - bull/bear determined by the longer marubozu
    """
@typing.overload
def TA_CDLLADDERBOTTOM() -> Indicator:
    ...
@typing.overload
def TA_CDLLADDERBOTTOM(data: KData) -> Indicator:
    """
    TA_CDLLADDERBOTTOM - Ladder Bottom
    """
@typing.overload
def TA_CDLLONGLEGGEDDOJI() -> Indicator:
    ...
@typing.overload
def TA_CDLLONGLEGGEDDOJI(data: KData) -> Indicator:
    """
    TA_CDLLONGLEGGEDDOJI - Long Legged Doji
    """
@typing.overload
def TA_CDLLONGLINE() -> Indicator:
    ...
@typing.overload
def TA_CDLLONGLINE(data: KData) -> Indicator:
    """
    TA_CDLLONGLINE - Long Line Candle
    """
@typing.overload
def TA_CDLMARUBOZU() -> Indicator:
    ...
@typing.overload
def TA_CDLMARUBOZU(data: KData) -> Indicator:
    """
    TA_CDLMARUBOZU - Marubozu
    """
@typing.overload
def TA_CDLMATCHINGLOW() -> Indicator:
    ...
@typing.overload
def TA_CDLMATCHINGLOW(data: KData) -> Indicator:
    """
    TA_CDLMATCHINGLOW - Matching Low
    """
@typing.overload
def TA_CDLMATHOLD(penetration: float = 0.5) -> Indicator:
    ...
@typing.overload
def TA_CDLMATHOLD(data: KData, penetration: float = 0.5) -> Indicator:
    """
    TA_CDLMATHOLD - Mat Hold
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLMORNINGDOJISTAR(penetration: float = 0.3) -> Indicator:
    ...
@typing.overload
def TA_CDLMORNINGDOJISTAR(data: KData, penetration: float = 0.3) -> Indicator:
    """
    TA_CDLMORNINGDOJISTAR - Morning Doji Star
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLMORNINGSTAR(penetration: float = 0.3) -> Indicator:
    ...
@typing.overload
def TA_CDLMORNINGSTAR(data: KData, penetration: float = 0.3) -> Indicator:
    """
    TA_CDLMORNINGSTAR - Morning Star
    
    :param KData data: input KData
    :param float penetration: Percentage of penetration of a candle within another candle (>=0)
    """
@typing.overload
def TA_CDLONNECK() -> Indicator:
    ...
@typing.overload
def TA_CDLONNECK(data: KData) -> Indicator:
    """
    TA_CDLONNECK - On-Neck Pattern
    """
@typing.overload
def TA_CDLPIERCING() -> Indicator:
    ...
@typing.overload
def TA_CDLPIERCING(data: KData) -> Indicator:
    """
    TA_CDLPIERCING - Piercing Pattern
    """
@typing.overload
def TA_CDLRICKSHAWMAN() -> Indicator:
    ...
@typing.overload
def TA_CDLRICKSHAWMAN(data: KData) -> Indicator:
    """
    TA_CDLRICKSHAWMAN - Rickshaw Man
    """
@typing.overload
def TA_CDLRISEFALL3METHODS() -> Indicator:
    ...
@typing.overload
def TA_CDLRISEFALL3METHODS(data: KData) -> Indicator:
    """
    TA_CDLRISEFALL3METHODS - Rising/Falling Three Methods
    """
@typing.overload
def TA_CDLSEPARATINGLINES() -> Indicator:
    ...
@typing.overload
def TA_CDLSEPARATINGLINES(data: KData) -> Indicator:
    """
    TA_CDLSEPARATINGLINES - Separating Lines
    """
@typing.overload
def TA_CDLSHOOTINGSTAR() -> Indicator:
    ...
@typing.overload
def TA_CDLSHOOTINGSTAR(data: KData) -> Indicator:
    """
    TA_CDLSHOOTINGSTAR - Shooting Star
    """
@typing.overload
def TA_CDLSHORTLINE() -> Indicator:
    ...
@typing.overload
def TA_CDLSHORTLINE(data: KData) -> Indicator:
    """
    TA_CDLSHORTLINE - Short Line Candle
    """
@typing.overload
def TA_CDLSPINNINGTOP() -> Indicator:
    ...
@typing.overload
def TA_CDLSPINNINGTOP(data: KData) -> Indicator:
    """
    TA_CDLSPINNINGTOP - Spinning Top
    """
@typing.overload
def TA_CDLSTALLEDPATTERN() -> Indicator:
    ...
@typing.overload
def TA_CDLSTALLEDPATTERN(data: KData) -> Indicator:
    """
    TA_CDLSTALLEDPATTERN - Stalled Pattern
    """
@typing.overload
def TA_CDLSTICKSANDWICH() -> Indicator:
    ...
@typing.overload
def TA_CDLSTICKSANDWICH(data: KData) -> Indicator:
    """
    TA_CDLSTICKSANDWICH - Stick Sandwich
    """
@typing.overload
def TA_CDLTAKURI() -> Indicator:
    ...
@typing.overload
def TA_CDLTAKURI(data: KData) -> Indicator:
    """
    TA_CDLTAKURI - Takuri (Dragonfly Doji with very long lower shadow)
    """
@typing.overload
def TA_CDLTASUKIGAP() -> Indicator:
    ...
@typing.overload
def TA_CDLTASUKIGAP(data: KData) -> Indicator:
    """
    TA_CDLTASUKIGAP - Tasuki Gap
    """
@typing.overload
def TA_CDLTHRUSTING() -> Indicator:
    ...
@typing.overload
def TA_CDLTHRUSTING(data: KData) -> Indicator:
    """
    TA_CDLTHRUSTING - Thrusting Pattern
    """
@typing.overload
def TA_CDLTRISTAR() -> Indicator:
    ...
@typing.overload
def TA_CDLTRISTAR(data: KData) -> Indicator:
    """
    TA_CDLTRISTAR - Tristar Pattern
    """
@typing.overload
def TA_CDLUNIQUE3RIVER() -> Indicator:
    ...
@typing.overload
def TA_CDLUNIQUE3RIVER(data: KData) -> Indicator:
    """
    TA_CDLUNIQUE3RIVER - Unique 3 River
    """
@typing.overload
def TA_CDLUPSIDEGAP2CROWS() -> Indicator:
    ...
@typing.overload
def TA_CDLUPSIDEGAP2CROWS(data: KData) -> Indicator:
    """
    TA_CDLUPSIDEGAP2CROWS - Upside Gap Two Crows
    """
@typing.overload
def TA_CDLXSIDEGAP3METHODS() -> Indicator:
    ...
@typing.overload
def TA_CDLXSIDEGAP3METHODS(data: KData) -> Indicator:
    """
    TA_CDLXSIDEGAP3METHODS - Upside/Downside Gap Three Methods
    """
@typing.overload
def TA_CEIL() -> Indicator:
    ...
@typing.overload
def TA_CEIL(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_CEIL(arg0: float) -> Indicator:
    """
    TA_CEIL - Vector Ceil
    """
@typing.overload
def TA_CMO(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_CMO(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_CMO(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_CMO(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_CMO(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_CMO - Chande Momentum Oscillator
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_CORREL(n: int = 30, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_CORREL(ind1: Indicator, ind2: Indicator, n: int = 30, fill_null: bool = True) -> Indicator:
    """
    TA_CORREL - Pearson's Correlation Coefficient (r)
        
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    :param int n: Number of periode (From 1 to 100000)
    """
@typing.overload
def TA_COS() -> Indicator:
    ...
@typing.overload
def TA_COS(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_COS(arg0: float) -> Indicator:
    """
    TA_COS - Vector Trigonometric Cos
    """
@typing.overload
def TA_COSH() -> Indicator:
    ...
@typing.overload
def TA_COSH(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_COSH(arg0: float) -> Indicator:
    """
    TA_COSH - Vector Trigonometric Cosh
    """
@typing.overload
def TA_DEMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_DEMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_DEMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_DEMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_DEMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_DEMA - Double Exponential Moving Average
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_DIV(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_DIV(ind1: Indicator, ind2: Indicator, fill_null: bool = True) -> Indicator:
    """
    TA_DIV - Vector Arithmetic Div
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    """
@typing.overload
def TA_DX(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_DX(data: KData, n: int = 14) -> Indicator:
    """
    TA_DX - Directional Movement Index
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_EMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_EMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_EMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_EMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_EMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_EMA - Exponential Moving Average
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_EXP() -> Indicator:
    ...
@typing.overload
def TA_EXP(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_EXP(arg0: float) -> Indicator:
    """
    TA_EXP - Vector Arithmetic Exp
    """
@typing.overload
def TA_FLOOR() -> Indicator:
    ...
@typing.overload
def TA_FLOOR(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_FLOOR(arg0: float) -> Indicator:
    """
    TA_FLOOR - Vector Floor
    """
@typing.overload
def TA_HT_DCPERIOD() -> Indicator:
    ...
@typing.overload
def TA_HT_DCPERIOD(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_DCPERIOD(arg0: float) -> Indicator:
    """
    TA_HT_DCPERIOD - Hilbert Transform - Dominant Cycle Period
    """
@typing.overload
def TA_HT_DCPHASE() -> Indicator:
    ...
@typing.overload
def TA_HT_DCPHASE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_DCPHASE(arg0: float) -> Indicator:
    """
    TA_HT_DCPHASE - Hilbert Transform - Dominant Cycle Phase
    """
@typing.overload
def TA_HT_PHASOR() -> Indicator:
    ...
@typing.overload
def TA_HT_PHASOR(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_PHASOR(arg0: float) -> Indicator:
    """
    TA_HT_PHASOR - Hilbert Transform - Phasor Components
    
    :return: result(0) - outInPhase
             result(1) - outQuadrature
    """
@typing.overload
def TA_HT_SINE() -> Indicator:
    ...
@typing.overload
def TA_HT_SINE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_SINE(arg0: float) -> Indicator:
    """
    TA_HT_SINE - Hilbert Transform - SineWave
    :return: result(0) - outSine
             result(1) - outLeadSine
    """
@typing.overload
def TA_HT_TRENDLINE() -> Indicator:
    ...
@typing.overload
def TA_HT_TRENDLINE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_TRENDLINE(arg0: float) -> Indicator:
    """
    TA_HT_TRENDLINE - Hilbert Transform - Instantaneous Trendline
    """
@typing.overload
def TA_HT_TRENDMODE() -> Indicator:
    ...
@typing.overload
def TA_HT_TRENDMODE(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_HT_TRENDMODE(arg0: float) -> Indicator:
    """
    TA_HT_TRENDMODE - Hilbert Transform - Trend vs Cycle Mode
    """
@typing.overload
def TA_IMI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_IMI(data: KData, n: int = 14) -> Indicator:
    """
    TA_IMI - Intraday Momentum Index
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_KAMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_KAMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_KAMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_KAMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_KAMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_KAMA - Kaufman Adaptive Moving Average
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_LINEARREG(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_LINEARREG - Linear Regression
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_LINEARREG_ANGLE(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_ANGLE(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_ANGLE(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_ANGLE(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_ANGLE(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_LINEARREG_ANGLE - Linear Regression Angle
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_LINEARREG_INTERCEPT(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_INTERCEPT(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_INTERCEPT(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_INTERCEPT(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_INTERCEPT(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_LINEARREG_INTERCEPT - Linear Regression Intercept
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_LINEARREG_SLOPE(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_SLOPE(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_SLOPE(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_SLOPE(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LINEARREG_SLOPE(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_LINEARREG_SLOPE - Linear Regression Slope
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_LN() -> Indicator:
    ...
@typing.overload
def TA_LN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LN(arg0: float) -> Indicator:
    """
    TA_LN - Vector Log Natural
    """
@typing.overload
def TA_LOG10() -> Indicator:
    ...
@typing.overload
def TA_LOG10(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_LOG10(arg0: float) -> Indicator:
    """
    TA_LOG10 - Vector Log10
    """
@typing.overload
def TA_MA(n: int = 30, matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_MA(data: Indicator, n: int = 30, matype: int = 0) -> Indicator:
    """
    TA_MA - Moving average
    
    :param Indicator data: input data
    :param int n: Number of periode (From 1 to 100000)
    :param int matype: Type of Moving Average
    """
@typing.overload
def TA_MACD(fast_n: int = 12, slow_n: int = 26, signal_n: int = 9) -> Indicator:
    ...
@typing.overload
def TA_MACD(data: Indicator, fast_n: int = 30, slow_n: int = 26, signal_n: int = 9) -> Indicator:
    """
    TA_MACD - Moving Average Convergence/Divergence
    
    :param Indicator data: input data
    :param int fast_n: Number of periode for fast MA (From 2 to 100000)
    :param int slow_n: Number of periode for slow MA (From 2 to 100000)
    :param int signal_n: Smoothing for the signal line (nb of period) (From 1 to 100000)
    """
@typing.overload
def TA_MACDEXT(fast_n: int = 12, slow_n: int = 26, signal_n: int = 9, fast_matype: int = 0, slow_matype: int = 0, signal_matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_MACDEXT(data: Indicator, fast_n: int = 30, slow_n: int = 26, signal_n: int = 9, fast_matype: int = 0, slow_matype: int = 0, signal_matype: int = 0) -> Indicator:
    """
    TA_MACDEXT - MACD with controllable MA type
    
    :param Indicator data: input data
    :param int fast_n: Number of periode for fast MA (From 2 to 100000)
    :param int slow_n: Number of periode for slow MA (From 2 to 100000)
    :param int signal_n: Smoothing for the signal line (nb of period) (From 1 to 100000)
    :param int fast_matype: Type of Moving Average for fast MA
    :param int slow_matype: Type of Moving Average for slow MA
    :param int signal_matype: Type of Moving Average for signal line
    """
@typing.overload
def TA_MACDFIX(n: int = 9) -> Indicator:
    ...
@typing.overload
def TA_MACDFIX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MACDFIX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MACDFIX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MACDFIX(data: Indicator, n: int = 9) -> Indicator:
    """
    TA_MACDFIX - Moving Average Convergence/Divergence Fix 12/26
    
    :param Indicator data: input data
    :param int n: Smoothing for the signal line (nb of period) (From 1 to 100000)
    :return: result(0) - outMACD
             result(1) - outMACDSignal
             result(2) - outMACDHist
    """
@typing.overload
def TA_MAMA(fast_limit: float = 0.5, slow_limit: float = 0.05) -> Indicator:
    ...
@typing.overload
def TA_MAMA(data: Indicator, fast_limit: float = 0.5, slow_limit: float = 0.05) -> Indicator:
    """
    TA_MAMA - MESA Adaptive Moving Average
    
    :param Indicator data: input data
    :param float fast_limit: Fast limit (From 0.01 to 0.99)
    :param float slow_limit: Slow limit (From 0.01 to 0.99)
    """
@typing.overload
def TA_MAVP(ref_ind: Indicator, min_n: int = 2, max_n: int = 30, matype: int = 0, fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_MAVP(ind1: Indicator, ind2: Indicator, min_n: int = 2, max_n: int = 30, fill_null: int = True, matype: bool = 0) -> Indicator:
    """
    TA_MAVP - Moving average with variable period
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    :param int min_n: Value less than minimum will be changed to Minimum period (From 2 to 100000)
    :param int max_n: Value higher than maximum will be changed to Maximum period (From 2 to 100000)
    :param int matype: Type of Moving Average
    :param bool fill_null: 日期对齐时，缺失日期数据填充nan值
    """
@typing.overload
def TA_MAX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MAX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MAX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MAX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MAX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MAX - Highest value over a specified period
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MAXINDEX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MAXINDEX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MAXINDEX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MAXINDEX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MAXINDEX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MAXINDEX - Index of highest value over a specified period
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MEDPRICE() -> Indicator:
    ...
@typing.overload
def TA_MEDPRICE(data: KData) -> Indicator:
    """
    TA_MEDPRICE - Median Price
    """
@typing.overload
def TA_MFI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_MFI(data: KData, n: int = 14) -> Indicator:
    """
    TA_MFI - Money Flow Index
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MIDPOINT(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_MIDPOINT(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MIDPOINT(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MIDPOINT(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MIDPOINT(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_MIDPOINT - MidPoint over period
    
    :param Indicator data: input
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MIDPRICE(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_MIDPRICE(data: KData, n: int = 14) -> Indicator:
    """
    TA_MIDPRICE - Midpoint Price over period
    
    :param KData data: input KData
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MIN(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MIN(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MIN(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MIN(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MIN(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MIN - Lowest value over a specified period
    
    :param Indicator data: input
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MININDEX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MININDEX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MININDEX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MININDEX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MININDEX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MININDEX - Index of lowest value over a specified period
    
    :param Indicator data: input
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_MINMAX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MINMAX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MINMAX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MINMAX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MINMAX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MINMAX - Lowest and highest values over a specified period
    
    :param Indicator data: input
    :param int n: Number of period (From 2 to 100000)
    :return: result(0) - outMin
             result(1) - outMax
    """
@typing.overload
def TA_MINMAXINDEX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_MINMAXINDEX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MINMAXINDEX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MINMAXINDEX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MINMAXINDEX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_MINMAXINDEX - Indexes of lowest and highest values over a specified period
        
    :param Indicator data: input
    :param int n: Number of period (From 2 to 100000)
    :return: result(0) - outMinIdx
             result(1) - outMaxIdx
    """
@typing.overload
def TA_MINUS_DI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_MINUS_DI(data: KData, n: int = 14) -> Indicator:
    """
    A_MINUS_DI - Minus Directional Indicator
    
    :param KData data: input KData
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_MINUS_DM(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_MINUS_DM(data: KData, n: int = 14) -> Indicator:
    """
    TA_MINUS_DM - Minus Directional Movement
    
    :param KData data: input KData
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_MOM(n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_MOM(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MOM(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_MOM(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_MOM(data: Indicator, n: int = 10) -> Indicator:
    """
    TA_MOM - Momentum
        
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_MULT(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_MULT(ind1: Indicator, ind2: Indicator, fill_null: bool = True) -> Indicator:
    """
    TA_MULT - Vector Arithmetic Mult
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    """
@typing.overload
def TA_NATR(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_NATR(data: KData, n: int = 14) -> Indicator:
    """
    TA_NATR - Normalized Average True Range
    
    :param KData data: input KData
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_OBV() -> Indicator:
    ...
@typing.overload
def TA_OBV(data: KData) -> Indicator:
    """
    TA_OBV - On Balance Volume
    """
@typing.overload
def TA_PLUS_DI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_PLUS_DI(data: KData, n: int = 14) -> Indicator:
    """
    TA_PLUS_DI - Plus Directional Indicator
    
    :param KData data: input KData
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_PLUS_DM(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_PLUS_DM(data: KData, n: int = 14) -> Indicator:
    """
    TA_PLUS_DM - Plus Directional Movement
    
    :param KData data: input KData
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_PPO(fast_n: int = 12, slow_n: int = 26, matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_PPO(data: Indicator, fast_n: int = 12, slow_n: int = 26, matype: int = 0) -> Indicator:
    """
    TA_PPO - Percentage Price Oscillator
    
    :param Indicator data: input data
    :param int fast_n: Number of periode for fast MA (From 2 to 100000)
    :param int slow_n: Number of periode for slow MA (From 2 to 100000)
    :param int matype: Type of Moving Average
    """
@typing.overload
def TA_ROC(n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_ROC(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROC(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROC(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ROC(data: Indicator, n: int = 10) -> Indicator:
    """
    TA_ROC - Rate of change : ((price/prevPrice)-1)*100
        
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_ROCP(n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_ROCP(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCP(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCP(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ROCP(data: Indicator, n: int = 10) -> Indicator:
    """
    TA_ROCP - Rate of change Percentage: (price-prevPrice)/prevPrice
    
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_ROCR(n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_ROCR(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCR(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCR(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ROCR(data: Indicator, n: int = 10) -> Indicator:
    """
    TA_ROCR - Rate of change ratio: (price/prevPrice)
    
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_ROCR100(n: int = 10) -> Indicator:
    ...
@typing.overload
def TA_ROCR100(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCR100(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_ROCR100(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_ROCR100(data: Indicator, n: int = 10) -> Indicator:
    """
    TA_ROCR100 - Rate of change ratio 100 scale: (price/prevPrice)*100
    
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_RSI(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_RSI(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_RSI(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_RSI(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_RSI(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_RSI - Relative Strength Index
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_SAR(acceleration: float = 0.02, maximum: float = 0.2) -> Indicator:
    ...
@typing.overload
def TA_SAR(data: KData, acceleration: float = 0.02, maximum: float = 0.2) -> Indicator:
    """
    TA_SAR - Parabolic SAR
    
    :param KData data: input KData object
    :param float acceleration: Acceleration Factor used up to the Maximum value (>= 0.0)
    :param float maximum: Acceleration Factor Maximum value (>= 0.0)
    """
@typing.overload
def TA_SAREXT(startvalue: float = 0.0, offsetonreverse: float = 0.0, accelerationinitlong: float = 0.02, accelerationlong: float = 0.02, accelerationmaxlong: float = 0.2, accelerationinitshort: float = 0.02, accelerationshort: float = 0.02, accelerationmaxshort: float = 0.2) -> Indicator:
    ...
@typing.overload
def TA_SAREXT(data: KData, startvalue: float = 0.0, offsetonreverse: float = 0.0, accelerationinitlong: float = 0.02, accelerationlong: float = 0.02, accelerationmaxlong: float = 0.2, accelerationinitshort: float = 0.02, accelerationshort: float = 0.02, accelerationmaxshort: float = 0.2) -> Indicator:
    """
    TA_SAREXT - Parabolic SAR - Extended
    
    :param KData data: input KData object
    :param float startvalue: Start value and direction. 0 for Auto, >0 for Long, <0 for Short
    :param float offsetonreverse: Percent offset added/removed to initial stop on short/long reversal (>= 0.0)
    :param float accelerationinitlong: Acceleration Factor initial value for the Long direction (>= 0.0)
    :param float accelerationlong: Acceleration Factor for the Long direction (>= 0.0)
    :param float accelerationmaxlong: Acceleration Factor maximum value for the Long direction (>= 0.0)
    :param float accelerationinitshort: Acceleration Factor initial value for the Short direction (>= 0.0)
    :param float accelerationshort: Acceleration Factor for the Short direction (>= 0.0)
    :param float accelerationmaxshort: Acceleration Factor maximum value for the Short direction(>= 0.0)
    """
@typing.overload
def TA_SIN() -> Indicator:
    ...
@typing.overload
def TA_SIN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_SIN(arg0: float) -> Indicator:
    """
    TA_SIN - Vector Trigonometric Sin
    """
@typing.overload
def TA_SINH() -> Indicator:
    ...
@typing.overload
def TA_SINH(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_SINH(arg0: float) -> Indicator:
    """
    TA_SINH - Vector Trigonometric Sinh
    """
@typing.overload
def TA_SMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_SMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_SMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_SMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_SMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_SMA - Simple Moving Average
        
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_SQRT() -> Indicator:
    ...
@typing.overload
def TA_SQRT(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_SQRT(arg0: float) -> Indicator:
    """
    TA_SQRT - Vector Square Root
    """
@typing.overload
def TA_STDDEV(n: int = 5, nbdev: float = 1.0) -> Indicator:
    ...
@typing.overload
def TA_STDDEV(data: Indicator, n: int = 5, nbdev: float = 1.0) -> Indicator:
    """
    TA_STDDEV - Standard Deviation
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    :param float nbdev: Nb of deviations
    """
@typing.overload
def TA_STOCH(fastk_n: int = 5, slowk_n: int = 3, slowk_matype: int = 0, slowd_n: int = 3, slowd_matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_STOCH(data: KData, fastk_n: int = 5, slowk_n: int = 3, slowk_matype: int = 0, slowd_n: int = 3, slowd_matype: int = 0) -> Indicator:
    """
    TA_STOCH - Stochastic
              
    :parma KData data: KData object
    :param int fastk_n: Time period for building the Fast-K line (From 1 to 100000)
    :param int slowk_n: Smoothing for making the Slow-K line. Usually set to 3 (From 1 to 100000)
    :param int slowk_matype: Type of Moving Average for Slow K (From 0 to 8)
    :param int slowd_n: Smoothing for making the Slow-D line (From 1 to 100000)
    :param int slowd_matype: Type of Moving Average for Slow D (From 0 to 8))  
    :return: result0 - slow K 
             result1 - slow D
    """
@typing.overload
def TA_STOCHF(fastk_n: int = 5, fastd_n: int = 3, fastd_matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_STOCHF(data: KData, fastk_n: int = 5, fastd_n: int = 3, fastd_matype: int = 0) -> Indicator:
    """
    TA_STOCHF - Stochastic Fast
    
    :param KData data: input KData object
    :param int fastk_n: Time period for building the Fast-K line (From 1 to 100000)
    :param int fastd_n: Smoothing for making the Fast-D line. Usually set to 3 (From 1 to 100000)
    :param int fastd_matype: Type of Moving Average for Fast D (From 0 to 8))
    :return: result0 - fast K 
             result1 - fast D
    """
@typing.overload
def TA_STOCHRSI(n: int = 14, fastk_n: int = 5, fastd_n: int = 3, matype: int = 0) -> Indicator:
    ...
@typing.overload
def TA_STOCHRSI(data: Indicator, n: int = 14, fastk_n: int = 5, fastd_n: int = 3, matype: int = 0) -> Indicator:
    """
    TA_STOCHRSI - Stochastic Relative Strength Index
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    :param int fastk_n: Time period for building the Fast-K line (From 1 to 100000)
    :param int fastd_n: Smoothing for making the Fast-D line. Usually set to 3 (From 1 to 100000)
    :param int matype: Type of Moving Average for Fast D (From 0 to 8))
    :return: result0 - fast K 
             result1 - fast D
    """
@typing.overload
def TA_SUB(fill_null: bool = True) -> Indicator:
    ...
@typing.overload
def TA_SUB(ind1: Indicator, ind2: Indicator, fill_null: bool = True) -> Indicator:
    """
    TA_SUB - Vector Arithmetic Subtraction
    
    :param Indicator ind1: input1
    :param Indicator ind2: input2
    """
@typing.overload
def TA_SUM(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_SUM(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_SUM(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_SUM(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_SUM(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_SUM - Summation
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_T3(n: int = 5, vfactor: float = 0.7) -> Indicator:
    ...
@typing.overload
def TA_T3(data: Indicator, n: int = 5, vfactor: float = 0.7) -> Indicator:
    """
    TA_T3 - Triple Exponential Moving Average (T3)
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    :param float vfactor: Volume Factor (From 0 to 1)
    """
@typing.overload
def TA_TAN() -> Indicator:
    ...
@typing.overload
def TA_TAN(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TAN(arg0: float) -> Indicator:
    """
    TA_TAN - Vector Trigonometric Tan
    """
@typing.overload
def TA_TANH() -> Indicator:
    ...
@typing.overload
def TA_TANH(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TANH(arg0: float) -> Indicator:
    """
    TA_TANH - Vector Trigonometric Tanh
    """
@typing.overload
def TA_TEMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_TEMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TEMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TEMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TEMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_TEMA - Triple Exponential Moving Average
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_TRANGE() -> Indicator:
    ...
@typing.overload
def TA_TRANGE(data: KData) -> Indicator:
    """
    TA_TRANGE - True Range
    """
@typing.overload
def TA_TRIMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_TRIMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TRIMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TRIMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TRIMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_TRIMA - Triangular Moving Average
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_TRIX(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_TRIX(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TRIX(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TRIX(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TRIX(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_TRIX - 1-day Rate-Of-Change (ROC) of a Triple Smooth EMA
    
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    """
@typing.overload
def TA_TSF(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_TSF(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TSF(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_TSF(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_TSF(data: Indicator, n: int = 14) -> Indicator:
    """
    TA_TSF - Time Series Forecast
    
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_TYPPRICE() -> Indicator:
    ...
@typing.overload
def TA_TYPPRICE(data: KData) -> Indicator:
    """
    TA_TYPPRICE - Typical Price
    """
@typing.overload
def TA_ULTOSC(n1: int = 7, n2: int = 14, n3: int = 28) -> Indicator:
    ...
@typing.overload
def TA_ULTOSC(data: KData, n1: int = 7, n2: int = 14, n3: int = 28) -> Indicator:
    """
    TA_ULTOSC - Ultimate Oscillator
    
    :param KData data: input KData object
    :param int n1: Number of bars for 1st period (From 1 to 100000)
    :param int n2: Number of bars fro 2nd period (From 1 to 100000)
    :param int n3: Number of bars for 3rd period (From 1 to 100000)
    """
@typing.overload
def TA_VAR(n: int = 5, nbdev: float = 1.0) -> Indicator:
    ...
@typing.overload
def TA_VAR(data: Indicator, n: int = 5, nbdev: float = 1.0) -> Indicator:
    """
    TA_VAR - Variance
    
    :param Indicator data: input data
    :param int n: Number of period (From 1 to 100000)
    :param float nbdev: Nb of deviations
    """
@typing.overload
def TA_WCLPRICE() -> Indicator:
    ...
@typing.overload
def TA_WCLPRICE(data: KData) -> Indicator:
    """
    TA_WCLPRICE - Weighted Close Price
    """
@typing.overload
def TA_WILLR(n: int = 14) -> Indicator:
    ...
@typing.overload
def TA_WILLR(data: KData, n: int = 14) -> Indicator:
    """
    TA_WILLR - Williams' %R
        
    :param KData data: input KData object
    :param int n: Number of period (From 2 to 100000)
    """
@typing.overload
def TA_WMA(n: int = 30) -> Indicator:
    ...
@typing.overload
def TA_WMA(arg0: IndParam) -> Indicator:
    ...
@typing.overload
def TA_WMA(arg0: Indicator, arg1: IndParam) -> Indicator:
    ...
@typing.overload
def TA_WMA(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def TA_WMA(data: Indicator, n: int = 30) -> Indicator:
    """
    TA_WMA - Weighted Moving Average
        
    :param Indicator data: input data
    :param int n: Number of period (From 2 to 100000)
    """
def TC_FixedA(commission: float = 0.0018, lowest_commission: float = 5.0, stamptax: float = 0.001, transferfee: float = 0.001, lowest_transferfee: float = 1.0) -> TradeCostBase:
    """
    TC_FixedA([commission=0.0018, lowestCommission=5.0, stamptax=0.001, transferfee=0.001, lowestTransferfee=1.0])
    
        2015年8月1日之前的A股交易成本算法
    
        :param float commission: 佣金比例
        :param float lowestCommission: 最低佣金值
        :param float stamptax: 印花税
        :param float transferfee: 过户费
        :param float lowestTransferfee: 最低过户费
        :return: :py:class:`TradeCostBase` 子类实例
    """
def TC_FixedA2015(commission: float = 0.0018, lowest_commission: float = 5.0, stamptax: float = 0.001, transferfee: float = 2e-05) -> TradeCostBase:
    """
    TC_FixedA2015([commission=0.0018, lowestCommission=5.0, stamptax=0.001, transferfee=0.00002])
    
        2015年8月1日上证过户费改为成交金额的千分之0.02
    
        :param float commission: 佣金比例
        :param float lowestCommission: 最低佣金值
        :param float stamptax: 印花税
        :param float transferfee: 过户费
        :return: :py:class:`TradeCostBase` 子类实例
    """
def TC_FixedA2017(commission: float = 0.0018, lowest_commission: float = 5.0, stamptax: float = 0.001, transferfee: float = 2e-05) -> TradeCostBase:
    """
    TC_FixedA2015([commission=0.0018, lowestCommission=5.0, stamptax=0.001, transferfee=0.00002])
    
        2017年1月1日起将对深市过户费项目单独列示，标准为成交金额0.02‰双向收取。
    
        :param float commission: 佣金比例
        :param float lowestCommission: 最低佣金值
        :param float stamptax: 印花税
        :param float transferfee: 过户费
        :return: :py:class:`TradeCostBase` 子类实例
    """
def TC_TestStub() -> TradeCostBase:
    """
    仅用于测试
    """
def TC_Zero() -> TradeCostBase:
    """
    零交易成本算法
    """
@typing.overload
def TIME() -> Indicator:
    ...
@typing.overload
def TIME(arg0: KData) -> Indicator:
    """
    TIME([data])
    
        取得该周期的时分秒。用法: TIME 函数返回有效值范围为(000000-235959)。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def TIMELINE() -> Indicator:
    ...
@typing.overload
def TIMELINE(arg0: KData) -> Indicator:
    """
    TIMELINE([k])
    
        分时价格数据
    
        :param KData k: 上下文
        :rtype: Indicator
    """
@typing.overload
def TIMELINEVOL() -> Indicator:
    ...
@typing.overload
def TIMELINEVOL(arg0: KData) -> Indicator:
    """
    TIMELINEVOL([k])
    
        分时成交量数据
    
        :param KData k: 上下文
        :rtype: Indicator
    """
@typing.overload
def TR() -> Indicator:
    ...
@typing.overload
def TR(kdata: KData) -> Indicator:
    """
    TR([kdata])
    
        真实波动幅度(TR)是以下三个值中的最大值:
        1. 当前周期最高价与最低价之差
        2. 当前周期最高价与前一周期收盘价之差的绝对值
        3. 当前周期最低价与前一周期收盘价之差的绝对值
    
        :param KData kdata: K线数据
        :rtype: Indicator
    """
@typing.overload
def TURNOVER(n: int = 1) -> Indicator:
    ...
@typing.overload
def TURNOVER(kdata: KData, n: int = 1) -> Indicator:
    """
    TURNOVER(data[,n=1])
        换手率=股票成交量/流通股股数×100%
    
        :param int n: 时间窗口
    """
@typing.overload
def UPNDAY(data: Indicator, n: int = 3) -> Indicator:
    ...
@typing.overload
def UPNDAY(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def UPNDAY(data: Indicator, n: Indicator) -> Indicator:
    """
    UPNDAY(data[, n=3])
    
        连涨周期数, UPNDAY(CLOSE,M)表示连涨M个周期
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
def UTCOffset() -> TimeDelta:
    """
    UTCOffset()
    
          获取当前系统 UTC 偏移量
    
          :rtype: TimeDelta
    """
@typing.overload
def VAR(n: int = 10) -> Indicator:
    ...
@typing.overload
def VAR(n: IndParam) -> Indicator:
    ...
@typing.overload
def VAR(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def VAR(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def VAR(data: Indicator, n: int = 10) -> Indicator:
    """
    VAR([data, n=10])
    
        估算样本方差, VAR(X,N)为X的N日估算样本方差
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def VARP(n: int = 10) -> Indicator:
    ...
@typing.overload
def VARP(n: IndParam) -> Indicator:
    ...
@typing.overload
def VARP(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def VARP(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def VARP(data: Indicator, n: int = 10) -> Indicator:
    """
    VARP([data, n=10])
    
        总体样本方差, VARP(X,N)为X的N日总体样本方差
    
        :param Indicator data: 输入数据
        :param int n|Indicator|IndParam: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def VIGOR(kdata: KData, n: int = 2) -> Indicator:
    ...
@typing.overload
def VIGOR(n: int = 2) -> Indicator:
    """
    VIGOR([kdata, n=2])
    
        亚历山大.艾尔德力度指数 [BOOK2]_
    
        计算公式：（收盘价今－收盘价昨）＊成交量今
    
        :param KData data: 输入数据
        :param int n: EMA平滑窗口
        :rtype: Indicator
    """
@typing.overload
def WEAVE(arg0: typing.Sequence) -> Indicator:
    ...
@typing.overload
def WEAVE(arg0: Indicator, arg1: Indicator) -> Indicator:
    ...
@typing.overload
def WEAVE(arg0: Indicator, arg1: Indicator, arg2: Indicator) -> Indicator:
    ...
@typing.overload
def WEAVE(arg0: Indicator, arg1: Indicator, arg2: Indicator, arg3: Indicator) -> Indicator:
    ...
@typing.overload
def WEAVE(arg0: Indicator, arg1: Indicator, arg2: Indicator, arg3: Indicator, arg4: Indicator) -> Indicator:
    ...
@typing.overload
def WEAVE(arg0: Indicator, arg1: Indicator, arg2: Indicator, arg3: Indicator, arg4: Indicator, arg5: Indicator) -> Indicator:
    """
    WEAVE(ind1, ind2[, ind3, ind4, ind5, ind6])
    
        将最多6个Indicator的结果组合在一起放在一个Indicator中。如ind = WEAVE(ind1, ind2), 则此时ind包含多个结果，按ind1、ind2的顺序存放。
        
        :param Indicator ind1: 指标1
        :param Indicator ind2: 指标2
        :param Indicator ind3: 指标3, 可省略
        :param Indicator ind4: 指标4, 可省略
        :param Indicator ind5: 指标5, 可省略
        :param Indicator ind6: 指标6, 可省略
        :rtype: Indicator
    """
@typing.overload
def WEEK() -> Indicator:
    ...
@typing.overload
def WEEK(arg0: KData) -> Indicator:
    """
    WEEK([data])
    
        取得该周期的星期数。用法：WEEK 函数返回有效值范围为(0-6)，0表示星期天。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def WINNER() -> Indicator:
    ...
@typing.overload
def WINNER(arg0: Indicator) -> Indicator:
    ...
@typing.overload
def WINNER(arg0: float) -> Indicator:
    """
    WINNER([ind])
        
        获利盘比例
        用法: WINNER(CLOSE)　表示以当前收市价卖出的获利盘比例。
        例如: 返回0.1表示10%获利盘;WINNER(10.5)表示10.5元价格的获利盘比例
        该函数仅对日线分析周期有效，且仅对存在流通盘权息数据的证券有效，对指数、基金等无效。
    """
@typing.overload
def WITHDAY(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHDAY(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHDAY([ind, fill_null])
    
        将指标数据转换到日线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHHALFYEAR(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHHALFYEAR(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHHALFYEAR([ind, fill_null])
    
        将指标数据转换到半年线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHHOUR(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHHOUR(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHHOUR([ind, fill_null])
    
        将指标数据转换到60分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHHOUR2(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHHOUR2(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHHOUR2([ind, fill_null])
    
        将指标数据转换到2小时线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHHOUR4(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHHOUR4(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHHOUR4([ind, fill_null])
    
        将指标数据转换到4小时线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHKTYPE(ktype: str, fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHKTYPE(ind: Indicator, ktype: str, fill_null: bool = False) -> Indicator:
    """
    WITHKTYPE([ind, ktype, fill_null])
    
        将指标数据转换到指定周期
    
        :param Indicator ind: 指标数据
        :param KQuery.KType ktype: 指标周期
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMIN(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMIN(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMIN([ind, fill_null])
    
        将指标数据转换到分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMIN15(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMIN15(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMIN15([ind, fill_null])
    
        将指标数据转换到15分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMIN30(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMIN30(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMIN30([ind, fill_null])
    
        将指标数据转换到30分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMIN5(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMIN5(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMIN5([ind, fill_null])
    
        将指标数据转换到5分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMIN60(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMIN60(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMIN60([ind, fill_null])
    
        将指标数据转换到60分钟线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHMONTH(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHMONTH(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHMONTH([ind, fill_null])
    
        将指标数据转换到月线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHQUARTER(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHQUARTER(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHQUARTER([ind, fill_null])
    
        将指标数据转换到季线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHWEEK(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHWEEK(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHWEEK([ind, fill_null])
    
        将指标数据转换到周线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WITHYEAR(fill_null: bool = False) -> Indicator:
    ...
@typing.overload
def WITHYEAR(ind: Indicator, fill_null: bool = False) -> Indicator:
    """
    WITHYEAR([ind, fill_null])
    
        将指标数据转换到年线
    
        :param Indicator ind: 指标数据
        :param bool fill_null: 是否填充空值
        :rtype: Indicator
    """
@typing.overload
def WMA(n: int = 22) -> Indicator:
    ...
@typing.overload
def WMA(n: IndParam) -> Indicator:
    ...
@typing.overload
def WMA(data: Indicator, n: IndParam) -> Indicator:
    ...
@typing.overload
def WMA(data: Indicator, n: Indicator) -> Indicator:
    ...
@typing.overload
def WMA(data: Indicator, n: int = 22) -> Indicator:
    """
    WMA([data, n=22])
    
        加权移动平均，算法:Yn=(1*X1+2*X2+...+n*Xn)/(1+2+...+n)
    
        :param Indicator data: 输入数据
        :param int|Indicator|IndParam n: 时间窗口
        :rtype: Indicator
    """
@typing.overload
def YEAR() -> Indicator:
    ...
@typing.overload
def YEAR(arg0: KData) -> Indicator:
    """
    YEAR([data])
    
        取得该周期的年份。
    
        :param data: 输入数据 KData
        :rtype: Indicator
    """
@typing.overload
def ZHBOND10(default_val: float = 0.4) -> Indicator:
    ...
@typing.overload
def ZHBOND10(data: DatetimeList, default_val: float = 0.4) -> Indicator:
    ...
@typing.overload
def ZHBOND10(data: KData, default_val: float = 0.4) -> Indicator:
    ...
@typing.overload
def ZHBOND10(data: Indicator, default_val: float = 0.4) -> Indicator:
    """
    ZHBOND10([data, defaut_val])
    
        获取10年期中国国债收益率
    
        :param DatetimeList|KDate|Indicator data: 输入的日期参考，优先使用上下文中的日期
        :param float default_val: 如果输入的日期早于已有国债数据的最早记录，则使用此默认值
    """
@typing.overload
def ZONGGUBEN() -> Indicator:
    ...
@typing.overload
def ZONGGUBEN(arg0: KData) -> Indicator:
    """
    ZONGGUBEN(kdata)
    
       获取总股本（单位：万股）
    
       :param KData kdata: k线数据
       :rtype: Indicator
    """
@typing.overload
def ZSCORE(out_extreme: bool = False, nsigma: float = 3.0, recursive: bool = False) -> Indicator:
    ...
@typing.overload
def ZSCORE(data: Indicator, out_extreme: bool = False, nsigma: float = 3.0, recursive: bool = False) -> Indicator:
    """
    ZSCORE(data[, out_extreme, nsigma, recursive])
    
        对数据进行标准化（归一），可选进行极值排除
    
        注：非窗口滚动，如需窗口滚动的标准化，直接 (x - MA(x, n)) / STDEV(x, n) 即可。
        
        :param Indicator data: 待剔除异常值的数据
        :param bool outExtreme: 指示剔除极值，默认 False
        :param float nsigma: 剔除极值时使用的 nsigma 倍 sigma，默认 3.0
        :param bool recursive: 是否进行递归剔除极值，默认 False
        :rtype: Indicator
    """
def active_device(arg0: str) -> None:
    """
    active_device(active_code: str)
            
        VIP功能授权码激活设备
        
        :param str active_code: 授权码
    """
@typing.overload
def backtest(context: StrategyContext, on_bar: typing.Any, tm: TradeManager, start_date: Datetime, end_date: Datetime = ..., ktype: str = 'DAY', ref_market: str = 'SH', mode: int = 0, support_short: bool = False, sp: SlippageBase = None) -> None:
    ...
@typing.overload
def backtest(on_bar: typing.Any, tm: TradeManager, start_date: Datetime, end_date: Datetime = ..., ktype: str = 'DAY', ref_market: str = 'SH', mode: int = 0, support_short: bool = False, sp: SlippageBase = None) -> None:
    """
    backtest([context], on_bar, tm, start_date, end_date, ktype, ref_market, mode)
    
        事件驱动式回测, 通常直接测试 Strategy 中的主体函数
    
        如果 hikyuu 已经加载数据，可以忽略 context 参数。否则通 Strategy 类似，需要主动传入 context 参数，
        context 中包含需要加载的股票代码、K线类型、K线数量、K线起始日期等信息。
          
        :param StrategyContext context: 策略上下文 ()
        :param func on_bar: 策略主体执行函数, 如: on_bar(stg: Strategy)
        :param TradeManager tm: 策略测试账户
        :param Datetime start_date: 起始日期
        :param Datetime end_date: 结束日期（不包含其本身）
        :param Query.KType ktype: K线类型(按该类型逐 Bar 执行测试)
        :param str ref_market: 所属市场
        :param mode 模式  0: 当前bar收盘价执行买卖操作; 1: 下一bar开盘价执行买卖操作
        :param support_short: 是否支持卖空
        :param Slippage sp: 滑点算法
    """
def batch_calculate_inds(arg0: typing.Sequence, arg1: KData) -> list:
    """
    batch_calculate_inds(inds, kdata) -> list)
        
        并行计算多个指标
        
        :param list inds: 指标列表
        :param KData kdata: K线数据
        :return: 指标计算结果列表
        :rtype: list
    """
def can_upgrade() -> bool:
    ...
def close_ostream_to_python() -> None:
    ...
def close_spend_time() -> None:
    """
    全局关闭 c++ 部分耗时打印
    """
def combinate_ind(inds: typing.Sequence, n: int = 7) -> list:
    """
    combinate_ind(inds[, n=7])
    
        对输入的指标序列进行组合, 如输入为 [ind1, ind2], 输出为 [EXIST(ind1,n), EXIST(ind2,n),
        EXIST(ind1,n)&EXIST(ind2,n)]
    
        :param list|tuple|seq inds: 待组合的指标列表
        :param int n: 指标在 n 周期内存在
        :return: 组合后的指标列表
        :rtype: list
    """
def combinate_index(arg0: typing.Any) -> list:
    """
    combinate_index(seq)
    
        获取序列组合的下标索引, 输入序列的长度最大不超过15，否则抛出异常
    
        :param inds: list 或 tuple 等可使用索引的可迭代对象
        :return: 返回组合的索引，可用于获取输入中相应索引位置的值
        :rtype: list
    """
def crtBrokerTM(broker: OrderBrokerBase, cost_func: TradeCostBase = ..., name: str = 'SYS', other_brokers: list[OrderBrokerBase] = []) -> TradeManager:
    ...
def crtSEOptimal(arg0: typing.Callable) -> SelectorBase:
    """
    crtSEOptimal(func)
        
        快速创建自定义绩效评估函数的寻优选择器
    
        :param func: 一个可调用对象，接收参数为 (sys, lastdate)，返回一个 float 数值
    """
def crtTM(date: Datetime = ..., init_cash: float = 100000, cost_func: TradeCostBase = ..., name: str = 'SYS') -> TradeManager:
    """
    crtTM([date = Datetime(199001010000), init_cash = 100000, cost_func = TC_Zero(), name = "SYS"])
    
        创建交易管理模块，管理帐户的交易记录及资金使用情况
        
        :param Datetime date:  账户建立日期
        :param float init_cash:    初始资金
        :param TradeCost cost_func: 交易成本算法
        :param string name:        账户名称
        :rtype: TradeManager
    """
def crt_pf_strategy(pf: Portfolio, query: Query, broker: OrderBrokerBase, cost_func: TradeCostBase, name: str = 'PFStrategy', other_brokers: list[OrderBrokerBase] = [], config: str = '') -> Strategy:
    ...
def crt_sys_strategy(sys: System, stk_market_code: str, query: Query, broker: OrderBrokerBase, cost_func: TradeCostBase, other_brokers: str = [], name: list[OrderBrokerBase] = 'SYSStrategy', config: str = '') -> Strategy:
    ...
def fetch_trial_license(arg0: str) -> str:
    """
    fetch_trial_license(email: str)
            
        获取试用授权码
        
        :param str email: 邮箱地址
    """
def find_optimal_system(sys_list: list[System], stock: Stock, query: Query, sort_key: str = '', sort_mode: int = 0) -> tuple[float, System]:
    ...
def find_optimal_system_multi(sys_list: list[System], stock: Stock, query: Query, sort_key: str = '', sort_mode: int = 0) -> tuple[float, System]:
    ...
def get_block(arg0: str, arg1: str) -> Block:
    """
    get_block(category: str, name: str)
        
        获取预定义板块
    
        :param str category: 板块分类
        :param str name: 板块名称
        :rtype: Block
    """
def get_business_name(arg0: BUSINESS) -> str:
    """
    get_business_name(business)
    
        :param BUSINESS business: 交易业务类型
        :return: 交易业务类型名称("INIT"|"BUY"|"SELL"|"GIFT"|"BONUS"|"CHECKIN"|"CHECKOUT"|"UNKNOWN"
        :rtype: string
    """
def get_data_from_buffer_server(arg0: str, arg1: list[Stock], arg2: str) -> None:
    """
    get_data_from_buffer_server(addr: str, stklist: list, ktype: Query.KType)
              
        :param str addr: 数据服务器地址
        :param list stklist: 需要获取数据的股票列表
        :param Query.KType ktype: 数据类型
    """
def get_date_range(start: Datetime, end: Datetime) -> DatetimeList:
    """
    get_date_range(start, end)
    
        获取指定 [start, end) 日期时间范围的自然日日历日期列表，仅支持到日
        注意: 如果 end 日期为空，将使用 Datetime 的最大日期，可能会使用过量内存
        
        :param Datetime start: 起始日期
        :param Datetime end: 结束日期
        :rtype: DatetimeList
    """
@typing.overload
def get_kdata(arg0: str, arg1: Query) -> KData:
    ...
@typing.overload
def get_kdata(market_code: str, start: int = 0, end: int = 9223372036854775807, ktype: str = 'DAY', recover_type: Query.RecoverType = ...) -> KData:
    """
    根据证券代码及起止位置获取 [start, end) 范围的 K 线数据
    
        :param str market_code: 证券代码，如: 'sh000001'
        :param int start: 起始索引
        :param int end: 结束索引
        :param Query.KType ktype: K 线类型, 'DAY'|'WEEK'|'MONTH'|'QUARTER'|'HALFYEAR'|'YEAR'|'MIN'|'MIN5'|'MIN15'|'MIN30'|'MIN60'
        :param Query.RecoverType recover_type: 复权类型
    """
@typing.overload
def get_kdata(market_code: str, start: Datetime = ..., end: Datetime = ..., ktype: str = 'DAY', recover_type: Query.RecoverType = ...) -> KData:
    """
    根据证券代码及起止日期获取 [start, end) 范围的 K 线数据
    
        :param str market_code: 证券代码，如: 'sh000001'
        :param int start: 起始日期
        :param int end: 结束日期
        :param Query.KType ktype: K 线类型, 'DAY'|'WEEK'|'MONTH'|'QUARTER'|'HALFYEAR'|'YEAR'|'MIN'|'MIN5'|'MIN15'|'MIN30'|'MIN60'
        :param Query.RecoverType recover_type: 复权类型
    """
def get_last_version() -> str:
    ...
def get_log_level() -> LOG_LEVEL:
    """
    获取当前日志级别
    """
def get_stock(arg0: str) -> Stock:
    """
    get_stock(market_code)
    
            根据"市场简称证券代码"获取对应的证券实例
    
            :param str market_code: 格式：“市场简称证券代码”，如"sh000001"
            :return: 对应的证券实例，如果实例不存在，则返回空实例，即Stock()，不抛出异常
            :rtype: Stock
    """
def get_system_part_enum(arg0: str) -> SystemPart:
    """
    get_system_part_enum(part_name)
    
         根据系统部件的字符串名称获取相应的枚举值
    
        :param str part_name: 系统部件的字符串名称，参见：:py:func:`getSystemPartName`
        :rtype: System.Part
    """
def get_system_part_name(arg0: int) -> str:
    """
    get_system_part_name(part)
    
        获取部件的字符串名称
        
            - System.Part.ENVIRONMENT  - "EV"
            - System.Part.CONDITION    - "CN"
            - System.Part.SIGNAL       - "SG"
            - System.Part.STOPLOSS     - "ST"
            - System.Part.TAKEPROFIT   - "TP"
            - System.Part.MONEYMANAGER - "MM"
            - System.Part.PROFITGOAL   - "PG"
            - System.Part.SLIPPAGE     - "SP"
            - System.Part.INVALID      - "--"
    
        :param int part: System.Part 枚举值
        :rtype: str
    """
def get_version() -> str:
    """
    getVersion()
    
            :return: hikyuu 当前版本
            :rtype: str
    """
def get_version_git() -> str:
    ...
def get_version_with_build() -> str:
    ...
def hikyuu_init(filename: str, ignore_preload: bool = False, context: StrategyContext = ...) -> None:
    ...
def inner_analysis_sys_list(arg0: typing.Any, arg1: Query, arg2: System) -> dict:
    ...
def inner_combinate_ind_analysis(arg0: Stock, arg1: Query, arg2: TradeManager, arg3: System, arg4: typing.Sequence, arg5: typing.Sequence, arg6: int) -> dict:
    ...
def inner_combinate_ind_analysis_with_block(arg0: Block, arg1: Query, arg2: TradeManager, arg3: System, arg4: typing.Sequence, arg5: typing.Sequence, arg6: int) -> dict:
    ...
def is_valid_license() -> bool:
    """
    is_valid_license()
            
        查看当前设备是否授权
    """
def isinf(arg0: float) -> bool:
    """
    是否是无穷大或无穷小
    """
def isnan(arg0: float) -> bool:
    """
    是否为非数字
    """
def open_ostream_to_python() -> None:
    ...
def open_spend_time() -> None:
    """
    全局开启 c++ 部分耗时打印
    """
def remove_license() -> None:
    """
    remove_license()
            
        移除当前授权
    """
@typing.overload
def roundDown(number: float, ndigits: int = 0) -> float:
    ...
@typing.overload
def roundDown(number: float, ndigits: int = 0) -> float:
    """
    roundDown(number[, ndigits=0])
    
        向下截取，如10.1截取后为10
    
        :param float number  待处理数据
        :param int ndigits 保留小数位数
        :rtype: float
    """
@typing.overload
def roundEx(number: float, ndigits: int = 0) -> float:
    ...
@typing.overload
def roundEx(number: float, ndigits: int = 0) -> float:
    """
    roundEx(number[, ndigits=0])
    
        四舍五入，ROUND_HALF_EVEN 银行家舍入法
    
        :param float number  待四舍五入的数据
        :param int ndigits 保留小数位数
        :rype: float
    """
@typing.overload
def roundUp(number: float, ndigits: int = 0) -> float:
    ...
@typing.overload
def roundUp(number: float, ndigits: int = 0) -> float:
    """
    roundUp(number[, ndigits=0])
    
        向上截取，如10.1截取后为11
    
        :param float number  待处理数据
        :param int ndigits 保留小数位数
        :rtype: float
    """
@typing.overload
def run_in_strategy(sys: System, stock: Stock, query: Query, broker: OrderBrokerBase, cost_func: TradeCostBase, other_brokers: list[OrderBrokerBase] = []) -> None:
    """
    run_in_strategy(sys, stk, query, broker, costfunc, [other_brokers=[]])
              
        在策略运行时中执行系统交易 SYS
        目前仅支持 buy_delay| sell_delay 均为 false 的系统，即 close 时执行交易
     
        :param sys: 交易系统
        :param stk: 交易对象
        :param query: 查询条件
        :param broker: 订单代理（专用与和账户资产同步的订单代理）
        :param costfunc: 成本函数
        :param other_brokers: 其他的订单代理
    """
@typing.overload
def run_in_strategy(pf: Portfolio, query: Query, broker: OrderBrokerBase, cost_func: TradeCostBase, other_brokers: list[OrderBrokerBase] = []) -> None:
    """
    run_in_strategy(pf, query, adjust_cycle, broker, costfunc, [other_brokers=[]])
              
        在策略运行时中执行组合策略 PF
        目前仅支持 buy_delay| sell_delay 均为 false 的系统，即 close 时执行交易
    
        :param Portfolio pf: 资产组合
        :param Query query: 查询条件
        :param broker: 订单代理（专用与和账户资产同步的订单代理）
        :param costfunc: 成本函数
        :param other_brokers: 其他的订单代理
    """
def set_log_level(arg0: LOG_LEVEL) -> None:
    """
    设置当前日志级别
    """
def set_python_in_interactive(arg0: bool) -> None:
    ...
def set_python_in_jupyter(arg0: bool) -> None:
    ...
def start_data_server(addr: str = 'tcp://0.0.0.0:9201', work_num: int = 2) -> None:
    """
    start_data_server(addr: str[, work_num: int=2])
            
        启动数据缓存服务
        
        :param str addr: 服务器地址
        :param int work_num: 工作线程数
        :return: None
    """
def start_spot_agent(print: bool = False, worker_num: int = 1, addr: str = '') -> None:
    ...
def stop_data_server() -> None:
    """
    stop_data_server()
            
        停止数据缓存服务
    """
def stop_spot_agent() -> None:
    ...
def toPriceList(arg0: typing.Sequence) -> list[float]:
    """
    将 python list/tuple/np.arry 对象转化为 PriceList 对象
    """
def view_license() -> str:
    """
    view_license()
            
        查看设备授权信息
    """
DEBUG: LOG_LEVEL  # value = <LOG_LEVEL.DEBUG: 1>
ERROR: LOG_LEVEL  # value = <LOG_LEVEL.ERROR: 4>
FATAL: LOG_LEVEL  # value = <LOG_LEVEL.FATAL: 5>
INFO: LOG_LEVEL  # value = <LOG_LEVEL.INFO: 2>
OFF: LOG_LEVEL  # value = <LOG_LEVEL.OFF: 6>
TRACE: LOG_LEVEL  # value = <LOG_LEVEL.TRACE: 0>
WARN: LOG_LEVEL  # value = <LOG_LEVEL.WARN: 3>
constant: Constant  # value = <hikyuu.cpp.core313.Constant object>
