# Hikyuu 量化交易策略开发图形界面

一个现代化的、用户友好的Hikyuu图形界面，专注于策略开发、回测和分析。

## 🎯 特性

- **可视化策略开发**: 直观的代码编辑器和策略模板
- **实时回测分析**: 一键运行回测，查看详细结果
- **图表展示功能**: K线图、资金曲线、收益率图表
- **策略向导**: 帮助快速生成策略框架
- **便携化设计**: 不修改原Hikyuu项目文件，独立运行

## 🚀 快速开始

### Windows 用户

1. **双击运行启动脚本**:
   ```
   start_hikyuu_gui.bat
   ```

2. **或手动启动**:
   ```bash
   python hikyuu_gui.py
   ```

### Linux/macOS 用户

1. **运行启动脚本**:
   ```bash
   ./start_hikyuu_gui.sh
   ```

2. **或手动启动**:
   ```bash
   python3 hikyuu_gui.py
   ```

## 📋 系统要求

- **Python**: 3.9+ (推荐 3.11 或 3.12)
- **操作系统**: Windows 7+, Ubuntu, macOS
- **必要依赖**:
  - hikyuu
  - matplotlib
  - pandas
  - numpy
  - tkinter (通常随Python安装)

## 🛠️ 安装依赖

如果启动脚本无法自动安装依赖，请手动安装：

```bash
# 安装Hikyuu
pip install hikyuu

# 安装其他依赖
pip install matplotlib pandas numpy
```

## 📖 使用说明

### 1. 股票选择
- 在左侧"股票选择"区域输入股票代码（如：sz000001）
- 点击"加载股票"按钮加载股票数据和K线图

### 2. 策略编辑
- **选择模板**: 从下拉菜单选择预设的策略模板
- **编写代码**: 在代码编辑器中编写或修改策略
- **清空代码**: 点击"清空"按钮清空编辑器

### 3. 运行回测
- 点击"运行回测"按钮执行策略
- 在"回测结果"选项卡查看详细统计信息
- 在"K线图表"选项卡查看策略表现图表

### 4. 菜单功能

#### 文件菜单
- **新建策略**: 清空编辑器，开始新策略
- **打开策略**: 从文件加载已保存的策略
- **保存策略**: 将当前策略保存到文件

#### 数据菜单
- **数据管理**: 启动Hikyuu数据管理工具
- **更新数据**: 更新股票数据（开发中）

#### 工具菜单
- **策略向导**: 通过向导快速生成策略框架
- **指标计算器**: 技术指标计算工具（开发中）

#### 帮助菜单
- **使用说明**: 查看详细使用说明
- **关于**: 查看软件信息

## 📝 策略模板

### 双均线策略
```python
# 双均线策略示例
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 创建信号指示器（5日线上穿20日线买入，下穿卖出）
my_sg = SG_Cross(MA(n=5), MA(n=20))

# 资金管理（固定每次买入1000股）
my_mm = MM_FixedCount(1000)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
```

### MACD策略
```python
# MACD策略示例
from hikyuu.interactive import *

# 创建交易账户
my_tm = crtTM(init_cash=100000)

# 创建MACD指标
macd = MACD()
diff = macd.getResult(0)  # DIFF线
dea = macd.getResult(1)   # DEA线

# 创建信号指示器（DIFF上穿DEA买入，下穿卖出）
my_sg = SG_Cross(diff, dea)

# 资金管理
my_mm = MM_FixedCount(1000)

# 创建交易系统
sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)

# 运行回测
sys.run(stock, Query(-250))
```

## 🔧 自定义开发

### 添加新的策略模板
在 `hikyuu_gui.py` 的 `load_template` 方法中添加新模板：

```python
templates = {
    "你的策略名称": '''
# 你的策略代码
from hikyuu.interactive import *
# ...
''',
    # 其他模板...
}
```

### 扩展功能
- 修改 `hikyuu_gui.py` 添加新功能
- 所有修改都在GUI文件中，不影响原Hikyuu项目

## 🐛 常见问题

### Q: 启动时提示"Hikyuu 加载失败"
**A**: 请确保已正确安装Hikyuu：
```bash
pip install hikyuu
```

### Q: 图表显示中文乱码
**A**: 程序会自动设置中文字体，如仍有问题，请安装中文字体。

### Q: 回测时提示"未找到交易系统对象 'sys'"
**A**: 确保策略代码中包含名为 `sys` 的交易系统对象。

### Q: 股票代码无效
**A**: 请使用正确的股票代码格式，如：
- 深圳股票：sz000001
- 上海股票：sh600000

## 📞 支持

- **Hikyuu官网**: https://hikyuu.org/
- **官方文档**: https://hikyuu.readthedocs.io/
- **GitHub**: https://github.com/fasiondog/hikyuu

## 📄 许可证

本图形界面基于MIT许可证开源，Hikyuu框架遵循其原有许可证。

---

**享受量化交易的乐趣！** 🎉
