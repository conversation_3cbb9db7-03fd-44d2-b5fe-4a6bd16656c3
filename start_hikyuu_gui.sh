#!/bin/bash

# Hikyuu 量化交易策略开发平台启动脚本 (Linux/macOS)

echo ""
echo "========================================"
echo "  Hikyuu 量化交易策略开发平台"
echo "========================================"
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.9+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo "✅ Python3 已安装"
echo ""

# 检查必要的依赖包
echo "📦 检查依赖包..."
python3 -c "import tkinter, matplotlib, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  缺少必要的依赖包，正在安装..."
    pip3 install matplotlib pandas numpy
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败，请手动安装:"
        echo "pip3 install matplotlib pandas numpy"
        exit 1
    fi
fi

echo "✅ 依赖包检查完成"
echo ""

# 检查Hikyuu是否安装
echo "🔍 检查Hikyuu框架..."
python3 -c "import hikyuu" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  未找到Hikyuu框架，正在安装..."
    pip3 install hikyuu
    if [ $? -ne 0 ]; then
        echo "❌ Hikyuu安装失败，请手动安装:"
        echo "pip3 install hikyuu"
        exit 1
    fi
fi

echo "✅ Hikyuu框架检查完成"
echo ""

# 启动GUI
echo "🚀 启动Hikyuu量化交易平台..."
echo ""
python3 hikyuu_gui.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 程序运行出错"
    read -p "按回车键退出..."
fi
