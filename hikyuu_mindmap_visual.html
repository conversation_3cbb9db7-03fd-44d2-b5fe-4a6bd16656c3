<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hikyuu 2.6.5 量化交易框架思维导图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .mindmap-container {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: auto;
        }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .info {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Hikyuu 2.6.5 量化交易框架思维导图</h1>
        
        <div class="info">
            <strong>📋 使用说明：</strong>
            <ul>
                <li>🔍 可以滚动查看完整的思维导图</li>
                <li>📱 支持移动端和桌面端查看</li>
                <li>🎯 点击节点可以展开/收起子节点</li>
                <li>💾 可以右键保存图片</li>
            </ul>
        </div>

        <div class="controls">
            <button class="btn" onclick="toggleFullscreen()">🔍 全屏查看</button>
            <button class="btn" onclick="resetZoom()">🔄 重置视图</button>
            <button class="btn" onclick="downloadImage()">💾 下载图片</button>
        </div>

        <div class="mindmap-container" id="mindmap">
            <div class="mermaid">
mindmap
  root((Hikyuu 2.6.5<br/>量化交易框架))
    
    数据管理层
      StockManager股票管理器
        sm['sz000001'] 获取股票
        get_stock_list() 股票列表
        __len__() 股票总数
      Stock股票对象
        属性
          name 股票名称
          code 股票代码
          market 市场代码
        方法
          get_kdata(Query) K线数据
          get_finance_data() 财务数据
      KData数据容器
        Query(-100) 最近100天
        KType.DAY 日线
        KType.MIN5 5分钟线
      数据提取
        CLOSE(kdata) 收盘价
        HIGH(kdata) 最高价
        LOW(kdata) 最低价
        VOL(kdata) 成交量
    
    技术指标层
      趋势指标
        MA(data, n) 移动平均
        EMA(data, n) 指数平均
        MACD(data) MACD指标
        ADX(kdata, n) 趋向指数
      震荡指标
        RSI(data, n) 相对强弱
        KDJ(kdata) 随机指标
        CCI(kdata, n) 顺势指标
        WR(kdata, n) 威廉指标
      成交量指标
        OBV(kdata) 能量潮
        AD(kdata) 聚散指标
      价格指标
        BOLL(data, n, p) 布林带
        SAR(kdata) 抛物线
        ATR(kdata, n) 真实波幅
    
    交易系统核心
      信号指示器SG
        SG_Cross(fast, slow) 双线交叉
        SG_Band(ind, low, high) 区间突破
        SG_Bool(condition) 布尔条件
        信号运算
          signal1 & signal2 AND
          signal1 | signal2 OR
      交易管理器TM
        crtTM(init_cash) 创建账户
        buy(datetime, stock, price, number) 买入
        sell(datetime, stock, price, number) 卖出
        get_trade_list() 交易记录
        get_position_list() 持仓记录
      资金管理器MM
        MM_FixedCount(count) 固定股数
        MM_FixedPercent(percent) 固定比例
        MM_FixedCash(cash) 固定金额
      交易系统SYS
        SYS_Simple(tm, sg, mm) 简单系统
        run(stock, query) 运行回测
    
    风险控制层
      止损策略ST
        ST_FixedPercent(percent) 固定百分比止损
      止盈策略TP
        TP_FixedPercent(percent) 固定百分比止盈
      市场环境EV
        EV_Bool(condition) 环境判断
      系统条件CN
        CN_Bool(condition) 条件判断
      滑点算法SP
        SP_FixedPercent(percent) 滑点控制
    
    高级策略层
      多因子模型MF
        MF_MultiFactor() 多因子模型
        add_factor(name, factor) 添加因子
      选股器SE
        SE_Fixed(stocks, sys) 固定选股
        SE_MultiFactor(mf, topn) 多因子选股
      投资组合PF
        PF_Simple(tm, af, se) 简单组合
      资金分配器AF
        AF_EqualWeight() 等权重分配
    
    工具扩展层
      日期时间
        Datetime(year, month, day) 日期对象
        DatetimeList() 日期列表
      数据转换
        hku_save(obj, file) 保存对象
        hku_load(file) 加载对象
      系统配置
        load_hikyuu() 初始化
        get_global_context() 全局上下文
      常量枚举
        BUSINESS.BUY=1 买入
        BUSINESS.SELL=2 卖出
        SystemPart.SG 信号部件
            </div>
        </div>

        <div class="info">
            <strong>🎯 思维导图说明：</strong>
            <ul>
                <li><strong>数据管理层</strong>：股票数据获取和处理的基础</li>
                <li><strong>技术指标层</strong>：各种技术分析指标的计算</li>
                <li><strong>交易系统核心</strong>：信号生成、交易执行、资金管理</li>
                <li><strong>风险控制层</strong>：止损止盈、环境判断等风险管理</li>
                <li><strong>高级策略层</strong>：多因子模型、选股、投资组合</li>
                <li><strong>工具扩展层</strong>：辅助工具和系统配置</li>
            </ul>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            mindmap: {
                padding: 20,
                maxNodeWidth: 200
            }
        });

        // 全屏功能
        function toggleFullscreen() {
            const container = document.getElementById('mindmap');
            if (!document.fullscreenElement) {
                container.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 重置视图
        function resetZoom() {
            location.reload();
        }

        // 下载图片功能
        function downloadImage() {
            const svg = document.querySelector('#mindmap svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    const link = document.createElement('a');
                    link.download = 'hikyuu_mindmap.png';
                    link.href = canvas.toDataURL();
                    link.click();
                };
                
                img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
            }
        }

        // 响应式调整
        window.addEventListener('resize', function() {
            // 可以在这里添加响应式调整逻辑
        });
    </script>
</body>
</html>
