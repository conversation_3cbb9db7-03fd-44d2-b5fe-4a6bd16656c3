#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Hikyuu思维导图生成器
避免字体问题，生成清晰的思维导图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle
import numpy as np

# 设置字体，避免中文显示问题
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_mindmap():
    """创建简化版思维导图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    ax.set_facecolor('white')
    
    # 定义颜色方案
    colors = {
        'root': '#2E86AB',
        'data': '#A23B72', 
        'indicator': '#F18F01',
        'trading': '#C73E1D',
        'risk': '#8E44AD',
        'advanced': '#27AE60',
        'tools': '#34495E'
    }
    
    # 根节点
    root_rect = FancyBboxPatch((6, 5), 4, 2, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['root'], 
                              edgecolor='white', 
                              linewidth=3)
    ax.add_patch(root_rect)
    ax.text(8, 6, 'Hikyuu 2.6.5\nQuantitative Trading', 
            ha='center', va='center', fontsize=14, fontweight='bold', color='white')
    
    # 六个主要模块
    modules = [
        # (x, y, width, height, color, title, items)
        (0.5, 9, 3, 2.5, colors['data'], 'Data Management', [
            'StockManager', 'Stock Object', 'KData', 'Query'
        ]),
        (4.5, 9, 3, 2.5, colors['indicator'], 'Technical Indicators', [
            'MA/EMA/SMA', 'RSI/KDJ/CCI', 'MACD/BOLL', 'OBV/ATR'
        ]),
        (8.5, 9, 3, 2.5, colors['trading'], 'Trading System', [
            'Signal (SG)', 'TradeManager', 'MoneyManager', 'System'
        ]),
        (12.5, 9, 3, 2.5, colors['risk'], 'Risk Control', [
            'Stoploss (ST)', 'TakeProfit (TP)', 'Environment', 'Slippage'
        ]),
        (0.5, 0.5, 3, 2.5, colors['advanced'], 'Advanced Strategy', [
            'MultiFactor', 'Selector', 'Portfolio', 'AllocateFunds'
        ]),
        (4.5, 0.5, 3, 2.5, colors['tools'], 'Tools & Utils', [
            'Datetime', 'Serialization', 'Configuration', 'Constants'
        ])
    ]
    
    # 绘制模块
    for x, y, w, h, color, title, items in modules:
        # 主模块框
        module_rect = FancyBboxPatch((x, y), w, h,
                                    boxstyle="round,pad=0.05",
                                    facecolor=color,
                                    edgecolor='white',
                                    linewidth=2,
                                    alpha=0.9)
        ax.add_patch(module_rect)
        
        # 模块标题
        ax.text(x + w/2, y + h - 0.2, title, 
                ha='center', va='center', fontsize=11, fontweight='bold', color='white')
        
        # 模块项目
        for i, item in enumerate(items):
            ax.text(x + w/2, y + h - 0.6 - i*0.35, f'• {item}', 
                    ha='center', va='center', fontsize=9, color='white')
    
    # 绘制连接线
    connections = [
        # 从根节点到各模块
        ((8, 7), (2, 9)),      # 到数据管理
        ((8, 7), (6, 9)),      # 到技术指标
        ((8, 7), (10, 9)),     # 到交易系统
        ((8, 7), (14, 9)),     # 到风险控制
        ((8, 5), (2, 3)),      # 到高级策略
        ((8, 5), (6, 3)),      # 到工具
    ]
    
    for start, end in connections:
        ax.plot([start[0], end[0]], [start[1], end[1]], 
                'k-', linewidth=2, alpha=0.6)
    
    # 添加使用流程
    flow_rect = FancyBboxPatch((9, 0.5), 6.5, 2.5,
                              boxstyle="round,pad=0.05",
                              facecolor='#ECF0F1',
                              edgecolor='#BDC3C7',
                              linewidth=1)
    ax.add_patch(flow_rect)
    
    ax.text(12.25, 2.7, 'Core Usage Flow', ha='center', va='center', 
            fontsize=11, fontweight='bold', color='#2C3E50')
    
    flow_steps = [
        '1. Get Data: sm["sz000001"].get_kdata()',
        '2. Calculate: MA(CLOSE(kdata), 5)',
        '3. Signal: SG_Cross(ma5, ma20)',
        '4. System: SYS_Simple(tm, sg, mm)',
        '5. Run: system.run(stock, query)',
        '6. Analyze: tm.get_trade_list()'
    ]
    
    for i, step in enumerate(flow_steps):
        ax.text(9.1, 2.4 - i*0.25, step, ha='left', va='center', 
                fontsize=8, color='#2C3E50')
    
    # 添加标题
    ax.text(8, 11.5, 'Hikyuu Quantitative Trading Framework Mind Map', 
            ha='center', va='center', fontsize=16, fontweight='bold', color='#2C3E50')
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('hikyuu_simple_mindmap.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('hikyuu_simple_mindmap.jpg', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("✅ 简化思维导图已生成:")
    print("   📄 hikyuu_simple_mindmap.png")
    print("   📄 hikyuu_simple_mindmap.jpg")
    
    # 不显示图片，避免阻塞
    plt.close()
    
    return True

def create_api_reference_chart():
    """创建API参考图表"""
    
    fig, ax = plt.subplots(1, 1, figsize=(18, 14))
    ax.set_xlim(0, 18)
    ax.set_ylim(0, 14)
    ax.axis('off')
    ax.set_facecolor('white')
    
    # 标题
    ax.text(9, 13.5, 'Hikyuu API Reference Chart', 
            ha='center', va='center', fontsize=18, fontweight='bold', color='#2C3E50')
    
    # API分类
    api_sections = [
        # (x, y, width, height, color, title, apis)
        (0.5, 10, 8, 3, '#3498DB', 'Core Data Types & Methods', [
            'sm = StockManager.instance()',
            'stock = sm["sz000001"]  # Get stock object',
            'kdata = stock.get_kdata(Query(-100))  # Get K-data',
            'close = CLOSE(kdata)  # Extract close prices',
            'ma5 = MA(close, 5)  # 5-day moving average',
            'rsi = RSI(close, 14)  # RSI indicator',
            'macd = MACD(close)  # MACD indicator'
        ]),
        (9.5, 10, 8, 3, '#E74C3C', 'Trading System Components', [
            'sg = SG_Cross(ma5, ma20)  # Cross signal',
            'tm = crtTM(init_cash=100000)  # Trade manager',
            'mm = MM_FixedCount(1000)  # Money manager',
            'st = ST_FixedPercent(0.05)  # Stop loss 5%',
            'tp = TP_FixedPercent(0.10)  # Take profit 10%',
            'sys = SYS_Simple(tm, sg, mm, st=st, tp=tp)',
            'sys.run(stock, Query(-250))  # Run backtest'
        ]),
        (0.5, 6, 8, 3, '#F39C12', 'Signal Types & Operations', [
            'SG_Cross(fast, slow)  # Moving average cross',
            'SG_Band(rsi, 30, 70)  # RSI band signal',
            'SG_Bool(condition)  # Boolean condition',
            'signal1 & signal2  # AND operation',
            'signal1 | signal2  # OR operation',
            'SG_Single(ma5 > ma20)  # Single condition',
            'SG_Manual()  # Manual signal input'
        ]),
        (9.5, 6, 8, 3, '#9B59B6', 'Analysis & Results', [
            'trades = tm.get_trade_list()  # Get trades',
            'positions = tm.get_position_list()  # Positions',
            'return_rate = (tm.current_cash - tm.init_cash)',
            '              / tm.init_cash * 100',
            'profit_trades = [t for t in trades if ...]',
            'win_rate = len(profit_trades) / len(trades)',
            'max_drawdown = calculate_max_drawdown()'
        ]),
        (0.5, 2, 17, 3, '#27AE60', 'Complete Example Code', [
            '# Complete trading strategy example',
            'stock = sm["sz000001"]',
            'kdata = stock.get_kdata(Query(-250))',
            'close = CLOSE(kdata)',
            'ma5, ma20 = MA(close, 5), MA(close, 20)',
            'rsi = RSI(close, 14)',
            'sg_ma = SG_Cross(ma5, ma20)',
            'sg_rsi = SG_Band(rsi, 30, 70)',
            'sg_combined = sg_ma & sg_rsi',
            'tm = crtTM(init_cash=100000)',
            'mm = MM_FixedCount(1000)',
            'sys = SYS_Simple(tm=tm, sg=sg_combined, mm=mm)',
            'sys.run(stock, Query(-250))',
            'print(f"Return: {(tm.current_cash-tm.init_cash)/tm.init_cash*100:.2f}%")'
        ])
    ]
    
    # 绘制API部分
    for x, y, w, h, color, title, apis in api_sections:
        # 部分框
        section_rect = FancyBboxPatch((x, y), w, h,
                                     boxstyle="round,pad=0.05",
                                     facecolor=color,
                                     edgecolor='white',
                                     linewidth=2,
                                     alpha=0.9)
        ax.add_patch(section_rect)
        
        # 标题
        ax.text(x + w/2, y + h - 0.15, title, 
                ha='center', va='center', fontsize=12, fontweight='bold', color='white')
        
        # API列表
        for i, api in enumerate(apis):
            ax.text(x + 0.1, y + h - 0.4 - i*0.25, api, 
                    ha='left', va='center', fontsize=8, color='white',
                    fontfamily='monospace')
    
    plt.tight_layout()
    
    # 保存API参考图
    plt.savefig('hikyuu_api_reference.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('hikyuu_api_reference.jpg', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("✅ API参考图已生成:")
    print("   📄 hikyuu_api_reference.png")
    print("   📄 hikyuu_api_reference.jpg")
    
    plt.close()
    
    return True

if __name__ == "__main__":
    print("🚀 开始生成Hikyuu思维导图图片...")
    
    try:
        # 生成简化思维导图
        create_simple_mindmap()
        
        # 生成API参考图
        create_api_reference_chart()
        
        print("\n🎉 所有图片生成完成！")
        print("📁 生成的文件:")
        print("   🖼️ hikyuu_simple_mindmap.png - 简化思维导图")
        print("   🖼️ hikyuu_simple_mindmap.jpg - 简化思维导图(JPG)")
        print("   🖼️ hikyuu_api_reference.png - API参考图")
        print("   🖼️ hikyuu_api_reference.jpg - API参考图(JPG)")
        print("\n💡 提示: 图片已保存在当前目录，可以直接打开查看！")
        
    except Exception as e:
        print(f"❌ 生成图片时出错: {e}")
        print("💡 请确保已安装matplotlib: pip install matplotlib")
