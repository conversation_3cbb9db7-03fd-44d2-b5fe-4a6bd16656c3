{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-20 15:45:43,093 [INFO] hikyuu version: 2.1.1_202408182226_RELEASE_windows_x64 [<module>] (D:\\workspace\\hikyuu\\hikyuu\\__init__.py:97) [hikyuu::hku_info]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-08-20 15:45:43.596 [HKU-I] - Using MYSQL BaseInfoDriver (BaseInfoDriver.cpp:58)\n", "2024-08-20 15:45:43.615 [HKU-I] - Loading market information... (StockManager.cpp:481)\n", "2024-08-20 15:45:43.621 [HKU-I] - Loading stock type information... (StockManager.cpp:494)\n", "2024-08-20 15:45:43.628 [HKU-I] - Loading stock information... (StockManager.cpp:409)\n", "2024-08-20 15:45:43.785 [HKU-I] - Loading stock weight... (StockManager.cpp:511)\n", "2024-08-20 15:45:45.041 [HKU-I] - Loading KData... (StockManager.cpp:134)\n", "2024-08-20 15:45:46.483 [HKU-I] - Preloading all day kdata to buffer! (StockManager.cpp:179)\n", "2024-08-20 15:45:46.483 [HKU-I] - Preloading all week kdata to buffer! (StockManager.cpp:179)\n", "2024-08-20 15:45:46.484 [HKU-I] - Preloading all month kdata to buffer! (StockManager.cpp:179)\n", "2024-08-20 15:45:46.552 [HKU-I] - 1.51s Loaded Data. (StockManager.cpp:159)\n", "CPU times: total: 516 ms\n", "Wall time: 3.78 s\n"]}], "source": ["%matplotlib inline\n", "%time from hikyuu.interactive import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["k = get_kdata('sh000001', -100)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\"temp\", 'wb') as f:\n", "    pickle.dump(k, f)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hku_save(k, \"temp\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["k2 = hku_load(\"temp\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["k2.plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}